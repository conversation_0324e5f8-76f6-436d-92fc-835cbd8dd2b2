{"total_scans": 10, "purpose": "Local testing of advanced ordinal classification", "scans": [{"case_id": "DEMO_01_CN_MMSE28_Age72", "class_label": "CN", "mmse_score": 28, "age": 72, "nifti_file": "DEMO_01_CN_MMSE28_Age72.nii.gz", "numpy_file": "DEMO_01_CN_MMSE28_Age72.npy", "expected_classification": "CN"}, {"case_id": "DEMO_02_CN_MMSE29_Age68", "class_label": "CN", "mmse_score": 29, "age": 68, "nifti_file": "DEMO_02_CN_MMSE29_Age68.nii.gz", "numpy_file": "DEMO_02_CN_MMSE29_Age68.npy", "expected_classification": "CN"}, {"case_id": "DEMO_03_CN_MMSE27_Age75", "class_label": "CN", "mmse_score": 27, "age": 75, "nifti_file": "DEMO_03_CN_MMSE27_Age75.nii.gz", "numpy_file": "DEMO_03_CN_MMSE27_Age75.npy", "expected_classification": "CN"}, {"case_id": "DEMO_04_MCI_MMSE22_Age78", "class_label": "MCI", "mmse_score": 22, "age": 78, "nifti_file": "DEMO_04_MCI_MMSE22_Age78.nii.gz", "numpy_file": "DEMO_04_MCI_MMSE22_Age78.npy", "expected_classification": "MCI"}, {"case_id": "DEMO_05_MCI_MMSE20_Age81", "class_label": "MCI", "mmse_score": 20, "age": 81, "nifti_file": "DEMO_05_MCI_MMSE20_Age81.nii.gz", "numpy_file": "DEMO_05_MCI_MMSE20_Age81.npy", "expected_classification": "MCI"}, {"case_id": "DEMO_06_MCI_MMSE24_Age76", "class_label": "MCI", "mmse_score": 24, "age": 76, "nifti_file": "DEMO_06_MCI_MMSE24_Age76.nii.gz", "numpy_file": "DEMO_06_MCI_MMSE24_Age76.npy", "expected_classification": "MCI"}, {"case_id": "DEMO_07_MCI_MMSE19_Age83", "class_label": "MCI", "mmse_score": 19, "age": 83, "nifti_file": "DEMO_07_MCI_MMSE19_Age83.nii.gz", "numpy_file": "DEMO_07_MCI_MMSE19_Age83.npy", "expected_classification": "MCI"}, {"case_id": "DEMO_08_AD_MMSE14_Age85", "class_label": "AD", "mmse_score": 14, "age": 85, "nifti_file": "DEMO_08_AD_MMSE14_Age85.nii.gz", "numpy_file": "DEMO_08_AD_MMSE14_Age85.npy", "expected_classification": "AD"}, {"case_id": "DEMO_09_AD_MMSE11_Age88", "class_label": "AD", "mmse_score": 11, "age": 88, "nifti_file": "DEMO_09_AD_MMSE11_Age88.nii.gz", "numpy_file": "DEMO_09_AD_MMSE11_Age88.npy", "expected_classification": "AD"}, {"case_id": "DEMO_10_AD_MMSE16_Age82", "class_label": "AD", "mmse_score": 16, "age": 82, "nifti_file": "DEMO_10_AD_MMSE16_Age82.nii.gz", "numpy_file": "DEMO_10_AD_MMSE16_Age82.npy", "expected_classification": "AD"}]}