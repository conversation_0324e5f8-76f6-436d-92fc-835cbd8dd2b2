# Local Demo MRI Collection

## Overview
- **Total Scans**: 10
- **Purpose**: Testing advanced ordinal classification
- **Classes**: CN (3), MCI (4), AD (3)

## Test Cases
- `DEMO_01_CN_MMSE28_Age72` - <PERSON><PERSON> case, <PERSON><PERSON><PERSON> 28
- `DEMO_02_CN_MMSE29_Age68` - <PERSON><PERSON> case, M<PERSON><PERSON> 29
- `DEMO_03_CN_MMSE27_Age75` - CN case, MMSE 27
- `DEMO_04_MCI_MMSE22_Age78` - MCI case, MMSE 22
- `DEMO_05_MCI_MMSE20_Age81` - MCI case, MMSE 20
- `DEMO_06_MCI_MMSE24_Age76` - MCI case, M<PERSON><PERSON> 24
- `DEMO_07_MCI_MMSE19_Age83` - MCI case, MMSE 19
- `DEMO_08_AD_MMSE14_Age85` - AD case, MMSE 14
- `DEMO_09_AD_MMSE11_Age88` - AD case, MMSE 11
- `DEMO_10_AD_MMSE16_Age82` - AD case, MMSE 16

## Usage
Load any scan and test with the advanced ordinal classification model.
Expected: Proper MMSE distribution, no clustering around 20-22.

## Files
- 10 NIfTI files (.nii.gz)
- 10 NumPy files (.npy)
- collection_metadata.json
- README.md
