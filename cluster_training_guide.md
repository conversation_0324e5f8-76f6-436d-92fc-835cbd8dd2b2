# Enhanced MCI Model Training Guide

## Current Data Structure Analysis

Based on the codebase analysis, here's what I found about your data organization:

### 📁 **Data Organization Structure**

```
ncomms2022/
├── lookupcsv/                    # CSV files with data paths and labels
│   ├── CrossValid/              # Cross-validation splits
│   ├── dataset_table/           # Individual dataset tables
│   │   ├── ADNI1, ADNI2, ADNI3, ADNIGO/
│   │   ├── AIBL/
│   │   ├── FHS/
│   │   ├── NACC_ALL/
│   │   ├── NIFD/
│   │   ├── OASIS/
│   │   ├── PPMI/
│   │   └── Stanford/
│   ├── derived_tables/          # Processed dataset tables
│   └── raw_tables/              # Raw dataset tables
├── checkpoint_dir/              # Trained model checkpoints
└── tb_log/                     # TensorBoard logs
```

### 🔍 **Key Findings from Code Analysis**

1. **Data Loading Pattern**: 
   - CSV files contain columns: `path`, `filename`, `ADD`, `COG`
   - Data paths are constructed as: `row['path'] + row['filename']`
   - Files are expected to be `.npy` format (converted from `.nii`)

2. **Supported Datasets**:
   - ADNI (Alzheimer's Disease Neuroimaging Initiative)
   - AIBL (Australian Imaging, Biomarker & Lifestyle)
   - FHS (Framingham Heart Study)
   - NACC (National Alzheimer's Coordinating Center)
   - NIFD, OASIS, PPMI, Stanford

3. **Training Configuration**:
   - Cross-validation: 5-fold
   - Tasks: `ADD` (classification), `COG` (regression)
   - Model checkpoints saved in `checkpoint_dir/`

## 🚀 **Setting Up Enhanced MCI Training**

### Step 1: Prepare Enhanced Training Script

```python
# enhanced_training_script.py
import os
import sys
import torch
from pathlib import Path

# Add ncomms2022 to path
sys.path.append('ncomms2022')

from model_wrappers import Multask_Wrapper
from utils import read_json
from enhanced_ncomms2022_model import EnhancedNCOMMs2022Model

def train_enhanced_mci_model():
    """Train enhanced model with MCI support"""
    
    # Enhanced configuration
    main_config = {
        'csv_dir': 'ncomms2022/lookupcsv/CrossValid/',
        'model_name': 'Enhanced_MCI_CNN'
    }
    
    # Load enhanced task configuration
    task_config = read_json('enhanced_task_config.json')
    
    # Enhanced tasks including MCI
    tasks = ['ATROPHY_CONTINUOUS', 'DEMENTIA_CLASSIFICATION', 'COG_SCORE', 
             'REGIONAL_ATROPHY', 'CLINICAL_SCORES']
    
    # Train with cross-validation
    for i in range(5):
        print(f"Training cross-validation fold {i}")
        
        fold_config = main_config.copy()
        fold_config['csv_dir'] = main_config['csv_dir'] + f'cross{i}/'
        fold_config['model_name'] = main_config['model_name'] + f'_cross{i}'
        
        # Initialize enhanced model wrapper
        model = Multask_Wrapper(
            tasks=tasks,
            device='cuda' if torch.cuda.is_available() else 'cpu',
            main_config=fold_config,
            task_config=task_config,
            seed=1000
        )
        
        # Train model
        model.train()
        
        # Generate scores
        model.gen_score(['test'], model.get_optimal_thres())

if __name__ == "__main__":
    train_enhanced_mci_model()
```

### Step 2: Create Enhanced Data Preparation Script

```python
# prepare_mci_data.py
import pandas as pd
import numpy as np
from pathlib import Path

def prepare_mci_dataset():
    """Prepare dataset with MCI labels"""
    
    # Read existing cross-validation data
    for i in range(5):
        csv_dir = Path(f'ncomms2022/lookupcsv/CrossValid/cross{i}')
        
        for split in ['train', 'valid', 'test']:
            csv_file = csv_dir / f'{split}.csv'
            
            if csv_file.exists():
                df = pd.read_csv(csv_file)
                
                # Add MCI classification based on existing ADD and COG scores
                df['MCI_CLASS'] = df.apply(classify_mci, axis=1)
                df['ATROPHY_SCORE'] = df.apply(calculate_atrophy_score, axis=1)
                
                # Save enhanced CSV
                enhanced_csv = csv_dir / f'{split}_enhanced.csv'
                df.to_csv(enhanced_csv, index=False)
                print(f"Enhanced dataset saved: {enhanced_csv}")

def classify_mci(row):
    """Classify into CN/MCI/AD based on existing scores"""
    add_score = row.get('ADD', 0)
    cog_score = row.get('COG', 1)
    
    # Simple heuristic - adjust based on your data
    if add_score == 0 and cog_score < 1.5:
        return 0  # CN
    elif add_score == 1 and cog_score > 2.5:
        return 2  # AD
    else:
        return 1  # MCI

def calculate_atrophy_score(row):
    """Calculate continuous atrophy score"""
    add_score = row.get('ADD', 0)
    cog_score = row.get('COG', 1)
    
    # Normalize to 0-1 range
    atrophy = (add_score * 0.6) + ((cog_score - 1) / 2 * 0.4)
    return np.clip(atrophy, 0, 1)

if __name__ == "__main__":
    prepare_mci_dataset()
```

### Step 3: Cluster Training Script

```bash
#!/bin/bash
# train_enhanced_mci.sh

#SBATCH --job-name=enhanced_mci_training
#SBATCH --partition=gpu
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=enhanced_mci_training_%j.out
#SBATCH --error=enhanced_mci_training_%j.err

# Load modules (adjust for your cluster)
module load python/3.8
module load cuda/11.0

# Activate conda environment
conda activate abstract

# Set up paths
export PYTHONPATH="${PYTHONPATH}:$(pwd)/ncomms2022"

# Run enhanced training
echo "Starting Enhanced MCI Model Training..."
echo "Timestamp: $(date)"
echo "GPU: $CUDA_VISIBLE_DEVICES"

python enhanced_training_script.py

echo "Training completed at: $(date)"
```

## 📊 **Data Requirements for MCI Training**

### Required Data Structure:
```csv
filename,path,ADD,COG,MCI_CLASS,ATROPHY_SCORE
subject001.npy,/data/processed/,0,1.2,0,0.15
subject002.npy,/data/processed/,1,2.8,1,0.45
subject003.npy,/data/processed/,1,3.2,2,0.85
```

### MCI Classification Labels:
- **0**: Cognitive Normal (CN)
- **1**: Mild Cognitive Impairment (MCI)  
- **2**: Alzheimer's Disease (AD)

### Continuous Atrophy Score:
- **0.0-0.33**: Normal range
- **0.33-0.67**: MCI range
- **0.67-1.0**: AD range

## 🔧 **Cluster Setup Questions**

To help you set up training, I need to know:

1. **Cluster Access**:
   - What cluster system are you using? (SLURM, PBS, etc.)
   - How do you typically submit jobs?
   - What's your username and project allocation?

2. **Data Location**:
   - Where are the MRI `.npy` files stored on the cluster?
   - What's the full path to your data directory?
   - Do you have access to the original ncomms2022 datasets?

3. **Environment**:
   - Is conda available on the cluster?
   - What Python/CUDA versions are available?
   - Are there any specific modules to load?

4. **Resources**:
   - How many GPUs can you request?
   - What's the maximum job time limit?
   - How much memory can you allocate?

## 🎯 **Next Steps**

1. **Identify your data paths** - Check where your MRI data is stored
2. **Prepare MCI labels** - Create enhanced CSV files with MCI classifications
3. **Set up cluster environment** - Configure conda environment and modules
4. **Submit training job** - Use the provided SLURM script template
5. **Monitor training** - Check logs and TensorBoard outputs

## 💡 **Quick Start Commands**

```bash
# 1. Navigate to your project directory
cd /path/to/your/mri_frontend

# 2. Check data structure
ls -la ncomms2022/lookupcsv/CrossValid/cross0/

# 3. Prepare enhanced data
python prepare_mci_data.py

# 4. Submit training job
sbatch train_enhanced_mci.sh

# 5. Monitor job
squeue -u $USER
tail -f enhanced_mci_training_*.out
```

Would you like me to help you adapt these scripts once you provide information about your specific cluster setup and data locations?
