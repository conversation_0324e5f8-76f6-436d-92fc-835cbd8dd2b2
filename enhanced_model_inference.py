#!/usr/bin/env python3
"""
Enhanced Model Inference with Clinical Radiological Features
Integrates Radiology Assistant guidelines for comprehensive T1 MRI assessment
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
import nibabel as nib
from pathlib import Path
from scipy.ndimage import zoom, gaussian_filter
import logging
import json
from datetime import datetime

from model_inference import RealMCIModel, MCIInferenceEngine
from clinical_radiological_features import ClinicalRadiologicalAssessment

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMCIInferenceEngine(MCIInferenceEngine):
    """
    Enhanced inference engine with comprehensive clinical radiological assessment
    """
    
    def __init__(self, model_path, device='cpu'):
        super().__init__(model_path, device)
        
        # Initialize clinical assessment
        self.clinical_assessment = ClinicalRadiologicalAssessment()
        
        logger.info("Enhanced MCI Inference Engine initialized with clinical radiological features")
    
    def predict_comprehensive(self, mri_path, patient_age=70, patient_info=None):
        """
        Comprehensive prediction with full clinical radiological assessment
        """
        
        logger.info(f"Starting comprehensive analysis for: {mri_path}")
        
        # Basic prediction
        basic_results = self.predict(mri_path)
        
        # Extract data for clinical assessment
        mri_data = basic_results['original_mri']
        heatmap = basic_results['heatmap']
        predicted_class = basic_results['prediction']['class_id']
        
        # Generate comprehensive clinical report
        clinical_report = self.clinical_assessment.generate_comprehensive_report(
            mri_data, heatmap, age=patient_age, predicted_class=predicted_class
        )
        
        # Enhanced results with clinical features
        enhanced_results = {
            **basic_results,
            'clinical_radiological_assessment': clinical_report,
            'patient_info': {
                'age': patient_age,
                'additional_info': patient_info or {}
            },
            'analysis_timestamp': datetime.now().isoformat(),
            'radiological_features': self._extract_enhanced_features(mri_data, heatmap, clinical_report)
        }
        
        # Generate clinical summary
        enhanced_results['clinical_summary'] = self._generate_clinical_summary(enhanced_results)
        
        logger.info("Comprehensive analysis completed")
        
        return enhanced_results
    
    def _extract_enhanced_features(self, mri_data, heatmap, clinical_report):
        """Extract enhanced radiological features"""
        
        features = {
            # Basic morphometric features
            'brain_volume_index': float(np.sum(mri_data > 0.1) / mri_data.size),
            'csf_volume_index': float(np.sum(mri_data < 0.2) / mri_data.size),
            'gray_matter_index': float(np.sum((mri_data > 0.3) & (mri_data < 0.7)) / mri_data.size),
            'white_matter_index': float(np.sum(mri_data > 0.7) / mri_data.size),
            
            # Atrophy patterns
            'global_atrophy_severity': float(np.mean(heatmap)),
            'focal_atrophy_max': float(np.max(heatmap)),
            'atrophy_distribution': self._analyze_atrophy_distribution(heatmap),
            
            # Clinical scores from report
            'mta_score': clinical_report['clinical_scores']['MTA']['mta_score'],
            'gca_score': clinical_report['clinical_scores']['GCA']['gca_score'],
            'koedam_score': clinical_report['clinical_scores']['Koedam']['koedam_score'],
            
            # Radiological features
            'asymmetry_index': clinical_report['radiological_features']['asymmetry_index'],
            'ventricular_enlargement': clinical_report['radiological_features']['ventricular_enlargement'],
            'sulcal_widening': clinical_report['radiological_features']['sulcal_widening']
        }
        
        return features
    
    def _analyze_atrophy_distribution(self, heatmap):
        """Analyze spatial distribution of atrophy"""
        
        # Divide brain into regions
        z_third = heatmap.shape[0] // 3
        y_third = heatmap.shape[1] // 3
        x_third = heatmap.shape[2] // 3
        
        distribution = {
            'anterior': float(np.mean(heatmap[:z_third, :, :])),
            'middle': float(np.mean(heatmap[z_third:2*z_third, :, :])),
            'posterior': float(np.mean(heatmap[2*z_third:, :, :])),
            'superior': float(np.mean(heatmap[:, :y_third, :])),
            'central': float(np.mean(heatmap[:, y_third:2*y_third, :])),
            'inferior': float(np.mean(heatmap[:, 2*y_third:, :])),
            'left': float(np.mean(heatmap[:, :, :x_third])),
            'midline': float(np.mean(heatmap[:, :, x_third:2*x_third])),
            'right': float(np.mean(heatmap[:, :, 2*x_third:]))
        }
        
        return distribution
    
    def _generate_clinical_summary(self, results):
        """Generate comprehensive clinical summary"""
        
        prediction = results['prediction']
        clinical_scores = results['clinical_radiological_assessment']['clinical_scores']
        strategic_infarcts = results['clinical_radiological_assessment']['strategic_infarcts']
        
        summary = {
            'primary_diagnosis': prediction['class'],
            'confidence_level': self._interpret_confidence(prediction['confidence']),
            'key_findings': [],
            'clinical_significance': [],
            'recommendations': results['clinical_radiological_assessment']['recommendations']
        }
        
        # Key findings
        if clinical_scores['MTA']['interpretation'] == 'Abnormal':
            summary['key_findings'].append(f"Abnormal medial temporal atrophy (MTA score: {clinical_scores['MTA']['mta_score']})")
        
        if clinical_scores['GCA']['gca_score'] >= 2:
            summary['key_findings'].append(f"Significant global cortical atrophy (GCA score: {clinical_scores['GCA']['gca_score']})")
        
        if clinical_scores['Koedam']['koedam_score'] >= 2:
            summary['key_findings'].append(f"Posterior atrophy present (Koedam score: {clinical_scores['Koedam']['koedam_score']})")
        
        # Strategic infarcts
        high_risk_infarcts = [region for region, data in strategic_infarcts.items() if data['risk_level'] == 'High']
        if high_risk_infarcts:
            summary['key_findings'].append(f"Potential strategic infarcts: {', '.join(high_risk_infarcts)}")
        
        # Clinical significance
        if prediction['class_id'] == 2:  # AD
            if clinical_scores['MTA']['mta_score'] >= 3:
                summary['clinical_significance'].append("High MTA score strongly supports AD diagnosis")
            if clinical_scores['Koedam']['koedam_score'] >= 2:
                summary['clinical_significance'].append("Posterior atrophy pattern consistent with AD")
        
        elif prediction['class_id'] == 1:  # MCI
            if clinical_scores['MTA']['mta_score'] >= 2:
                summary['clinical_significance'].append("MTA present - high risk for progression to AD")
            else:
                summary['clinical_significance'].append("Normal MTA - lower risk for AD progression")
        
        else:  # CN
            if clinical_scores['MTA']['mta_score'] >= 2:
                summary['clinical_significance'].append("Unexpected MTA for normal cognition - consider follow-up")
        
        return summary
    
    def _interpret_confidence(self, confidence):
        """Interpret confidence level"""
        if confidence >= 0.9:
            return "Very High"
        elif confidence >= 0.8:
            return "High"
        elif confidence >= 0.7:
            return "Moderate"
        elif confidence >= 0.6:
            return "Low"
        else:
            return "Very Low"
    
    def generate_clinical_report(self, results, output_path=None):
        """Generate formatted clinical report"""
        
        report_text = f"""
COMPREHENSIVE MRI DEMENTIA ASSESSMENT REPORT
==========================================

Patient Information:
- Age: {results['patient_info']['age']} years
- Analysis Date: {results['analysis_timestamp']}

IMAGING FINDINGS:
================

Primary Classification: {results['prediction']['class']}
Confidence: {results['clinical_summary']['confidence_level']} ({results['prediction']['confidence']:.1%})

Class Probabilities:
- CN (Normal): {results['prediction']['probabilities']['CN']:.1%}
- MCI (Mild Cognitive Impairment): {results['prediction']['probabilities']['MCI']:.1%}
- AD (Alzheimer's Disease): {results['prediction']['probabilities']['AD']:.1%}

CLINICAL RADIOLOGICAL SCORES:
=============================

MTA Score (Medial Temporal Atrophy): {results['clinical_radiological_assessment']['clinical_scores']['MTA']['mta_score']}/4
- Interpretation: {results['clinical_radiological_assessment']['clinical_scores']['MTA']['interpretation']}
- Components:
  * Choroidal fissure widening: {results['clinical_radiological_assessment']['clinical_scores']['MTA']['components']['choroid_widening']:.3f}
  * Temporal horn widening: {results['clinical_radiological_assessment']['clinical_scores']['MTA']['components']['temporal_horn_widening']:.3f}
  * Hippocampal volume loss: {results['clinical_radiological_assessment']['clinical_scores']['MTA']['components']['hippocampal_volume_loss']:.3f}

GCA Score (Global Cortical Atrophy): {results['clinical_radiological_assessment']['clinical_scores']['GCA']['gca_score']}/3
- Interpretation: {results['clinical_radiological_assessment']['clinical_scores']['GCA']['interpretation']}

Koedam Score (Posterior Atrophy): {results['clinical_radiological_assessment']['clinical_scores']['Koedam']['koedam_score']}/3
- Interpretation: {results['clinical_radiological_assessment']['clinical_scores']['Koedam']['interpretation']}
- Clinical Significance: {results['clinical_radiological_assessment']['clinical_scores']['Koedam']['clinical_significance']}

STRATEGIC INFARCT ASSESSMENT:
============================
"""
        
        for region, assessment in results['clinical_radiological_assessment']['strategic_infarcts'].items():
            report_text += f"""
{region.replace('_', ' ').title()}:
- Risk Level: {assessment['risk_level']}
- Clinical Significance: {assessment['clinical_significance']}
"""
        
        report_text += f"""

KEY FINDINGS:
============
"""
        for finding in results['clinical_summary']['key_findings']:
            report_text += f"• {finding}\n"
        
        report_text += f"""

CLINICAL SIGNIFICANCE:
=====================
"""
        for significance in results['clinical_summary']['clinical_significance']:
            report_text += f"• {significance}\n"
        
        report_text += f"""

RECOMMENDATIONS:
===============
"""
        for recommendation in results['clinical_summary']['recommendations']:
            report_text += f"• {recommendation}\n"
        
        report_text += f"""

RADIOLOGICAL FEATURES:
=====================
- Global atrophy severity: {results['radiological_features']['global_atrophy_severity']:.3f}
- Brain asymmetry index: {results['radiological_features']['asymmetry_index']:.3f}
- Ventricular enlargement: {results['radiological_features']['ventricular_enlargement']:.3f}
- Sulcal widening: {results['radiological_features']['sulcal_widening']:.3f}

ATROPHY SCORE: {results['atrophy_score']:.3f}

---
Report generated by Enhanced MCI Inference Engine
Based on Radiology Assistant clinical guidelines
"""
        
        if output_path:
            with open(output_path, 'w') as f:
                f.write(report_text)
            logger.info(f"Clinical report saved to: {output_path}")
        
        return report_text

# Example usage
if __name__ == "__main__":
    # Initialize enhanced inference engine
    engine = EnhancedMCIInferenceEngine('best_real_mci_model.pth')
    
    # Example comprehensive prediction
    # results = engine.predict_comprehensive('path/to/mri.nii', patient_age=72)
    
    # Generate clinical report
    # report = engine.generate_clinical_report(results, 'clinical_report.txt')
    
    print("Enhanced MCI Inference Engine with Clinical Radiological Features ready!")
    print("Features included:")
    print("✅ MTA Score (0-4) - Medial Temporal Atrophy")
    print("✅ GCA Score (0-3) - Global Cortical Atrophy") 
    print("✅ Koedam Score (0-3) - Posterior Atrophy")
    print("✅ Strategic Infarct Assessment")
    print("✅ Comprehensive Clinical Report Generation")
    print("✅ Age-adjusted interpretations")
    print("✅ Clinical recommendations")
