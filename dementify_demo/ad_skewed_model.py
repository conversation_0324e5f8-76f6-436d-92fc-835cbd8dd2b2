#!/usr/bin/env python3
"""
AD-Skewed Model for Dementify
This model is biased towards detecting Alzheimer's Disease more aggressively
"""

import numpy as np
from scipy.ndimage import gaussian_filter

def predict_with_ad_skewed_model(mri_data, case_id, ground_truth):
    """AD-SKEWED model that is more sensitive to detecting Alzheimer's Disease"""
    
    print(f"🔴 Analyzing MRI scan {case_id} with AD-SKEWED model...")
    
    # Extract real features from MRI scan (same as base model)
    brain_mask = mri_data > np.percentile(mri_data, 15)
    brain_volume = np.sum(brain_mask)
    brain_intensity_mean = np.mean(mri_data[brain_mask])
    brain_intensity_std = np.std(mri_data[brain_mask])
    
    # Calculate brain tissue characteristics
    gray_matter_threshold = brain_intensity_mean + 0.3 * brain_intensity_std
    gray_matter_volume = np.sum((mri_data > gray_matter_threshold) & brain_mask)
    gray_matter_ratio = gray_matter_volume / brain_volume if brain_volume > 0 else 0
    
    # Calculate intensity gradients (atrophy indicators) - MORE SENSITIVE
    grad_x = np.gradient(mri_data, axis=0)
    grad_y = np.gradient(mri_data, axis=1)
    grad_z = np.gradient(mri_data, axis=2)
    gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2 + grad_z**2)
    avg_gradient = np.mean(gradient_magnitude[brain_mask])
    
    # Regional analysis with AD-SKEWED sensitivity
    center_x, center_y, center_z = np.array(mri_data.shape) // 2
    
    # Hippocampal region analysis (MORE SENSITIVE to atrophy)
    hippo_left = mri_data[center_x-10:center_x-5, center_y-5:center_y+5, center_z-5:center_z+5]
    hippo_right = mri_data[center_x+5:center_x+10, center_y-5:center_y+5, center_z-5:center_z+5]
    hippo_intensity = (np.mean(hippo_left) + np.mean(hippo_right)) / 2
    
    # Temporal lobe analysis (KEY for AD detection)
    temporal_left = mri_data[center_x-15:center_x-10, center_y+5:center_y+15, center_z-5:center_z+5]
    temporal_right = mri_data[center_x+10:center_x+15, center_y+5:center_y+15, center_z-5:center_z+5]
    temporal_intensity = (np.mean(temporal_left) + np.mean(temporal_right)) / 2
    
    # Entorhinal cortex analysis (EARLY AD marker)
    entorhinal_left = mri_data[center_x-12:center_x-8, center_y-8:center_y-3, center_z-8:center_z-3]
    entorhinal_right = mri_data[center_x+8:center_x+12, center_y-8:center_y-3, center_z-8:center_z-3]
    entorhinal_intensity = (np.mean(entorhinal_left) + np.mean(entorhinal_right)) / 2
    
    print(f"   Brain volume: {brain_volume:,}, GM ratio: {gray_matter_ratio:.3f}")
    print(f"   Hippocampal: {hippo_intensity:.3f}, Temporal: {temporal_intensity:.3f}, Entorhinal: {entorhinal_intensity:.3f}")
    
    # AD-SKEWED health scoring (more sensitive to pathology)
    # Lower thresholds for detecting problems
    health_score = (gray_matter_ratio * 0.5) + (hippo_intensity / brain_intensity_mean * 0.3) + (entorhinal_intensity / brain_intensity_mean * 0.2)
    
    # Enhanced atrophy detection (more sensitive)
    atrophy_score = (avg_gradient / brain_intensity_mean) * 1.2  # Amplified sensitivity
    
    # AD-focused cognitive score (biased towards detecting problems)
    cognitive_score = health_score - (atrophy_score * 0.4)  # Higher atrophy penalty
    
    # Additional AD risk factors
    temporal_hippo_ratio = temporal_intensity / (hippo_intensity + 1e-8)
    entorhinal_risk = 1.0 - (entorhinal_intensity / brain_intensity_mean)
    
    # Combined AD risk score
    ad_risk_score = (1.0 - cognitive_score) + (entorhinal_risk * 0.3) + (atrophy_score * 0.2)
    
    print(f"   Health: {health_score:.3f}, Atrophy: {atrophy_score:.3f}, AD Risk: {ad_risk_score:.3f}")
    
    # AD-SKEWED classification (lower thresholds for AD detection)
    if cognitive_score > 0.65:  # Higher threshold for CN
        base_probs = [0.70, 0.25, 0.05]  # Still mostly CN but less confident
        predicted_class = 0
        mmse_base = 27
    elif cognitive_score > 0.45:  # Lower threshold for MCI
        base_probs = [0.15, 0.50, 0.35]  # More AD probability in MCI range
        predicted_class = 1
        mmse_base = 20
    else:  # More aggressive AD detection
        base_probs = [0.05, 0.15, 0.80]  # Strong AD bias
        predicted_class = 2
        mmse_base = 14
    
    # Apply AD risk boost
    if ad_risk_score > 0.6:
        # Boost AD probability
        base_probs[2] += 0.15  # Increase AD prob
        base_probs[0] -= 0.10  # Decrease CN prob
        base_probs[1] -= 0.05  # Decrease MCI prob
        mmse_base -= 2  # Lower MMSE for high risk
    
    # Add scan-specific variation
    scan_variation = np.random.RandomState(int(np.sum(mri_data) % 1000))
    noise = scan_variation.normal(0, 0.04, 3)
    probs = np.array(base_probs) + noise
    probs = np.maximum(probs, 0.01)
    probs = probs / np.sum(probs)
    
    # Update predicted class based on final probabilities
    predicted_class = np.argmax(probs)
    confidence = float(probs[predicted_class])
    
    # Calculate MMSE with AD bias (lower scores)
    mmse_score = mmse_base + (cognitive_score - 0.5) * 8  # Less optimistic scaling
    mmse_score = np.clip(mmse_score, 8, 30)
    
    print(f"   AD-SKEWED prediction: {['CN', 'MCI', 'AD'][predicted_class]} (MMSE: {mmse_score:.1f}, Conf: {confidence:.1%})")
    
    return {
        'predicted_class': predicted_class,
        'predicted_label': ['CN', 'MCI', 'AD'][predicted_class],
        'probabilities': {
            'CN': float(probs[0]),
            'MCI': float(probs[1]),
            'AD': float(probs[2])
        },
        'confidence': confidence,
        'mmse_score': float(mmse_score),
        'ground_truth': ground_truth.get(case_id, {}),
        'model_type': 'AD_SKEWED_MODEL',
        'scan_features': {
            'brain_volume': int(brain_volume),
            'gray_matter_ratio': float(gray_matter_ratio),
            'cognitive_score': float(cognitive_score),
            'health_score': float(health_score),
            'atrophy_score': float(atrophy_score),
            'ad_risk_score': float(ad_risk_score),
            'entorhinal_risk': float(entorhinal_risk)
        }
    }

def create_ad_skewed_heatmap(mri_data, predicted_class, confidence, case_id):
    """Create heatmap with AD-focused attention"""
    
    print(f"🔴 Creating AD-SKEWED heatmap for {case_id}, class {predicted_class}")
    
    # Use scan-specific seed
    scan_characteristics = np.sum(mri_data) + np.std(mri_data) + np.mean(mri_data)
    scan_hash = int(scan_characteristics * 1000) % 10000
    np.random.seed(scan_hash)
    
    # Initialize heatmap
    heatmap = np.zeros_like(mri_data, dtype=np.float32)
    brain_mask = mri_data > np.percentile(mri_data, 15)
    brain_intensities = mri_data * brain_mask
    
    # AD-focused activation levels (higher for all classes)
    if predicted_class == 0:  # CN - but still show some risk areas
        activation_strength = 0.6
        target_activation = 0.05  # 5% (higher than base model)
    elif predicted_class == 1:  # MCI - moderate-high activation
        activation_strength = 0.8
        target_activation = 0.08  # 8%
    else:  # AD - high activation
        activation_strength = 1.0
        target_activation = 0.12  # 12% (higher than base model)
    
    # Focus on AD-relevant regions
    if np.sum(brain_mask) > 0:
        # Target hippocampal and temporal regions more aggressively
        brain_mean = np.mean(brain_intensities[brain_intensities > 0])
        brain_std = np.std(brain_intensities[brain_intensities > 0])
        
        # Lower threshold for activation (more sensitive)
        meaningful_threshold = brain_mean + 0.2 * brain_std  # Lower than base model
        meaningful_regions = (brain_intensities > meaningful_threshold) & brain_mask
        
        print(f"   AD-focused regions: {np.sum(meaningful_regions)} voxels")
        
        if np.sum(meaningful_regions) > 0:
            # Create base activation
            heatmap = meaningful_regions.astype(np.float32) * activation_strength
            
            # Add scan-specific modulation
            normalized_intensity = brain_intensities / (np.max(brain_intensities) + 1e-8)
            heatmap = heatmap * (0.7 + 0.3 * normalized_intensity)  # Higher base activation
            
            # Apply brain mask
            heatmap = heatmap * brain_mask
            
            # Threshold to target activation level
            if np.max(heatmap) > 0:
                flat_heatmap = heatmap.flatten()
                sorted_vals = np.sort(flat_heatmap)[::-1]
                
                target_voxels = int(mri_data.size * target_activation)
                if target_voxels < len(sorted_vals):
                    threshold = sorted_vals[target_voxels]
                    heatmap[heatmap < threshold] = 0
        else:
            # Fallback: use all brain regions with AD bias
            heatmap = brain_mask.astype(np.float32) * activation_strength * 0.7
    
    # Apply smoothing
    heatmap = gaussian_filter(heatmap, sigma=1.0)
    
    # Ensure good visibility with AD focus
    if np.max(heatmap) > 0:
        heatmap = heatmap / np.max(heatmap)  # Normalize to 0-1
        
        # Lower threshold for AD-skewed model (more visible)
        threshold = 0.15  # Lower than base model
        heatmap[heatmap < threshold] = 0
        
        # Renormalize
        if np.max(heatmap) > 0:
            heatmap = heatmap / np.max(heatmap)
    
    activation_pct = np.sum(heatmap > 0.1) / heatmap.size * 100
    unique_vals = len(np.unique(heatmap[heatmap > 0]))
    
    print(f"   AD-SKEWED activation: {activation_pct:.2f}%, max: {np.max(heatmap):.3f}, unique: {unique_vals}")
    
    return heatmap
