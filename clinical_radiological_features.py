#!/usr/bin/env python3
"""
Clinical Radiological Features for T1 MRI Dementia Assessment
Based on Radiology Assistant guidelines: https://radiologyassistant.nl/neuroradiology/dementia/role-of-mri
"""

import numpy as np
import torch
from scipy.ndimage import gaussian_filter
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClinicalRadiologicalAssessment:
    """
    Comprehensive clinical radiological assessment based on standardized scales
    """
    
    def __init__(self):
        # Define anatomical regions with precise coordinates for T1 assessment
        self.anatomical_regions = {
            # Medial Temporal Lobe regions for MTA scoring
            'hippocampus': {
                'coords': [(35, 55), (45, 65), (35, 55)],
                'description': 'Hippocampal formation',
                'clinical_significance': 'Primary target in AD, MTA scoring'
            },
            'choroid_fissure': {
                'coords': [(38, 52), (48, 62), (38, 52)],
                'description': 'Choroidal fissure width',
                'clinical_significance': 'Early MTA changes'
            },
            'temporal_horn': {
                'coords': [(40, 50), (50, 70), (40, 50)],
                'description': 'Temporal horn of lateral ventricle',
                'clinical_significance': 'MTA progression indicator'
            },
            
            # Global Cortical Atrophy regions
            'frontal_cortex': {
                'coords': [(10, 40), (40, 80), (30, 70)],
                'description': 'Frontal cortical regions',
                'clinical_significance': 'GCA assessment, FTLD'
            },
            'parietal_cortex': {
                'coords': [(20, 50), (30, 60), (40, 70)],
                'description': 'Parietal cortical regions',
                'clinical_significance': 'GCA assessment, Koedam scoring'
            },
            'temporal_cortex': {
                'coords': [(30, 60), (40, 70), (30, 60)],
                'description': 'Temporal cortical regions',
                'clinical_significance': 'GCA assessment, FTLD'
            },
            'occipital_cortex': {
                'coords': [(60, 80), (30, 70), (30, 70)],
                'description': 'Occipital cortical regions',
                'clinical_significance': 'GCA assessment'
            },
            
            # Posterior atrophy regions for Koedam scoring
            'precuneus': {
                'coords': [(25, 45), (25, 45), (45, 65)],
                'description': 'Precuneus region',
                'clinical_significance': 'Characteristic of AD, especially presenile'
            },
            'posterior_cingulate': {
                'coords': [(30, 50), (30, 50), (40, 60)],
                'description': 'Posterior cingulate cortex',
                'clinical_significance': 'Koedam scoring, early AD changes'
            },
            'parieto_occipital_sulci': {
                'coords': [(40, 60), (20, 40), (50, 70)],
                'description': 'Parieto-occipital sulci',
                'clinical_significance': 'Koedam scoring'
            },
            
            # Strategic infarction locations
            'thalamus_medial': {
                'coords': [(42, 48), (52, 58), (42, 48)],
                'description': 'Medial thalamic nuclei',
                'clinical_significance': 'Strategic infarcts, memory/learning'
            },
            'angular_gyrus': {
                'coords': [(35, 55), (35, 55), (55, 75)],
                'description': 'Angular gyrus (dominant hemisphere)',
                'clinical_significance': 'Strategic infarcts, language'
            },
            'basal_ganglia': {
                'coords': [(38, 52), (48, 62), (38, 52)],
                'description': 'Basal ganglia structures',
                'clinical_significance': 'Lacunar infarcts, movement disorders'
            }
        }
    
    def calculate_mta_score(self, mri_data, saliency_map, age=70):
        """
        Calculate MTA (Medial Temporal Atrophy) Score (0-4)
        Based on coronal T1-weighted assessment at hippocampal level
        """
        
        # Extract hippocampal region
        hippo_coords = self.anatomical_regions['hippocampus']['coords']
        z_range, y_range, x_range = hippo_coords
        z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
        y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
        x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])
        
        hippocampus_region = mri_data[z_start:z_end, y_start:y_end, x_start:x_end]
        hippocampus_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]
        
        # Extract choroidal fissure
        choroid_coords = self.anatomical_regions['choroid_fissure']['coords']
        z_range, y_range, x_range = choroid_coords
        z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
        y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
        x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])
        
        choroid_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]
        
        # Extract temporal horn
        temporal_coords = self.anatomical_regions['temporal_horn']['coords']
        z_range, y_range, x_range = temporal_coords
        z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
        y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
        x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])
        
        temporal_horn_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]
        
        # Calculate MTA components
        choroid_widening = np.mean(choroid_atrophy)
        temporal_horn_widening = np.mean(temporal_horn_atrophy)
        hippocampal_volume_loss = np.mean(hippocampus_atrophy)
        
        # MTA scoring logic based on clinical criteria
        if choroid_widening < 0.2 and temporal_horn_widening < 0.2 and hippocampal_volume_loss < 0.2:
            mta_score = 0  # No atrophy
        elif choroid_widening >= 0.2 and temporal_horn_widening < 0.3 and hippocampal_volume_loss < 0.3:
            mta_score = 1  # Only widening of choroid fissure
        elif choroid_widening >= 0.3 and temporal_horn_widening >= 0.3 and hippocampal_volume_loss < 0.4:
            mta_score = 2  # Also widening of temporal horn
        elif hippocampal_volume_loss >= 0.4 and hippocampal_volume_loss < 0.7:
            mta_score = 3  # Moderate hippocampal volume loss
        else:
            mta_score = 4  # Severe hippocampal volume loss
        
        # Age-adjusted interpretation
        if age < 75:
            abnormal_threshold = 2
        else:
            abnormal_threshold = 3
        
        interpretation = "Normal" if mta_score < abnormal_threshold else "Abnormal"
        
        return {
            'mta_score': mta_score,
            'interpretation': interpretation,
            'age_threshold': abnormal_threshold,
            'components': {
                'choroid_widening': choroid_widening,
                'temporal_horn_widening': temporal_horn_widening,
                'hippocampal_volume_loss': hippocampal_volume_loss
            }
        }
    
    def calculate_gca_score(self, mri_data, saliency_map):
        """
        Calculate GCA (Global Cortical Atrophy) Score (0-3)
        Mean score for cortical atrophy throughout complete cerebrum
        """
        
        cortical_regions = ['frontal_cortex', 'parietal_cortex', 'temporal_cortex', 'occipital_cortex']
        regional_atrophy_scores = []
        
        for region_name in cortical_regions:
            coords = self.anatomical_regions[region_name]['coords']
            z_range, y_range, x_range = coords
            z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
            y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
            x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])
            
            region_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]
            regional_atrophy_scores.append(np.mean(region_atrophy))
        
        # Calculate mean cortical atrophy
        mean_cortical_atrophy = np.mean(regional_atrophy_scores)
        
        # GCA scoring based on clinical criteria
        if mean_cortical_atrophy < 0.2:
            gca_score = 0  # No cortical atrophy
        elif mean_cortical_atrophy < 0.4:
            gca_score = 1  # Mild atrophy: opening of sulci
        elif mean_cortical_atrophy < 0.7:
            gca_score = 2  # Moderate atrophy: volume loss of gyri
        else:
            gca_score = 3  # Severe end-stage atrophy: 'knife blade'
        
        interpretation_map = {
            0: "No cortical atrophy",
            1: "Mild atrophy: opening of sulci",
            2: "Moderate atrophy: volume loss of gyri",
            3: "Severe end-stage atrophy: 'knife blade'"
        }
        
        return {
            'gca_score': gca_score,
            'interpretation': interpretation_map[gca_score],
            'mean_cortical_atrophy': mean_cortical_atrophy,
            'regional_scores': {
                region: score for region, score in zip(cortical_regions, regional_atrophy_scores)
            }
        }
    
    def calculate_koedam_score(self, mri_data, saliency_map):
        """
        Calculate Koedam Score (0-3) for Posterior Atrophy
        Particularly important for presenile AD diagnosis
        """
        
        posterior_regions = ['precuneus', 'posterior_cingulate', 'parieto_occipital_sulci']
        posterior_atrophy_scores = []
        
        for region_name in posterior_regions:
            coords = self.anatomical_regions[region_name]['coords']
            z_range, y_range, x_range = coords
            z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
            y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
            x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])
            
            region_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]
            posterior_atrophy_scores.append(np.mean(region_atrophy))
        
        # Take maximum score across orientations (as per clinical guidelines)
        max_posterior_atrophy = np.max(posterior_atrophy_scores)
        
        # Koedam scoring
        if max_posterior_atrophy < 0.2:
            koedam_score = 0  # No posterior atrophy
        elif max_posterior_atrophy < 0.4:
            koedam_score = 1  # Mild posterior atrophy
        elif max_posterior_atrophy < 0.7:
            koedam_score = 2  # Moderate posterior atrophy
        else:
            koedam_score = 3  # Severe posterior atrophy
        
        interpretation_map = {
            0: "No posterior atrophy",
            1: "Mild posterior atrophy",
            2: "Moderate posterior atrophy", 
            3: "Severe posterior atrophy"
        }
        
        return {
            'koedam_score': koedam_score,
            'interpretation': interpretation_map[koedam_score],
            'max_posterior_atrophy': max_posterior_atrophy,
            'regional_scores': {
                region: score for region, score in zip(posterior_regions, posterior_atrophy_scores)
            },
            'clinical_significance': "High predictive value for AD, especially presenile AD"
        }
    
    def assess_strategic_infarcts(self, mri_data, saliency_map):
        """
        Assess strategic infarction locations crucial for cognitive function
        """
        
        strategic_regions = ['thalamus_medial', 'angular_gyrus', 'basal_ganglia']
        strategic_assessments = {}
        
        for region_name in strategic_regions:
            coords = self.anatomical_regions[region_name]['coords']
            z_range, y_range, x_range = coords
            z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
            y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
            x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])
            
            region_data = mri_data[z_start:z_end, y_start:y_end, x_start:x_end]
            region_saliency = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]
            
            # Detect potential infarcts (low intensity + high saliency)
            potential_infarct = np.mean(region_data < 0.3) * np.mean(region_saliency > 0.5)
            
            strategic_assessments[region_name] = {
                'infarct_probability': float(potential_infarct),
                'clinical_significance': self.anatomical_regions[region_name]['clinical_significance'],
                'risk_level': 'High' if potential_infarct > 0.3 else 'Moderate' if potential_infarct > 0.1 else 'Low'
            }
        
        return strategic_assessments
    
    def generate_comprehensive_report(self, mri_data, saliency_map, age=70, predicted_class=None):
        """
        Generate comprehensive clinical radiological report
        """
        
        # Calculate all clinical scores
        mta_assessment = self.calculate_mta_score(mri_data, saliency_map, age)
        gca_assessment = self.calculate_gca_score(mri_data, saliency_map)
        koedam_assessment = self.calculate_koedam_score(mri_data, saliency_map)
        strategic_assessment = self.assess_strategic_infarcts(mri_data, saliency_map)
        
        # Generate clinical interpretation
        clinical_interpretation = self._generate_clinical_interpretation(
            mta_assessment, gca_assessment, koedam_assessment, strategic_assessment, age, predicted_class
        )
        
        comprehensive_report = {
            'clinical_scores': {
                'MTA': mta_assessment,
                'GCA': gca_assessment,
                'Koedam': koedam_assessment
            },
            'strategic_infarcts': strategic_assessment,
            'clinical_interpretation': clinical_interpretation,
            'radiological_features': self._extract_radiological_features(mri_data, saliency_map),
            'recommendations': self._generate_recommendations(mta_assessment, gca_assessment, koedam_assessment, age)
        }
        
        return comprehensive_report
    
    def _generate_clinical_interpretation(self, mta, gca, koedam, strategic, age, predicted_class):
        """Generate clinical interpretation based on scores"""
        
        interpretation = []
        
        # MTA interpretation
        if mta['mta_score'] >= 2 and age < 75:
            interpretation.append("Significant medial temporal atrophy for age, consistent with AD")
        elif mta['mta_score'] >= 3 and age >= 75:
            interpretation.append("Abnormal medial temporal atrophy, suggestive of AD")
        
        # GCA interpretation
        if gca['gca_score'] >= 2:
            interpretation.append(f"Moderate to severe global cortical atrophy ({gca['interpretation']})")
        
        # Koedam interpretation
        if koedam['koedam_score'] >= 2:
            interpretation.append("Significant posterior atrophy, particularly relevant for presenile AD")
        
        # Strategic infarcts
        high_risk_regions = [region for region, data in strategic.items() if data['risk_level'] == 'High']
        if high_risk_regions:
            interpretation.append(f"Potential strategic infarcts in: {', '.join(high_risk_regions)}")
        
        # Overall assessment
        if not interpretation:
            interpretation.append("No significant atrophy patterns detected")
        
        return interpretation
    
    def _extract_radiological_features(self, mri_data, saliency_map):
        """Extract key radiological features"""
        
        features = {
            'global_atrophy': np.mean(saliency_map),
            'asymmetry_index': self._calculate_asymmetry(saliency_map),
            'ventricular_enlargement': self._assess_ventricular_enlargement(mri_data),
            'sulcal_widening': self._assess_sulcal_widening(saliency_map)
        }
        
        return features
    
    def _calculate_asymmetry(self, saliency_map):
        """Calculate brain asymmetry index"""
        mid_sagittal = saliency_map.shape[0] // 2
        left_hemisphere = saliency_map[:mid_sagittal, :, :]
        right_hemisphere = saliency_map[mid_sagittal:, :, :]
        
        left_mean = np.mean(left_hemisphere)
        right_mean = np.mean(right_hemisphere)
        
        asymmetry_index = abs(left_mean - right_mean) / (left_mean + right_mean + 1e-8)
        return float(asymmetry_index)
    
    def _assess_ventricular_enlargement(self, mri_data):
        """Assess ventricular enlargement"""
        # Simple assessment based on central low-intensity regions
        center_region = mri_data[40:50, 50:60, 40:50]
        ventricular_volume = np.sum(center_region < 0.2) / center_region.size
        return float(ventricular_volume)
    
    def _assess_sulcal_widening(self, saliency_map):
        """Assess sulcal widening"""
        # High saliency in cortical regions indicates sulcal widening
        cortical_saliency = np.mean(saliency_map[20:70, 20:80, 20:70])
        return float(cortical_saliency)
    
    def _generate_recommendations(self, mta, gca, koedam, age):
        """Generate clinical recommendations"""
        
        recommendations = []
        
        if mta['mta_score'] >= 2:
            recommendations.append("Consider neuropsychological testing and CSF biomarkers")
            recommendations.append("Follow-up MRI in 12-18 months to assess progression")
        
        if gca['gca_score'] >= 2:
            recommendations.append("Evaluate for reversible causes of dementia")
            recommendations.append("Consider PET imaging for metabolic assessment")
        
        if koedam['koedam_score'] >= 2 and age < 65:
            recommendations.append("High suspicion for presenile AD - consider genetic testing")
        
        if not recommendations:
            recommendations.append("Continue routine clinical monitoring")
        
        return recommendations

# Example usage and testing
if __name__ == "__main__":
    # Initialize assessment
    assessment = ClinicalRadiologicalAssessment()
    
    # Create dummy data for testing
    dummy_mri = np.random.randn(91, 109, 91) * 0.1 + 0.5
    dummy_saliency = np.random.uniform(0, 1, (91, 109, 91))
    
    # Generate comprehensive report
    report = assessment.generate_comprehensive_report(dummy_mri, dummy_saliency, age=72)
    
    print("Clinical Radiological Assessment Report:")
    print("=" * 50)
    print(f"MTA Score: {report['clinical_scores']['MTA']['mta_score']}")
    print(f"GCA Score: {report['clinical_scores']['GCA']['gca_score']}")
    print(f"Koedam Score: {report['clinical_scores']['Koedam']['koedam_score']}")
    print("\nClinical Interpretation:")
    for interpretation in report['clinical_interpretation']:
        print(f"- {interpretation}")
