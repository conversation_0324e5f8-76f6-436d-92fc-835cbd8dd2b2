#!/usr/bin/env python3
"""
Continuous Prediction System with Proper Thresholds
Realistic MCI classification with continuous scores and proper class boundaries
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import json
from pathlib import Path

class ContinuousMCIModel(nn.Module):
    """Model that outputs continuous cognitive scores"""
    
    def __init__(self, dropout_rate=0.5):
        super(ContinuousMCIModel, self).__init__()
        
        # Conservative CNN architecture
        self.features = nn.Sequential(
            nn.Conv3d(1, 16, kernel_size=5, padding=2),
            nn.BatchNorm3d(16),
            nn.ReLU(inplace=True),
            nn.Dropout3d(0.2),
            nn.MaxPool3d(3),
            
            nn.Conv3d(16, 32, kernel_size=3, padding=1),
            nn.<PERSON>ch<PERSON>orm3d(32),
            nn.<PERSON>L<PERSON>(inplace=True),
            nn.Dropout3d(0.3),
            nn.MaxPool3d(3),
            
            nn.Conv3d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.Dropout3d(0.4),
            nn.AdaptiveAvgPool3d((3, 3, 3))
        )
        
        self.feature_size = 64 * 3 * 3 * 3
        
        # Continuous cognitive score predictor (0-30 scale like MMSE)
        self.cognitive_score_head = nn.Sequential(
            nn.Linear(self.feature_size, 128),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.8),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Output 0-1, will scale to 0-30
        )
        
        # Atrophy severity score (0-1)
        self.atrophy_head = nn.Sequential(
            nn.Linear(self.feature_size, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.7),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
        # Clinical scores (MTA, GCA, Koedam)
        self.clinical_head = nn.Sequential(
            nn.Linear(self.feature_size, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.7),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(32, 3),
            nn.Sigmoid()  # Will scale to appropriate ranges
        )
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.02)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        features = self.features(x)
        features = features.view(features.size(0), -1)
        
        # Continuous cognitive score (0-30 scale)
        cognitive_raw = self.cognitive_score_head(features)
        cognitive_score = cognitive_raw * 30.0  # Scale to 0-30
        
        # Atrophy score (0-1)
        atrophy_score = self.atrophy_head(features)
        
        # Clinical scores (scaled to appropriate ranges)
        clinical_raw = self.clinical_head(features)
        clinical_scores = clinical_raw * torch.tensor([4.0, 3.0, 3.0])  # MTA(0-4), GCA(0-3), Koedam(0-3)
        
        return {
            'cognitive_score': cognitive_score,
            'atrophy_score': atrophy_score,
            'clinical_scores': clinical_scores,
            'features': features
        }

class ContinuousThresholdClassifier:
    """Converts continuous scores to discrete classes with proper thresholds"""
    
    def __init__(self):
        # Evidence-based thresholds for cognitive scores (MMSE-like scale 0-30)
        self.thresholds = {
            'cn_threshold': 26.0,    # Normal: 26-30
            'mci_threshold': 20.0,   # MCI: 20-25
            # AD: 0-19
        }
        
        # Confidence bands around thresholds
        self.confidence_bands = {
            'cn_band': 1.0,    # 25-27 = uncertain CN
            'mci_band': 1.5,   # 18.5-21.5 = uncertain MCI
        }
    
    def classify_continuous_score(self, cognitive_score, return_probabilities=True):
        """Convert continuous cognitive score to class probabilities"""
        
        score = float(cognitive_score)
        
        # Base classification
        if score >= self.thresholds['cn_threshold']:
            base_class = 0  # CN
        elif score >= self.thresholds['mci_threshold']:
            base_class = 1  # MCI
        else:
            base_class = 2  # AD
        
        if not return_probabilities:
            return base_class
        
        # Calculate soft probabilities based on distance from thresholds
        cn_prob = self._sigmoid_probability(score, self.thresholds['cn_threshold'], 2.0)
        ad_prob = self._sigmoid_probability(30 - score, 30 - self.thresholds['mci_threshold'], 2.0)
        mci_prob = 1.0 - cn_prob - ad_prob
        mci_prob = max(0.0, mci_prob)  # Ensure non-negative
        
        # Normalize probabilities
        total = cn_prob + mci_prob + ad_prob
        if total > 0:
            cn_prob /= total
            mci_prob /= total
            ad_prob /= total
        else:
            # Fallback to uniform distribution
            cn_prob = mci_prob = ad_prob = 1/3
        
        return np.array([cn_prob, mci_prob, ad_prob])
    
    def _sigmoid_probability(self, score, threshold, steepness=1.0):
        """Sigmoid function for smooth probability transitions"""
        return 1.0 / (1.0 + np.exp(-steepness * (score - threshold)))
    
    def get_confidence_level(self, cognitive_score):
        """Get confidence level for the prediction"""
        score = float(cognitive_score)
        
        # Distance from nearest threshold
        dist_to_cn = abs(score - self.thresholds['cn_threshold'])
        dist_to_mci = abs(score - self.thresholds['mci_threshold'])
        
        min_dist = min(dist_to_cn, dist_to_mci)
        
        # High confidence if far from thresholds
        if min_dist > 3.0:
            return "High"
        elif min_dist > 1.5:
            return "Medium"
        else:
            return "Low"

def create_realistic_training_data(num_samples=1200):
    """Create realistic training data with proper class distributions"""
    
    print("🧠 Creating realistic training data...")
    
    # Realistic class distribution (not perfectly balanced)
    cn_samples = int(num_samples * 0.45)    # 45% CN
    mci_samples = int(num_samples * 0.35)   # 35% MCI  
    ad_samples = num_samples - cn_samples - mci_samples  # 20% AD
    
    data = []
    cognitive_scores = []
    labels = []
    
    # CN samples (cognitive scores 26-30)
    for _ in range(cn_samples):
        mri = np.random.normal(0.5, 0.15, (91, 109, 91))
        # Add healthy brain patterns
        mri = add_healthy_patterns(mri)
        
        # Cognitive score for CN: 26-30 with some noise
        cog_score = np.random.normal(28.0, 1.5)
        cog_score = np.clip(cog_score, 26.0, 30.0)
        
        data.append(mri)
        cognitive_scores.append(cog_score)
        labels.append(0)  # CN
    
    # MCI samples (cognitive scores 20-25)
    for _ in range(mci_samples):
        mri = np.random.normal(0.4, 0.18, (91, 109, 91))
        # Add mild atrophy patterns
        mri = add_mild_atrophy_patterns(mri)
        
        # Cognitive score for MCI: 20-25 with some noise
        cog_score = np.random.normal(22.5, 1.8)
        cog_score = np.clip(cog_score, 20.0, 25.0)
        
        data.append(mri)
        cognitive_scores.append(cog_score)
        labels.append(1)  # MCI
    
    # AD samples (cognitive scores 10-19)
    for _ in range(ad_samples):
        mri = np.random.normal(0.3, 0.2, (91, 109, 91))
        # Add severe atrophy patterns
        mri = add_severe_atrophy_patterns(mri)
        
        # Cognitive score for AD: 10-19 with some noise
        cog_score = np.random.normal(15.0, 3.0)
        cog_score = np.clip(cog_score, 5.0, 19.0)
        
        data.append(mri)
        cognitive_scores.append(cog_score)
        labels.append(2)  # AD
    
    # Shuffle data
    indices = np.random.permutation(len(data))
    data = [data[i] for i in indices]
    cognitive_scores = [cognitive_scores[i] for i in indices]
    labels = [labels[i] for i in indices]
    
    print(f"✅ Created {len(data)} samples:")
    print(f"   CN: {cn_samples} (45%)")
    print(f"   MCI: {mci_samples} (35%)")
    print(f"   AD: {ad_samples} (20%)")
    
    return np.array(data), np.array(cognitive_scores), np.array(labels)

def add_healthy_patterns(mri):
    """Add healthy brain patterns"""
    center = (45, 54, 45)
    # Add some structure but keep healthy
    for i in range(center[0]-15, center[0]+15):
        for j in range(center[1]-20, center[1]+20):
            for k in range(center[2]-15, center[2]+15):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    mri[i, j, k] += np.random.normal(0.1, 0.05)
    return np.clip(mri, 0, 1)

def add_mild_atrophy_patterns(mri):
    """Add mild atrophy patterns for MCI"""
    center = (45, 54, 45)
    # Mild hippocampal atrophy
    for i in range(center[0]-8, center[0]+8):
        for j in range(center[1]-12, center[1]+12):
            for k in range(center[2]-8, center[2]+8):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    mri[i, j, k] *= np.random.uniform(0.85, 0.95)
    return np.clip(mri, 0, 1)

def add_severe_atrophy_patterns(mri):
    """Add severe atrophy patterns for AD"""
    center = (45, 54, 45)
    # Significant atrophy
    for i in range(center[0]-12, center[0]+12):
        for j in range(center[1]-18, center[1]+18):
            for k in range(center[2]-12, center[2]+12):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    mri[i, j, k] *= np.random.uniform(0.6, 0.8)
    return np.clip(mri, 0, 1)

def test_continuous_system():
    """Test the continuous prediction system"""
    
    print("🧪 Testing Continuous Prediction System...")
    
    # Create classifier
    classifier = ContinuousThresholdClassifier()
    
    # Test various cognitive scores
    test_scores = [29.0, 27.0, 24.0, 22.0, 18.0, 12.0]
    
    print("\n📊 Threshold Testing:")
    print("Score | Class | Probabilities | Confidence")
    print("-" * 45)
    
    for score in test_scores:
        probs = classifier.classify_continuous_score(score)
        predicted_class = np.argmax(probs)
        class_names = ['CN', 'MCI', 'AD']
        confidence = classifier.get_confidence_level(score)
        
        print(f"{score:5.1f} | {class_names[predicted_class]:4s} | "
              f"[{probs[0]:.2f}, {probs[1]:.2f}, {probs[2]:.2f}] | {confidence}")
    
    return classifier

if __name__ == "__main__":
    print("🧠 CONTINUOUS PREDICTION SYSTEM")
    print("=" * 40)
    
    # Test the system
    classifier = test_continuous_system()
    
    # Create sample data
    data, scores, labels = create_realistic_training_data(300)
    
    print(f"\n✅ System ready for training with realistic data")
    print(f"📊 Cognitive score range: {scores.min():.1f} - {scores.max():.1f}")
    print(f"🎯 Thresholds: CN≥26, MCI≥20, AD<20")
