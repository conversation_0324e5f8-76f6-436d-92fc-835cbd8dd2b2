#!/usr/bin/env python3
"""
Test Heatmap Fix - Verify no more red brain everywhere
"""

import numpy as np
from final_mci_streamlit_app import FinalMCIInferenceEngine
import matplotlib.pyplot as plt

def test_heatmap_quality():
    """Test that heatmaps are clean and selective"""
    
    print("🗺️ TESTING HEATMAP QUALITY FIX")
    print("=" * 50)
    print("Objective: Verify no more red dots everywhere")
    print()
    
    # Create inference engine
    engine = FinalMCIInferenceEngine()
    
    # Test cases with different MMSE scores
    test_cases = [
        {
            'name': 'Normal Brain (MMSE 28)',
            'mmse': 28,
            'expected': 'Empty or minimal heatmap'
        },
        {
            'name': 'Mild MCI (MMSE 22)',
            'mmse': 22,
            'expected': 'Empty heatmap'
        },
        {
            'name': 'Moderate MCI (MMSE 18)',
            'mmse': 18,
            'expected': 'Small hippocampal regions only'
        },
        {
            'name': 'Severe AD (MMSE 12)',
            'mmse': 12,
            'expected': 'Focused hippocampal + minimal temporal'
        }
    ]
    
    for case in test_cases:
        print(f"\n🧪 Testing: {case['name']}")
        print(f"   Expected: {case['expected']}")
        
        # Create test MRI
        test_mri = np.random.normal(0.5, 0.1, (91, 109, 91))
        test_mri = np.clip(test_mri, 0, 1)
        
        # Generate heatmap
        heatmap = engine._generate_realistic_brain_heatmap(case['mmse'], test_mri)
        
        # Analyze heatmap quality
        total_voxels = heatmap.size
        nonzero_voxels = np.count_nonzero(heatmap)
        max_intensity = np.max(heatmap)
        mean_intensity = np.mean(heatmap[heatmap > 0]) if nonzero_voxels > 0 else 0
        
        print(f"   📊 Heatmap Analysis:")
        print(f"      Total voxels: {total_voxels:,}")
        print(f"      Active voxels: {nonzero_voxels:,} ({nonzero_voxels/total_voxels*100:.2f}%)")
        print(f"      Max intensity: {max_intensity:.3f}")
        print(f"      Mean intensity (active): {mean_intensity:.3f}")
        
        # Quality assessment
        if case['mmse'] >= 24:  # Normal/mild
            if nonzero_voxels == 0:
                print(f"      ✅ PERFECT: Empty heatmap for normal case")
            elif nonzero_voxels < 100:
                print(f"      ✅ GOOD: Minimal heatmap for normal case")
            else:
                print(f"      ❌ BAD: Too much activity for normal case")
        
        elif case['mmse'] >= 20:  # Mild MCI
            if nonzero_voxels == 0:
                print(f"      ✅ PERFECT: Empty heatmap for mild MCI")
            elif nonzero_voxels < 50:
                print(f"      ✅ GOOD: Minimal heatmap for mild MCI")
            else:
                print(f"      ❌ BAD: Too much activity for mild MCI")
        
        elif case['mmse'] >= 15:  # Moderate impairment
            if nonzero_voxels < 200:
                print(f"      ✅ GOOD: Focused heatmap for moderate impairment")
            elif nonzero_voxels < 1000:
                print(f"      ⚠️ OKAY: Moderate heatmap activity")
            else:
                print(f"      ❌ BAD: Too much activity for moderate impairment")
        
        else:  # Severe impairment
            if nonzero_voxels < 500:
                print(f"      ✅ GOOD: Focused heatmap for severe case")
            elif nonzero_voxels < 2000:
                print(f"      ⚠️ OKAY: Moderate heatmap activity")
            else:
                print(f"      ❌ BAD: Too much activity even for severe case")
        
        # Check for whole brain activation (the main problem)
        activation_percentage = nonzero_voxels / total_voxels * 100
        if activation_percentage > 10:
            print(f"      🚨 CRITICAL: {activation_percentage:.1f}% of brain active - WHOLE BRAIN RED!")
        elif activation_percentage > 5:
            print(f"      ⚠️ WARNING: {activation_percentage:.1f}% of brain active - too much")
        elif activation_percentage > 1:
            print(f"      📊 MODERATE: {activation_percentage:.1f}% of brain active")
        else:
            print(f"      ✅ EXCELLENT: {activation_percentage:.1f}% of brain active - focused!")

def test_comprehensive_prediction():
    """Test comprehensive prediction with heatmap quality"""
    
    print(f"\n🔍 TESTING COMPREHENSIVE PREDICTION")
    print("=" * 40)
    
    engine = FinalMCIInferenceEngine()
    
    # Create test MRI
    test_mri = np.random.normal(0.6, 0.1, (91, 109, 91))
    test_mri = np.clip(test_mri, 0, 1)
    
    try:
        # Run comprehensive prediction
        results = engine.comprehensive_predict(test_mri)
        
        print(f"✅ Comprehensive prediction successful")
        print(f"📊 Models tested: {len(results['models'])}")
        
        # Check each model's heatmap
        for model_name, model_results in results['models'].items():
            heatmap = model_results['heatmap']
            nonzero_voxels = np.count_nonzero(heatmap)
            total_voxels = heatmap.size
            activation_percentage = nonzero_voxels / total_voxels * 100
            
            print(f"\n🔍 {model_name}:")
            print(f"   MMSE Score: {model_results['cognitive_score']:.1f}")
            print(f"   Heatmap activation: {activation_percentage:.2f}%")
            
            if activation_percentage > 10:
                print(f"   🚨 PROBLEM: Whole brain red!")
            elif activation_percentage > 5:
                print(f"   ⚠️ WARNING: Too much activation")
            elif activation_percentage > 1:
                print(f"   📊 MODERATE: Reasonable activation")
            else:
                print(f"   ✅ EXCELLENT: Focused activation")
    
    except Exception as e:
        print(f"❌ Comprehensive prediction failed: {e}")

def main():
    """Main test function"""
    
    print("🗺️ HEATMAP QUALITY FIX TEST")
    print("=" * 60)
    print("Testing that heatmaps no longer show whole brain red")
    print()
    
    test_heatmap_quality()
    test_comprehensive_prediction()
    
    print(f"\n🎉 HEATMAP FIX TEST COMPLETE!")
    print(f"Check results above - should see focused, selective heatmaps")

if __name__ == "__main__":
    main()
