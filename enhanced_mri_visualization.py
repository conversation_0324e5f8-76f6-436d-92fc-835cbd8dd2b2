#!/usr/bin/env python3
"""
Enhanced MRI Visualization using Nilearn - Radiologist-Grade Visualization
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
from pathlib import Path
import streamlit as st

# Install nilearn if not available
try:
    import nilearn
    from nilearn import plotting, image
    from nilearn.plotting import plot_anat, plot_stat_map, plot_glass_brain
    from nilearn.image import resample_to_img, smooth_img
except ImportError:
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "nilearn"])
    import nilearn
    from nilearn import plotting, image
    from nilearn.plotting import plot_anat, plot_stat_map, plot_glass_brain
    from nilearn.image import resample_to_img, smooth_img

def create_radiologist_mri_visualization(mri_data, heatmap_data=None, title="MRI Scan", 
                                       cognitive_score=None, predicted_class=None):
    """
    Create radiologist-grade MRI visualization using <PERSON>arn
    
    Args:
        mri_data: 3D numpy array of MRI data
        heatmap_data: 3D numpy array of heatmap/attention data (optional)
        title: Title for the visualization
        cognitive_score: MMSE score (optional)
        predicted_class: Predicted class (CN/MCI/AD) (optional)
    
    Returns:
        matplotlib figure object
    """
    
    # Create NIfTI-like image from numpy array
    mri_img = nib.Nifti1Image(mri_data, affine=np.eye(4))
    
    if heatmap_data is not None:
        heatmap_img = nib.Nifti1Image(heatmap_data, affine=np.eye(4))
        # Smooth heatmap for better visualization
        heatmap_img = smooth_img(heatmap_img, fwhm=2.0)
    
    # Create figure with proper radiologist layout
    fig = plt.figure(figsize=(20, 12))
    
    # Title with clinical information
    title_text = title
    if cognitive_score is not None:
        title_text += f" | MMSE: {cognitive_score:.1f}/30"
    if predicted_class is not None:
        class_names = ['CN (Normal)', 'MCI (Mild Cognitive Impairment)', 'AD (Alzheimer\'s Disease)']
        title_text += f" | Prediction: {class_names[predicted_class]}"
    
    fig.suptitle(title_text, fontsize=16, fontweight='bold')
    
    # 1. Anatomical views (standard radiologist orientations)
    
    # Axial view (top-down) - Radiologist standard
    ax1 = plt.subplot(2, 4, 1)
    plotting.plot_anat(mri_img, display_mode='z', cut_coords=5, 
                      axes=ax1, title="Axial View", 
                      annotate=True, draw_cross=False)
    
    # Coronal view (front-back) - Radiologist standard  
    ax2 = plt.subplot(2, 4, 2)
    plotting.plot_anat(mri_img, display_mode='y', cut_coords=5,
                      axes=ax2, title="Coronal View",
                      annotate=True, draw_cross=False)
    
    # Sagittal view (left-right) - Radiologist standard
    ax3 = plt.subplot(2, 4, 3)
    plotting.plot_anat(mri_img, display_mode='x', cut_coords=5,
                      axes=ax3, title="Sagittal View", 
                      annotate=True, draw_cross=False)
    
    # Glass brain overview
    ax4 = plt.subplot(2, 4, 4)
    plotting.plot_glass_brain(mri_img, axes=ax4, title="Glass Brain",
                             colorbar=False, plot_abs=False)
    
    # 2. Heatmap overlays (if available)
    if heatmap_data is not None:
        
        # Axial heatmap overlay
        ax5 = plt.subplot(2, 4, 5)
        plotting.plot_stat_map(heatmap_img, bg_img=mri_img, 
                              display_mode='z', cut_coords=5,
                              axes=ax5, title="Axial + Heatmap",
                              threshold=0.1, cmap='hot', alpha=0.7,
                              annotate=True, draw_cross=False)
        
        # Coronal heatmap overlay
        ax6 = plt.subplot(2, 4, 6)
        plotting.plot_stat_map(heatmap_img, bg_img=mri_img,
                              display_mode='y', cut_coords=5, 
                              axes=ax6, title="Coronal + Heatmap",
                              threshold=0.1, cmap='hot', alpha=0.7,
                              annotate=True, draw_cross=False)
        
        # Sagittal heatmap overlay
        ax7 = plt.subplot(2, 4, 7)
        plotting.plot_stat_map(heatmap_img, bg_img=mri_img,
                              display_mode='x', cut_coords=5,
                              axes=ax7, title="Sagittal + Heatmap", 
                              threshold=0.1, cmap='hot', alpha=0.7,
                              annotate=True, draw_cross=False)
        
        # Glass brain heatmap
        ax8 = plt.subplot(2, 4, 8)
        plotting.plot_glass_brain(heatmap_img, axes=ax8, title="Attention Map",
                                 colorbar=True, plot_abs=False, cmap='hot',
                                 threshold=0.1)
    
    plt.tight_layout()
    return fig

def create_clinical_comparison_view(mri_list, heatmap_list, scores_list, predictions_list, 
                                  case_names=None):
    """
    Create side-by-side comparison for clinical review
    
    Args:
        mri_list: List of MRI data arrays
        heatmap_list: List of heatmap data arrays  
        scores_list: List of MMSE scores
        predictions_list: List of predicted classes
        case_names: List of case names (optional)
    
    Returns:
        matplotlib figure object
    """
    
    n_cases = len(mri_list)
    fig = plt.figure(figsize=(6*n_cases, 12))
    
    class_names = ['CN', 'MCI', 'AD']
    colors = ['green', 'orange', 'red']
    
    for i, (mri_data, heatmap_data, score, pred) in enumerate(zip(mri_list, heatmap_list, scores_list, predictions_list)):
        
        # Create NIfTI images
        mri_img = nib.Nifti1Image(mri_data, affine=np.eye(4))
        heatmap_img = nib.Nifti1Image(heatmap_data, affine=np.eye(4))
        heatmap_img = smooth_img(heatmap_img, fwhm=2.0)
        
        case_name = case_names[i] if case_names else f"Case {i+1}"
        pred_class = class_names[pred]
        color = colors[pred]
        
        # Axial view with heatmap
        ax1 = plt.subplot(3, n_cases, i+1)
        plotting.plot_stat_map(heatmap_img, bg_img=mri_img,
                              display_mode='z', cut_coords=1,
                              axes=ax1, title=f"{case_name}\nAxial",
                              threshold=0.1, cmap='hot', alpha=0.7,
                              annotate=False, draw_cross=False)
        
        # Coronal view with heatmap  
        ax2 = plt.subplot(3, n_cases, n_cases + i+1)
        plotting.plot_stat_map(heatmap_img, bg_img=mri_img,
                              display_mode='y', cut_coords=1,
                              axes=ax2, title="Coronal", 
                              threshold=0.1, cmap='hot', alpha=0.7,
                              annotate=False, draw_cross=False)
        
        # Clinical summary
        ax3 = plt.subplot(3, n_cases, 2*n_cases + i+1)
        ax3.text(0.5, 0.7, f"MMSE: {score:.1f}/30", 
                ha='center', va='center', fontsize=14, fontweight='bold',
                transform=ax3.transAxes)
        ax3.text(0.5, 0.5, f"Prediction: {pred_class}",
                ha='center', va='center', fontsize=12, color=color,
                fontweight='bold', transform=ax3.transAxes)
        
        # Add confidence or additional metrics here
        confidence = max([0.6, 0.7, 0.8][pred], 0.5)  # Mock confidence
        ax3.text(0.5, 0.3, f"Confidence: {confidence:.1%}",
                ha='center', va='center', fontsize=10,
                transform=ax3.transAxes)
        
        ax3.set_xlim(0, 1)
        ax3.set_ylim(0, 1)
        ax3.axis('off')
        
        # Add border color based on prediction
        for spine in ax3.spines.values():
            spine.set_edgecolor(color)
            spine.set_linewidth(3)
    
    plt.tight_layout()
    return fig

def fix_mri_orientation(mri_data, orientation_fix='flip_axial'):
    """
    Fix MRI orientation issues for radiologist viewing
    
    Args:
        mri_data: 3D numpy array
        orientation_fix: Type of fix to apply
    
    Returns:
        Fixed MRI data
    """
    
    fixed_mri = mri_data.copy()
    
    if orientation_fix == 'flip_axial':
        # 180 degree flip for axial view (as requested)
        fixed_mri = np.flip(fixed_mri, axis=2)
    elif orientation_fix == 'flip_coronal':
        # Flip coronal view
        fixed_mri = np.flip(fixed_mri, axis=1)
    elif orientation_fix == 'flip_sagittal':
        # Flip sagittal view
        fixed_mri = np.flip(fixed_mri, axis=0)
    elif orientation_fix == 'rotate_180':
        # 180 degree rotation
        fixed_mri = np.rot90(fixed_mri, k=2, axes=(0, 1))
    
    return fixed_mri

def create_streamlit_mri_viewer(mri_data, heatmap_data=None, cognitive_score=None, 
                               predicted_class=None, case_name="MRI Scan"):
    """
    Create Streamlit-compatible MRI viewer using Nilearn
    
    Args:
        mri_data: 3D numpy array of MRI data
        heatmap_data: 3D numpy array of heatmap data (optional)
        cognitive_score: MMSE score (optional)
        predicted_class: Predicted class index (optional)
        case_name: Name of the case
    
    Returns:
        Streamlit displayable figure
    """
    
    # Apply orientation fix (180 degree flip as requested)
    mri_data = fix_mri_orientation(mri_data, 'flip_axial')
    if heatmap_data is not None:
        heatmap_data = fix_mri_orientation(heatmap_data, 'flip_axial')
    
    # Create the visualization
    fig = create_radiologist_mri_visualization(
        mri_data, heatmap_data, case_name, cognitive_score, predicted_class
    )
    
    return fig

def test_mri_visualization():
    """Test the MRI visualization system"""
    
    print("🧠 Testing Enhanced MRI Visualization...")
    
    # Create test MRI data
    test_mri = np.random.normal(0.5, 0.2, (91, 109, 91))
    test_heatmap = np.random.exponential(0.1, (91, 109, 91))
    
    # Add some structure
    center = (45, 54, 45)
    for i in range(center[0]-20, center[0]+20):
        for j in range(center[1]-25, center[1]+25):
            for k in range(center[2]-20, center[2]+20):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                    if dist < 15:
                        test_mri[i, j, k] += 0.3
                        test_heatmap[i, j, k] += 0.2
    
    # Test single case visualization
    fig = create_radiologist_mri_visualization(
        test_mri, test_heatmap, "Test Case", 
        cognitive_score=22.5, predicted_class=1
    )
    
    plt.savefig('test_mri_visualization.png', dpi=300, bbox_inches='tight')
    print("✅ Test visualization saved to: test_mri_visualization.png")
    
    # Test comparison view
    mri_list = [test_mri, test_mri * 0.8, test_mri * 0.6]
    heatmap_list = [test_heatmap, test_heatmap * 1.2, test_heatmap * 1.5]
    scores_list = [28.5, 22.1, 16.8]
    predictions_list = [0, 1, 2]
    case_names = ["Normal", "MCI", "AD"]
    
    fig2 = create_clinical_comparison_view(
        mri_list, heatmap_list, scores_list, predictions_list, case_names
    )
    
    plt.savefig('test_clinical_comparison.png', dpi=300, bbox_inches='tight')
    print("✅ Clinical comparison saved to: test_clinical_comparison.png")
    
    print("🎉 MRI visualization system ready!")

if __name__ == "__main__":
    test_mri_visualization()
