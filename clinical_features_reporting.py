"""
Clinical Features Extraction and Enhanced Reporting System
Derives quantitative clinical features and generates comprehensive PDF reports with MCI-specific recommendations
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import streamlit as st
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.backends.backend_pdf import PdfPages
import io
import base64
from scipy import ndimage
from scipy.stats import percentileofscore

class ClinicalFeatureExtractor:
    """
    Extract quantitative clinical features from MRI data and model predictions
    Derives features that radiologists use for assessment
    """
    
    def __init__(self):
        # Reference values for normalization (based on healthy population)
        self.reference_values = {
            'hippocampal_volume': {'mean': 4200, 'std': 400},  # mm³
            'total_brain_volume': {'mean': 1400000, 'std': 100000},  # mm³
            'ventricular_volume': {'mean': 25000, 'std': 8000},  # mm³
            'cortical_thickness': {'mean': 2.8, 'std': 0.3},  # mm
            'csf_volume': {'mean': 180000, 'std': 30000}  # mm³
        }
        
        # Age-related correction factors
        self.age_corrections = {
            'hippocampal_volume': -0.5,  # % per year after 60
            'cortical_thickness': -0.3,  # % per year after 60
            'ventricular_volume': 1.2   # % per year after 60
        }
    
    def extract_comprehensive_features(self, mri_data: np.ndarray, 
                                     brain_mask: Optional[np.ndarray],
                                     predictions: Dict,
                                     metadata: Dict) -> Dict:
        """
        Extract comprehensive clinical features from MRI data
        
        Args:
            mri_data: Preprocessed MRI data
            brain_mask: Brain mask from preprocessing
            predictions: Model predictions
            metadata: MRI metadata
            
        Returns:
            Dictionary of extracted clinical features
        """
        features = {}
        
        try:
            st.info("🔬 Extracting clinical features...")
            
            # Basic volumetric features
            features.update(self._extract_volumetric_features(mri_data, brain_mask))
            
            # Regional atrophy measures
            features.update(self._extract_regional_atrophy(mri_data, predictions))
            
            # Morphometric features
            features.update(self._extract_morphometric_features(mri_data, brain_mask))
            
            # Derived clinical indices
            features.update(self._calculate_clinical_indices(features, predictions))
            
            # Age-normalized features (if age available)
            if 'age' in metadata:
                features.update(self._apply_age_normalization(features, metadata['age']))
            
            st.success(f"✅ Extracted {len(features)} clinical features")
            return features
            
        except Exception as e:
            st.error(f"❌ Feature extraction failed: {str(e)}")
            return {}
    
    def _extract_volumetric_features(self, mri_data: np.ndarray, 
                                   brain_mask: Optional[np.ndarray]) -> Dict:
        """Extract basic volumetric features"""
        features = {}
        
        # Voxel volume (assuming 1mm³ voxels after preprocessing)
        voxel_volume = 1.0  # mm³
        
        if brain_mask is not None:
            # Total brain volume
            features['total_brain_volume'] = float(np.sum(brain_mask) * voxel_volume)
            
            # Brain tissue volume (non-zero intensities within brain)
            brain_tissue = mri_data[brain_mask > 0]
            features['brain_tissue_volume'] = float(np.sum(brain_tissue > 0.1) * voxel_volume)
        else:
            # Fallback without brain mask
            features['total_brain_volume'] = float(np.sum(mri_data > 0.1) * voxel_volume)
            features['brain_tissue_volume'] = features['total_brain_volume']
        
        # Estimate CSF volume (low intensity regions within brain)
        if brain_mask is not None:
            csf_estimate = np.sum((mri_data > 0.05) & (mri_data < 0.2) & brain_mask) * voxel_volume
        else:
            csf_estimate = np.sum((mri_data > 0.05) & (mri_data < 0.2)) * voxel_volume
        
        features['estimated_csf_volume'] = float(csf_estimate)
        
        # Brain parenchymal fraction
        if features['total_brain_volume'] > 0:
            features['brain_parenchymal_fraction'] = features['brain_tissue_volume'] / features['total_brain_volume']
        else:
            features['brain_parenchymal_fraction'] = 0.0
        
        return features
    
    def _extract_regional_atrophy(self, mri_data: np.ndarray, predictions: Dict) -> Dict:
        """Extract regional atrophy measures"""
        features = {}
        
        # Use regional atrophy predictions if available
        if 'regional_atrophy' in predictions:
            regional_scores = predictions['regional_atrophy']
            
            # Convert to clinical measures
            features['hippocampal_atrophy_score'] = regional_scores.get('hippocampus', 0.0)
            features['temporal_atrophy_score'] = regional_scores.get('temporal_cortex', 0.0)
            features['parietal_atrophy_score'] = regional_scores.get('parietal_cortex', 0.0)
            features['frontal_atrophy_score'] = regional_scores.get('frontal_cortex', 0.0)
            
            # Estimate hippocampal volume from atrophy score
            ref_volume = self.reference_values['hippocampal_volume']['mean']
            atrophy_factor = 1.0 - regional_scores.get('hippocampus', 0.0)
            features['estimated_hippocampal_volume'] = ref_volume * atrophy_factor
        
        # Global atrophy measures
        if 'atrophy_score' in predictions:
            features['global_atrophy_score'] = predictions['atrophy_score']
        
        return features
    
    def _extract_morphometric_features(self, mri_data: np.ndarray, 
                                     brain_mask: Optional[np.ndarray]) -> Dict:
        """Extract morphometric features"""
        features = {}
        
        try:
            # Estimate cortical thickness using distance transform
            if brain_mask is not None:
                # Simple cortical thickness estimation
                brain_boundary = ndimage.binary_erosion(brain_mask, iterations=3)
                distance_map = ndimage.distance_transform_edt(brain_boundary)
                
                # Average cortical thickness
                cortical_region = brain_mask & ~brain_boundary
                if np.sum(cortical_region) > 0:
                    features['mean_cortical_thickness'] = float(np.mean(distance_map[cortical_region]))
                else:
                    features['mean_cortical_thickness'] = 2.5  # Default value
            else:
                features['mean_cortical_thickness'] = 2.5
            
            # Surface area estimation (simplified)
            if brain_mask is not None:
                # Count boundary voxels as proxy for surface area
                boundary_voxels = np.sum(ndimage.binary_erosion(brain_mask) != brain_mask)
                features['estimated_surface_area'] = float(boundary_voxels * 6.0)  # mm² (6 faces per voxel)
            else:
                features['estimated_surface_area'] = 150000.0  # Default value
            
        except Exception as e:
            # Fallback values
            features['mean_cortical_thickness'] = 2.5
            features['estimated_surface_area'] = 150000.0
        
        return features
    
    def _calculate_clinical_indices(self, features: Dict, predictions: Dict) -> Dict:
        """Calculate derived clinical indices"""
        indices = {}
        
        # Brain volume ratio (compared to reference)
        if 'total_brain_volume' in features:
            ref_volume = self.reference_values['total_brain_volume']['mean']
            indices['brain_volume_ratio'] = features['total_brain_volume'] / ref_volume
        
        # Hippocampal volume ratio
        if 'estimated_hippocampal_volume' in features:
            ref_hippo = self.reference_values['hippocampal_volume']['mean']
            indices['hippocampal_volume_ratio'] = features['estimated_hippocampal_volume'] / ref_hippo
        
        # Ventricular enlargement index
        if 'estimated_csf_volume' in features and 'total_brain_volume' in features:
            indices['ventricular_enlargement_index'] = features['estimated_csf_volume'] / features['total_brain_volume']
        
        # Atrophy severity index (composite score)
        atrophy_components = []
        if 'global_atrophy_score' in features:
            atrophy_components.append(features['global_atrophy_score'])
        if 'hippocampal_atrophy_score' in features:
            atrophy_components.append(features['hippocampal_atrophy_score'])
        
        if atrophy_components:
            indices['composite_atrophy_index'] = np.mean(atrophy_components)
        
        return indices
    
    def _apply_age_normalization(self, features: Dict, age: float) -> Dict:
        """Apply age normalization to features"""
        normalized = {}
        
        if age > 60:  # Apply age correction for elderly
            age_factor = age - 60
            
            for feature, correction in self.age_corrections.items():
                if feature in features:
                    original_value = features[feature]
                    age_adjusted = original_value * (1 + (correction * age_factor / 100))
                    normalized[f'{feature}_age_adjusted'] = age_adjusted
        
        return normalized

class EnhancedPDFReportGenerator:
    """
    Generate comprehensive PDF reports with MCI-specific recommendations
    """
    
    def __init__(self):
        self.report_template = {
            'header': 'Demetify - AI-Powered Dementia Assessment Report',
            'institution': 'University of Illinois Urbana-Champaign',
            'project_lead': 'S. Seshadri'
        }
    
    def generate_comprehensive_report(self, mri_data: np.ndarray,
                                    predictions: Dict,
                                    clinical_features: Dict,
                                    interpretability_result: Dict,
                                    metadata: Dict,
                                    filename_base: str) -> bytes:
        """
        Generate comprehensive PDF report with MCI-specific content
        
        Returns:
            PDF bytes
        """
        try:
            st.info("📄 Generating comprehensive clinical report...")
            
            # Create PDF buffer
            pdf_buffer = io.BytesIO()
            
            with PdfPages(pdf_buffer) as pdf:
                # Page 1: Executive Summary
                self._create_executive_summary_page(pdf, predictions, clinical_features, metadata)
                
                # Page 2: Clinical Assessment
                self._create_clinical_assessment_page(pdf, predictions, clinical_features, interpretability_result)
                
                # Page 3: Imaging Analysis
                self._create_imaging_analysis_page(pdf, mri_data, interpretability_result)
                
                # Page 4: MCI-Specific Analysis (if applicable)
                if predictions.get('predicted_class') == 'MCI':
                    self._create_mci_specific_page(pdf, predictions, clinical_features, interpretability_result)
                
                # Page 5: Recommendations and Follow-up
                self._create_recommendations_page(pdf, predictions, clinical_features)
            
            pdf_bytes = pdf_buffer.getvalue()
            pdf_buffer.close()
            
            st.success(f"✅ Generated comprehensive report ({len(pdf_bytes):,} bytes)")
            return pdf_bytes
            
        except Exception as e:
            st.error(f"❌ PDF generation failed: {str(e)}")
            return None

    def _create_executive_summary_page(self, pdf, predictions: Dict,
                                     clinical_features: Dict, metadata: Dict):
        """Create executive summary page"""
        fig, ax = plt.subplots(figsize=(8.5, 11))
        ax.axis('off')

        # Header
        ax.text(0.5, 0.95, self.report_template['header'],
                ha='center', va='top', fontsize=16, fontweight='bold')
        ax.text(0.5, 0.92, self.report_template['institution'],
                ha='center', va='top', fontsize=12)
        ax.text(0.5, 0.89, f"Project Lead: {self.report_template['project_lead']}",
                ha='center', va='top', fontsize=10)

        # Report info
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ax.text(0.05, 0.85, f"Report Generated: {current_time}", fontsize=10)
        ax.text(0.05, 0.82, f"Patient ID: {metadata.get('patient_id', 'Anonymous')}", fontsize=10)

        # Executive Summary
        ax.text(0.05, 0.75, "EXECUTIVE SUMMARY", fontsize=14, fontweight='bold')

        predicted_class = predictions.get('predicted_class', 'Unknown')
        confidence = predictions.get('confidence', 0.0)
        atrophy_score = predictions.get('atrophy_score', 0.0)

        summary_text = f"""
AI Classification: {predicted_class}
Confidence Level: {confidence:.1%}
Atrophy Severity Score: {atrophy_score:.2f}/1.0

Clinical Interpretation:
"""

        if predicted_class == 'CN':
            summary_text += "• No significant cognitive impairment detected\n• Brain structure within normal limits\n• Routine follow-up recommended"
        elif predicted_class == 'MCI':
            summary_text += "• Mild Cognitive Impairment pattern identified\n• Intermediate atrophy between normal and AD\n• Enhanced monitoring recommended\n• Consider neuropsychological evaluation"
        elif predicted_class == 'AD':
            summary_text += "• Alzheimer's Disease pattern detected\n• Significant brain atrophy present\n• Clinical correlation strongly recommended\n• Comprehensive dementia workup indicated"

        ax.text(0.05, 0.70, summary_text, fontsize=11, va='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.3))

        # Key Metrics
        ax.text(0.05, 0.45, "KEY CLINICAL METRICS", fontsize=14, fontweight='bold')

        # Clinical scores if available
        if 'clinical_scores' in predictions:
            clinical_scores = predictions['clinical_scores']
            metrics_text = f"""
Automated MTA Score: {clinical_scores.get('MTA_score', 0):.1f}/4.0
Automated GCA Score: {clinical_scores.get('GCA_score', 0):.1f}/3.0
Automated Koedam Score: {clinical_scores.get('Koedam_score', 0):.1f}/3.0
"""
        else:
            metrics_text = "Clinical scores not available in this analysis."

        ax.text(0.05, 0.40, metrics_text, fontsize=11, va='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.3))

        # Disclaimer
        disclaimer = """
IMPORTANT DISCLAIMER:
This AI-generated report is intended for radiologist assistance only.
Clinical correlation and professional medical interpretation are required.
This tool should not be used as the sole basis for clinical decisions.
"""
        ax.text(0.05, 0.15, disclaimer, fontsize=9, va='top', style='italic',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.5))

        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _create_clinical_assessment_page(self, pdf, predictions: Dict,
                                       clinical_features: Dict, interpretability_result: Dict):
        """Create clinical assessment page"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(8.5, 11))
        fig.suptitle('Clinical Assessment Details', fontsize=16, fontweight='bold')

        # Regional atrophy analysis
        if 'regional_analysis' in interpretability_result:
            regional_analysis = interpretability_result['regional_analysis']
            regions = list(regional_analysis.keys())
            saliency_scores = [regional_analysis[r]['mean_saliency'] for r in regions]

            ax1.barh(regions, saliency_scores, color='skyblue')
            ax1.set_xlabel('Mean Saliency Score')
            ax1.set_title('Regional Atrophy Analysis')
            ax1.set_xlim(0, 1)

        # Clinical scores comparison
        if 'clinical_scores' in predictions:
            clinical_scores = predictions['clinical_scores']
            scores = ['MTA', 'GCA', 'Koedam']
            values = [clinical_scores.get(f'{s}_score', 0) for s in scores]
            max_values = [4, 3, 3]  # Maximum values for each score

            x = np.arange(len(scores))
            ax2.bar(x, values, color='lightcoral', alpha=0.7, label='Current')
            ax2.bar(x, max_values, color='gray', alpha=0.3, label='Maximum')
            ax2.set_xlabel('Clinical Scores')
            ax2.set_ylabel('Score Value')
            ax2.set_title('Automated Clinical Scoring')
            ax2.set_xticks(x)
            ax2.set_xticklabels(scores)
            ax2.legend()

        # Volumetric features
        if clinical_features:
            feature_names = ['total_brain_volume', 'estimated_hippocampal_volume', 'brain_parenchymal_fraction']
            feature_values = [clinical_features.get(f, 0) for f in feature_names]

            ax3.bar(range(len(feature_names)), feature_values, color='lightgreen')
            ax3.set_xlabel('Volumetric Features')
            ax3.set_ylabel('Value')
            ax3.set_title('Quantitative Brain Measures')
            ax3.set_xticks(range(len(feature_names)))
            ax3.set_xticklabels([f.replace('_', ' ').title() for f in feature_names], rotation=45)

        # Confidence and probability distribution
        if 'class_probabilities' in predictions:
            probs = predictions['class_probabilities']
            classes = list(probs.keys())
            prob_values = list(probs.values())

            colors = ['green', 'orange', 'red']
            ax4.pie(prob_values, labels=classes, colors=colors, autopct='%1.1f%%')
            ax4.set_title('Classification Probabilities')

        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _create_imaging_analysis_page(self, pdf, mri_data: np.ndarray, interpretability_result: Dict):
        """Create imaging analysis page with heatmaps"""
        fig, axes = plt.subplots(2, 3, figsize=(8.5, 11))
        fig.suptitle('Imaging Analysis - Atrophy Heatmaps', fontsize=16, fontweight='bold')

        if interpretability_result and 'heatmap_data' in interpretability_result:
            heatmap_data = interpretability_result['heatmap_data']

            # Get middle slices
            mid_z = heatmap_data.shape[2] // 2
            mid_y = heatmap_data.shape[1] // 2
            mid_x = heatmap_data.shape[0] // 2

            # Original MRI slices (top row)
            axes[0, 0].imshow(mri_data[:, :, mid_z], cmap='gray')
            axes[0, 0].set_title(f'Axial - Original (Z={mid_z})')
            axes[0, 0].axis('off')

            axes[0, 1].imshow(mri_data[:, mid_y, :], cmap='gray')
            axes[0, 1].set_title(f'Coronal - Original (Y={mid_y})')
            axes[0, 1].axis('off')

            axes[0, 2].imshow(mri_data[mid_x, :, :], cmap='gray')
            axes[0, 2].set_title(f'Sagittal - Original (X={mid_x})')
            axes[0, 2].axis('off')

            # Heatmap overlays (bottom row)
            im1 = axes[1, 0].imshow(heatmap_data[:, :, mid_z], cmap='hot', alpha=0.8)
            axes[1, 0].set_title('Axial - Atrophy Heatmap')
            axes[1, 0].axis('off')

            axes[1, 1].imshow(heatmap_data[:, mid_y, :], cmap='hot', alpha=0.8)
            axes[1, 1].set_title('Coronal - Atrophy Heatmap')
            axes[1, 1].axis('off')

            axes[1, 2].imshow(heatmap_data[mid_x, :, :], cmap='hot', alpha=0.8)
            axes[1, 2].set_title('Sagittal - Atrophy Heatmap')
            axes[1, 2].axis('off')

            # Add colorbar
            plt.colorbar(im1, ax=axes[1, :], orientation='horizontal',
                        fraction=0.05, pad=0.1, label='Atrophy Severity')

        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _create_mci_specific_page(self, pdf, predictions: Dict,
                                clinical_features: Dict, interpretability_result: Dict):
        """Create MCI-specific analysis page"""
        fig, ax = plt.subplots(figsize=(8.5, 11))
        ax.axis('off')

        ax.text(0.5, 0.95, 'MCI-SPECIFIC ANALYSIS',
                ha='center', va='top', fontsize=16, fontweight='bold')

        # MCI characteristics
        mci_analysis = f"""
MILD COGNITIVE IMPAIRMENT ASSESSMENT

Current Status:
• Classification: {predictions.get('predicted_class', 'MCI')}
• Confidence: {predictions.get('confidence', 0.0):.1%}
• Atrophy Score: {predictions.get('atrophy_score', 0.0):.2f} (Intermediate level)

Key MCI Indicators:
• Hippocampal atrophy present but not severe
• Preserved overall brain structure
• Intermediate between normal aging and AD
• Pattern consistent with prodromal AD

Risk Assessment:
"""

        atrophy_score = predictions.get('atrophy_score', 0.0)
        if atrophy_score < 0.4:
            mci_analysis += "• LOW risk of progression to AD within 2 years\n"
        elif atrophy_score < 0.6:
            mci_analysis += "• MODERATE risk of progression to AD within 2 years\n"
        else:
            mci_analysis += "• HIGH risk of progression to AD within 2 years\n"

        mci_analysis += """
Monitoring Recommendations:
• Follow-up MRI in 6-12 months
• Neuropsychological testing
• Consider biomarker evaluation (CSF/PET)
• Lifestyle interventions (exercise, cognitive training)
• Monitor for functional decline

Clinical Correlation:
• Correlate with cognitive testing scores
• Assess functional independence
• Review medication effects
• Consider comorbidities
"""

        ax.text(0.05, 0.90, mci_analysis, fontsize=11, va='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.3))

        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _create_recommendations_page(self, pdf, predictions: Dict, clinical_features: Dict):
        """Create recommendations and follow-up page"""
        fig, ax = plt.subplots(figsize=(8.5, 11))
        ax.axis('off')

        ax.text(0.5, 0.95, 'CLINICAL RECOMMENDATIONS',
                ha='center', va='top', fontsize=16, fontweight='bold')

        predicted_class = predictions.get('predicted_class', 'Unknown')

        recommendations = f"""
IMMEDIATE ACTIONS:

"""

        if predicted_class == 'CN':
            recommendations += """• Continue routine clinical care
• Reassure patient about normal findings
• Maintain healthy lifestyle practices
• Consider baseline for future comparison"""
        elif predicted_class == 'MCI':
            recommendations += """• Comprehensive neuropsychological evaluation
• Assess functional status and daily activities
• Consider biomarker testing (if available)
• Initiate cognitive monitoring program
• Discuss prognosis and planning with patient/family"""
        elif predicted_class == 'AD':
            recommendations += """• Urgent clinical correlation required
• Complete dementia workup indicated
• Consider specialist referral (neurology/geriatrics)
• Discuss diagnosis and prognosis with family
• Initiate appropriate medical management"""

        recommendations += f"""

FOLLOW-UP SCHEDULE:

"""

        if predicted_class == 'CN':
            recommendations += "• Next imaging: 2-3 years (routine screening)\n• Clinical follow-up: Annual"
        elif predicted_class == 'MCI':
            recommendations += "• Next imaging: 6-12 months\n• Clinical follow-up: Every 3-6 months\n• Cognitive testing: Every 6 months"
        elif predicted_class == 'AD':
            recommendations += "• Next imaging: 6 months (assess progression)\n• Clinical follow-up: Monthly initially\n• Ongoing specialist care"

        recommendations += """

ADDITIONAL CONSIDERATIONS:

• Review current medications for cognitive effects
• Assess and treat vascular risk factors
• Encourage physical exercise and social engagement
• Consider genetic counseling if family history present
• Discuss advance directives and care planning

QUALITY ASSURANCE:

This AI analysis should be reviewed by a qualified radiologist.
Clinical correlation with patient history and examination is essential.
Consider additional imaging or testing if clinical suspicion differs from AI assessment.
"""

        ax.text(0.05, 0.90, recommendations, fontsize=10, va='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.3))

        # Footer
        footer_text = f"""
Report generated by Demetify AI System
{self.report_template['institution']}
Project Lead: {self.report_template['project_lead']}
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
        ax.text(0.5, 0.05, footer_text, ha='center', va='bottom', fontsize=8, style='italic')

        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

# Export functions
__all__ = [
    'ClinicalFeatureExtractor',
    'EnhancedPDFReportGenerator'
]
