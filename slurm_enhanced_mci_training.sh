#!/bin/bash
#SBATCH --job-name=enhanced_mci_training
#SBATCH --account=sridhar-ic
#SBATCH --partition=gpu
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=32
#SBATCH --gres=gpu:A100:4
#SBATCH --mem=128G
#SBATCH --time=24:00:00
#SBATCH --output=logs/enhanced_mci_training_%j.out
#SBATCH --error=logs/enhanced_mci_training_%j.err
#SBATCH --mail-type=BEGIN,END,FAIL
#SBATCH --mail-user=<EMAIL>

# Enhanced MCI Model Training on Illinois Campus Cluster
# Optimized for 4x A100 GPUs with parallel cross-validation training

echo "🧠 Enhanced MCI Model Training - Illinois Campus Cluster"
echo "========================================================"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "GPUs allocated: $CUDA_VISIBLE_DEVICES"
echo ""

# Set up environment
PROJECT_DIR="/projects/illinois/cob/ba/sridhar"
WORK_DIR="$PROJECT_DIR/enhanced_mci_training"
ENV_NAME="enhanced_mci"

# Navigate to working directory
cd "$WORK_DIR" || exit 1
echo "📁 Working directory: $(pwd)"

# Load required modules
echo "📦 Loading modules..."
module load anaconda3
module list

# Activate enhanced conda environment
echo "🐍 Activating conda environment: $ENV_NAME"
source activate "$ENV_NAME"

# Verify environment
echo "🔍 Environment verification:"
echo "  Python: $(python --version)"
echo "  PyTorch: $(python -c 'import torch; print(torch.__version__)')"
echo "  CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "  GPU count: $(python -c 'import torch; print(torch.cuda.device_count())')"

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:${WORK_DIR}"
export CUDA_LAUNCH_BLOCKING=1
export TORCH_DISTRIBUTED_DEBUG=INFO

# Create necessary directories
mkdir -p {logs,checkpoints,results,tensorboard}

echo ""
echo "🚀 Starting Enhanced MCI Model Training..."
echo "=========================================="

# Training configuration
CROSS_FOLDS=5
TASKS="ATROPHY_CONTINUOUS,DEMENTIA_CLASSIFICATION,COG_SCORE,REGIONAL_ATROPHY,CLINICAL_SCORES"

# Function to train single fold
train_fold() {
    local fold=$1
    local gpu_id=$2
    
    echo "🔄 Training fold $fold on GPU $gpu_id"
    
    CUDA_VISIBLE_DEVICES=$gpu_id python enhanced_mci_training.py \
        --fold $fold \
        --tasks $TASKS \
        --config enhanced_task_config.json \
        --output_dir "checkpoints/fold_$fold" \
        --log_dir "tensorboard/fold_$fold" \
        --batch_size 16 \
        --epochs 100 \
        --learning_rate 0.001 \
        --device cuda \
        --mixed_precision \
        --gradient_checkpointing \
        2>&1 | tee "logs/training_fold_${fold}_${SLURM_JOB_ID}.log"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo "✅ Fold $fold completed successfully"
    else
        echo "❌ Fold $fold failed with exit code $exit_code"
    fi
    
    return $exit_code
}

# Parallel training across 4 GPUs (5 folds, some GPUs will handle 2 folds)
echo "🔄 Starting parallel training across $CROSS_FOLDS folds..."

# Start background processes for each fold
pids=()
gpu_assignments=(0 1 2 3 0)  # GPU assignments for 5 folds

for fold in $(seq 0 $((CROSS_FOLDS-1))); do
    gpu_id=${gpu_assignments[$fold]}
    echo "Starting fold $fold on GPU $gpu_id..."
    
    train_fold $fold $gpu_id &
    pids+=($!)
    
    # Small delay to avoid resource conflicts
    sleep 10
done

# Wait for all training processes to complete
echo "⏳ Waiting for all training processes to complete..."
failed_folds=()

for i in "${!pids[@]}"; do
    pid=${pids[$i]}
    fold=$i
    
    if wait $pid; then
        echo "✅ Fold $fold (PID: $pid) completed successfully"
    else
        echo "❌ Fold $fold (PID: $pid) failed"
        failed_folds+=($fold)
    fi
done

# Check training results
if [ ${#failed_folds[@]} -eq 0 ]; then
    echo "🎉 All folds completed successfully!"
    training_success=true
else
    echo "⚠️ Failed folds: ${failed_folds[*]}"
    training_success=false
fi

# Generate training summary
echo ""
echo "📊 Generating training summary..."
python generate_training_summary.py \
    --checkpoint_dir checkpoints \
    --output_file "results/training_summary_${SLURM_JOB_ID}.json" \
    --failed_folds "${failed_folds[*]}"

# Model evaluation and validation
if [ "$training_success" = true ]; then
    echo ""
    echo "🔍 Running model evaluation..."
    
    python evaluate_enhanced_model.py \
        --checkpoint_dir checkpoints \
        --test_data_dir data/test \
        --output_dir "results/evaluation_${SLURM_JOB_ID}" \
        --generate_interpretability \
        --create_clinical_reports
    
    # Generate comprehensive heatmaps
    echo "🧠 Generating comprehensive interpretability heatmaps..."
    python generate_comprehensive_heatmaps.py \
        --model_checkpoints checkpoints \
        --test_samples data/test_samples \
        --output_dir "results/heatmaps_${SLURM_JOB_ID}"
fi

# Cleanup intermediate files (keep only essential results)
echo ""
echo "🧹 Cleaning up intermediate files..."
find tensorboard -name "*.tmp" -delete 2>/dev/null || true
find checkpoints -name "*.temp" -delete 2>/dev/null || true

# Final summary
echo ""
echo "📋 Training Job Summary"
echo "======================"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $start_time"
echo "End time: $(date)"
echo "Duration: $(($(date +%s) - $(date -d "$start_time" +%s))) seconds"
echo "Training success: $training_success"
echo "Failed folds: ${failed_folds[*]:-None}"
echo ""

# Resource usage summary
echo "💻 Resource Usage Summary:"
echo "  Max memory used: $(sacct -j $SLURM_JOB_ID --format=MaxRSS --noheader | head -1)"
echo "  GPU utilization: Check nvidia-smi logs"
echo ""

# Archive results
echo "📦 Archiving results..."
tar -czf "results/enhanced_mci_training_${SLURM_JOB_ID}.tar.gz" \
    checkpoints/ results/ logs/ tensorboard/ \
    --exclude="*.tmp" --exclude="*.temp"

echo "✅ Results archived: results/enhanced_mci_training_${SLURM_JOB_ID}.tar.gz"

# Final status
if [ "$training_success" = true ]; then
    echo "🎉 Enhanced MCI training completed successfully!"
    exit 0
else
    echo "❌ Enhanced MCI training completed with errors"
    exit 1
fi
