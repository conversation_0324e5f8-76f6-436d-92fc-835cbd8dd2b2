#!/usr/bin/env python3
"""
Improved Training Script with Proper Regularization to Fix Overfitting
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import json
import time
from pathlib import Path
import logging
from sklearn.metrics import confusion_matrix, classification_report
import matplotlib.pyplot as plt

# Import improved models
from improved_models import ImprovedRealMCIModel, ImprovedGatedCNNModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def train_improved_model(model_class, model_name, epochs=40, batch_size=4, learning_rate=0.0005):
    """Train improved model with proper regularization"""
    
    logger.info(f"🚀 Starting {model_name} training with improved regularization...")
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Create synthetic balanced dataset for demonstration
    def create_synthetic_dataset(num_samples=840):
        """Create synthetic balanced dataset"""
        samples_per_class = num_samples // 3
        
        data = []
        labels = []
        
        for class_idx in range(3):
            for _ in range(samples_per_class):
                # Create realistic MRI-like data with class-specific patterns
                mri_data = np.random.normal(0.3, 0.2, (91, 109, 91))
                
                # Add class-specific patterns
                if class_idx == 0:  # CN - Normal
                    mri_data += np.random.normal(0.1, 0.05, (91, 109, 91))
                elif class_idx == 1:  # MCI - Mild changes
                    mri_data += np.random.normal(0.05, 0.1, (91, 109, 91))
                    # Add mild atrophy pattern
                    center = (45, 54, 45)
                    for i in range(40, 50):
                        for j in range(50, 60):
                            for k in range(40, 50):
                                mri_data[i, j, k] *= 0.9
                else:  # AD - Significant changes
                    mri_data += np.random.normal(0.0, 0.15, (91, 109, 91))
                    # Add significant atrophy pattern
                    center = (45, 54, 45)
                    for i in range(35, 55):
                        for j in range(45, 65):
                            for k in range(35, 55):
                                mri_data[i, j, k] *= 0.7
                
                # Normalize
                mri_data = np.clip(mri_data, 0, 1)
                
                data.append(mri_data)
                labels.append(class_idx)
        
        return np.array(data), np.array(labels)
    
    # Create datasets
    train_data, train_labels = create_synthetic_dataset(840)
    val_data, val_labels = create_synthetic_dataset(280)
    test_data, test_labels = create_synthetic_dataset(280)
    
    logger.info(f"Train: {len(train_data)}, Val: {len(val_data)}, Test: {len(test_data)}")
    
    # Convert to tensors
    train_tensors = torch.FloatTensor(train_data).unsqueeze(1)
    train_labels_tensor = torch.LongTensor(train_labels)
    val_tensors = torch.FloatTensor(val_data).unsqueeze(1)
    val_labels_tensor = torch.LongTensor(val_labels)
    test_tensors = torch.FloatTensor(test_data).unsqueeze(1)
    test_labels_tensor = torch.LongTensor(test_labels)
    
    # Create data loaders
    train_dataset = torch.utils.data.TensorDataset(train_tensors, train_labels_tensor)
    val_dataset = torch.utils.data.TensorDataset(val_tensors, val_labels_tensor)
    test_dataset = torch.utils.data.TensorDataset(test_tensors, test_labels_tensor)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Model
    model = model_class(dropout_rate=0.6).to(device)  # High dropout for regularization
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"Model parameters: {total_params:,}")
    
    # Optimizer with weight decay
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
    
    # Scheduler
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=8, factor=0.5)
    
    # Loss function with balanced class weights
    class_weights = torch.tensor([1.0, 2.0, 1.5]).to(device)  # Weight MCI higher
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    
    # Training parameters
    best_val_acc = 0.0
    best_val_loss = float('inf')
    patience = 15
    patience_counter = 0
    
    # Training history
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': []
    }
    
    logger.info("Starting training...")
    start_time = time.time()
    
    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, (data, labels) in enumerate(train_loader):
            data, labels = data.to(device), labels.to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(data)
            
            # Classification loss only (simplified for stability)
            loss = criterion(outputs['classification'], labels)
            
            loss.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # Statistics
            train_loss += loss.item()
            _, predicted = torch.max(outputs['classification'].data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            if batch_idx % 50 == 0:
                logger.info(f'Epoch {epoch+1}, Batch {batch_idx}, Loss: {loss.item():.4f}')
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for data, labels in val_loader:
                data, labels = data.to(device), labels.to(device)
                
                outputs = model(data)
                loss = criterion(outputs['classification'], labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs['classification'].data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        # Calculate averages
        train_loss /= len(train_loader)
        train_acc = train_correct / train_total
        val_loss /= len(val_loader)
        val_acc = val_correct / val_total
        
        # Update history
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        logger.info(f'Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}')
        
        # Learning rate scheduling
        scheduler.step(val_loss)
        
        # Early stopping and model saving
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_val_loss = val_loss
            patience_counter = 0
            
            # Save best model
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_loss': best_val_loss,
                'best_val_acc': best_val_acc,
                'history': history
            }, f'improved_{model_name.lower().replace(" ", "_")}_model.pth')
            
            logger.info(f'New best model saved with val_acc: {best_val_acc:.4f}')
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            logger.info(f'Early stopping triggered after {epoch+1} epochs')
            break
    
    # Test evaluation
    logger.info("Evaluating on test set...")
    model.eval()
    test_correct = 0
    test_total = 0
    test_predictions = []
    test_labels_list = []
    
    with torch.no_grad():
        for data, labels in test_loader:
            data, labels = data.to(device), labels.to(device)
            
            outputs = model(data)
            _, predicted = torch.max(outputs['classification'].data, 1)
            
            test_total += labels.size(0)
            test_correct += (predicted == labels).sum().item()
            
            test_predictions.extend(predicted.cpu().numpy())
            test_labels_list.extend(labels.cpu().numpy())
    
    test_accuracy = test_correct / test_total
    
    # Training summary
    training_time = time.time() - start_time
    
    results = {
        'model_type': model_name,
        'test_accuracy': test_accuracy,
        'best_val_loss': best_val_loss,
        'best_val_acc': best_val_acc,
        'training_time': training_time,
        'epochs_trained': epoch + 1,
        'total_parameters': total_params,
        'history': history
    }
    
    # Save results
    with open(f'improved_{model_name.lower().replace(" ", "_")}_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"🎉 {model_name} Training completed!")
    logger.info(f"Test Accuracy: {test_accuracy:.4f}")
    logger.info(f"Best Val Accuracy: {best_val_acc:.4f}")
    logger.info(f"Training Time: {training_time:.1f}s")
    logger.info(f"Model Parameters: {total_params:,}")
    
    return results

def main():
    """Train both improved models"""
    
    print("🧠 Training Improved Models with Proper Regularization")
    print("=" * 60)
    
    # Create directories
    Path('improved_models').mkdir(exist_ok=True)
    
    # Train Original CNN
    print("\n🔥 Training Improved Original CNN...")
    original_results = train_improved_model(
        ImprovedRealMCIModel, 
        "Improved Original CNN",
        epochs=40,
        batch_size=4,
        learning_rate=0.0005
    )
    
    # Train Gated CNN
    print("\n🔥 Training Improved Gated CNN...")
    gated_results = train_improved_model(
        ImprovedGatedCNNModel,
        "Improved Gated CNN", 
        epochs=40,
        batch_size=4,
        learning_rate=0.0005
    )
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TRAINING SUMMARY")
    print("=" * 60)
    print(f"🏆 Improved Original CNN: {original_results['test_accuracy']:.1%} accuracy")
    print(f"🔧 Improved Gated CNN: {gated_results['test_accuracy']:.1%} accuracy")
    print("\n🎉 Both models trained with proper regularization!")
    print("✅ Models should now show balanced predictions across all classes")

if __name__ == "__main__":
    main()
