#!/usr/bin/env python3
"""
Standalone heatmap visualization tool to test if heatmaps are working
"""

import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
from pathlib import Path

def create_test_heatmap_overlay(mri_data, heatmap):
    """Create a test heatmap overlay that DEFINITELY shows red/yellow"""
    
    # Get middle slices
    mid_x, mid_y, mid_z = mri_data.shape[0]//2, mri_data.shape[1]//2, mri_data.shape[2]//2
    
    # Create figure
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('🔥 HEATMAP OVERLAY TEST - Should Show RED/YELLOW', fontsize=16, fontweight='bold')
    
    # Normalize MRI for display
    mri_norm = (mri_data - mri_data.min()) / (mri_data.max() - mri_data.min())
    
    # Normalize heatmap
    if heatmap.max() > 0:
        heatmap_norm = heatmap / heatmap.max()
    else:
        heatmap_norm = heatmap
    
    # Create red-yellow colormap
    colors = [(0, 0, 0, 0), (1, 0, 0, 0.8), (1, 1, 0, 1.0)]  # Transparent -> Red -> Yellow
    heatmap_cmap = LinearSegmentedColormap.from_list('test_heatmap', colors, N=256)
    
    # AXIAL VIEW
    axial_mri = np.rot90(mri_norm[:, :, mid_z])
    axial_heatmap = np.rot90(heatmap_norm[:, :, mid_z])
    
    axes[0].imshow(axial_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
    # Use very low threshold to ensure visibility
    axial_heatmap_masked = np.ma.masked_where(axial_heatmap < 0.01, axial_heatmap)
    im1 = axes[0].imshow(axial_heatmap_masked, cmap=heatmap_cmap, aspect='equal', vmin=0, vmax=1)
    axes[0].set_title(f'Axial (Z={mid_z}) - Max: {axial_heatmap.max():.3f}', fontweight='bold')
    axes[0].axis('off')
    
    # CORONAL VIEW
    coronal_mri = np.rot90(mri_norm[:, mid_y, :])
    coronal_heatmap = np.rot90(heatmap_norm[:, mid_y, :])
    
    axes[1].imshow(coronal_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
    coronal_heatmap_masked = np.ma.masked_where(coronal_heatmap < 0.01, coronal_heatmap)
    im2 = axes[1].imshow(coronal_heatmap_masked, cmap=heatmap_cmap, aspect='equal', vmin=0, vmax=1)
    axes[1].set_title(f'Coronal (Y={mid_y}) - Max: {coronal_heatmap.max():.3f}', fontweight='bold')
    axes[1].axis('off')
    
    # SAGITTAL VIEW
    sagittal_mri = np.rot90(mri_norm[mid_x, :, :])
    sagittal_heatmap = np.rot90(heatmap_norm[mid_x, :, :])
    
    axes[2].imshow(sagittal_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
    sagittal_heatmap_masked = np.ma.masked_where(sagittal_heatmap < 0.01, sagittal_heatmap)
    im3 = axes[2].imshow(sagittal_heatmap_masked, cmap=heatmap_cmap, aspect='equal', vmin=0, vmax=1)
    axes[2].set_title(f'Sagittal (X={mid_x}) - Max: {sagittal_heatmap.max():.3f}', fontweight='bold')
    axes[2].axis('off')
    
    # Add colorbar
    cbar = plt.colorbar(im3, ax=axes, orientation='horizontal', fraction=0.05, pad=0.1, shrink=0.8)
    cbar.set_label('Heatmap Intensity (Should be RED/YELLOW)', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    return fig

def main():
    st.title("🔥 Heatmap Visualization Test Tool")
    st.markdown("**This tool tests if heatmaps are properly overlaid on MRI scans**")
    
    # Load test data
    demo_files = list(Path("demo_scans").glob("*.npy"))
    
    if demo_files:
        selected_file = st.selectbox("Select Demo MRI Scan", demo_files)
        
        if st.button("🧠 Generate and Test Heatmap Overlay"):
            with st.spinner("Generating heatmap..."):
                try:
                    # Load MRI data
                    mri_data = np.load(selected_file)
                    st.success(f"✅ Loaded MRI: {mri_data.shape}")
                    
                    # Generate heatmap using the engine
                    from final_mci_streamlit_app import FinalMCIInferenceEngine
                    engine = FinalMCIInferenceEngine()
                    
                    heatmap = engine.generate_model_heatmap(mri_data, "Final Improved CNN", 22.0)
                    
                    if heatmap is not None:
                        # Calculate statistics
                        total_voxels = np.prod(heatmap.shape)
                        active_voxels = np.sum(heatmap > 0.01)
                        activation_percentage = (active_voxels / total_voxels) * 100
                        
                        st.success(f"✅ Heatmap generated successfully!")
                        
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Heatmap Max", f"{heatmap.max():.3f}")
                        with col2:
                            st.metric("Activation %", f"{activation_percentage:.2f}%")
                        with col3:
                            st.metric("Non-zero voxels", f"{np.sum(heatmap > 0):,}")
                        
                        # Create and display overlay
                        fig = create_test_heatmap_overlay(mri_data, heatmap)
                        st.pyplot(fig, use_container_width=True)
                        plt.close()
                        
                        # Show heatmap statistics
                        st.markdown("### 📊 Heatmap Analysis")
                        
                        if heatmap.max() > 0.5:
                            st.success("🎉 **STRONG HEATMAP** - Should be clearly visible as red/yellow regions!")
                        elif heatmap.max() > 0.1:
                            st.warning("⚠️ **MODERATE HEATMAP** - Should be visible but may be faint")
                        else:
                            st.error("❌ **WEAK HEATMAP** - May not be visible")
                        
                        # Show slice-by-slice analysis
                        st.markdown("### 🔍 Slice Analysis")
                        mid_x, mid_y, mid_z = mri_data.shape[0]//2, mri_data.shape[1]//2, mri_data.shape[2]//2
                        
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            axial_max = np.max(heatmap[:, :, mid_z])
                            st.metric("Axial Max", f"{axial_max:.3f}")
                        with col2:
                            coronal_max = np.max(heatmap[:, mid_y, :])
                            st.metric("Coronal Max", f"{coronal_max:.3f}")
                        with col3:
                            sagittal_max = np.max(heatmap[mid_x, :, :])
                            st.metric("Sagittal Max", f"{sagittal_max:.3f}")
                        
                        # Test different models
                        st.markdown("### 🤖 Test Different Models")
                        
                        model_names = ["Final Improved CNN", "Advanced Ordinal CNN", "Gradient-Optimized CNN"]
                        
                        for model_name in model_names:
                            if st.button(f"Test {model_name}", key=model_name):
                                with st.spinner(f"Testing {model_name}..."):
                                    model_heatmap = engine.generate_model_heatmap(mri_data, model_name, 22.0)
                                    
                                    if model_heatmap is not None:
                                        model_activation = np.sum(model_heatmap > 0.01) / np.prod(model_heatmap.shape) * 100
                                        st.write(f"**{model_name}**: {model_activation:.2f}% activation, max: {model_heatmap.max():.3f}")
                                        
                                        # Quick visualization
                                        fig_model = create_test_heatmap_overlay(mri_data, model_heatmap)
                                        st.pyplot(fig_model, use_container_width=True)
                                        plt.close()
                                    else:
                                        st.error(f"Failed to generate heatmap for {model_name}")
                        
                    else:
                        st.error("❌ Failed to generate heatmap")
                        
                except Exception as e:
                    st.error(f"❌ Error: {str(e)}")
                    import traceback
                    st.code(traceback.format_exc())
    else:
        st.error("No demo files found in demo_scans/ folder")
        
    # Instructions
    st.markdown("---")
    st.markdown("""
    ### 📋 What to Look For:
    
    1. **Red/Yellow Regions**: Should appear overlaid on the gray MRI scan
    2. **Activation Percentage**: Should be 3-15% for visible heatmaps
    3. **Max Values**: Should be > 0.1 for good visibility
    4. **Slice Analysis**: At least one view should show clear activation
    
    If you don't see red/yellow regions, there's an issue with the overlay function.
    """)

if __name__ == "__main__":
    main()
