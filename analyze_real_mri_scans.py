#!/usr/bin/env python3
"""
Analyze real MRI scans from Windows E: drive
Identify T1 scans and extract NACCETPR labels
"""

import os
import nibabel as nib
import numpy as np
import pandas as pd
from pathlib import Path
import re

def analyze_nii_file(filepath):
    """
    Analyze a .nii file to determine if it's a T1 scan.
    
    Args:
        filepath: Path to .nii file
        
    Returns:
        dict: Analysis results
    """
    try:
        # Load the NIfTI file
        img = nib.load(filepath)
        data = img.get_fdata()
        header = img.header
        
        # Get basic info
        shape = data.shape
        voxel_size = header.get_zooms()
        data_type = header.get_data_dtype()
        
        # Analyze intensity characteristics (T1 has specific intensity patterns)
        intensity_stats = {
            'min': float(np.min(data)),
            'max': float(np.max(data)),
            'mean': float(np.mean(data)),
            'std': float(np.std(data))
        }
        
        # Check if it looks like a T1 scan based on characteristics
        is_likely_t1 = False
        t1_confidence = "Unknown"
        
        # T1 scans typically have:
        # - 3D volumes (3 dimensions)
        # - Reasonable brain dimensions
        # - Specific intensity characteristics
        if len(shape) == 3:
            # Check dimensions (typical brain MRI dimensions)
            if (100 < shape[0] < 300 and 100 < shape[1] < 300 and 100 < shape[2] < 300):
                # Check intensity range (T1 typically has good contrast)
                if intensity_stats['max'] > intensity_stats['mean'] * 3:
                    is_likely_t1 = True
                    t1_confidence = "High"
                elif intensity_stats['max'] > intensity_stats['mean'] * 2:
                    is_likely_t1 = True
                    t1_confidence = "Medium"
        
        # Extract sequence info from filename if possible
        filename = os.path.basename(filepath)
        sequence_hints = []
        
        # Look for T1 indicators in filename
        if any(indicator in filename.lower() for indicator in ['t1', 'mprage', 'spgr', 'fspgr']):
            sequence_hints.append("T1_in_filename")
            is_likely_t1 = True
            t1_confidence = "High"
        
        # Look for other sequence types
        if any(indicator in filename.lower() for indicator in ['t2', 'flair', 'dwi', 'dti']):
            sequence_hints.append("Non_T1_in_filename")
            is_likely_t1 = False
            t1_confidence = "Low"
        
        return {
            'filepath': filepath,
            'filename': filename,
            'shape': shape,
            'voxel_size': voxel_size,
            'data_type': str(data_type),
            'intensity_stats': intensity_stats,
            'is_likely_t1': is_likely_t1,
            't1_confidence': t1_confidence,
            'sequence_hints': sequence_hints,
            'file_size_mb': round(os.path.getsize(filepath) / (1024*1024), 2)
        }
        
    except Exception as e:
        return {
            'filepath': filepath,
            'filename': os.path.basename(filepath),
            'error': str(e),
            'is_likely_t1': False,
            't1_confidence': "Error"
        }

def extract_subject_id(filename):
    """
    Extract subject ID from filename for NACCETPR lookup.
    
    Args:
        filename: MRI filename
        
    Returns:
        str: Extracted subject ID or None
    """
    # Common patterns for subject IDs in NACC data
    patterns = [
        r'NACC(\d+)',  # NACC followed by numbers
        r'(\d{6,})',   # 6 or more consecutive digits
        r'mri(\d+)',   # mri followed by numbers
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            return match.group(1)
    
    return None

def find_naccetpr_labels():
    """
    Look for NACCETPR labels in CSV files.
    """
    # Search for CSV files that might contain NACCETPR
    csv_locations = [
        "/mnt/e/",
        "/home/<USER>/mri_frontend/ncomms2022/lookupcsv/",
        "/home/<USER>/mri_frontend/ncomms2022/FigureTable/",
    ]
    
    naccetpr_data = {}
    
    for location in csv_locations:
        if os.path.exists(location):
            for root, dirs, files in os.walk(location):
                for file in files:
                    if file.endswith('.csv'):
                        csv_path = os.path.join(root, file)
                        try:
                            df = pd.read_csv(csv_path)
                            # Check if this CSV has NACCETPR column
                            if 'NACCETPR' in df.columns:
                                print(f"Found NACCETPR in: {csv_path}")
                                # Store the data
                                for _, row in df.iterrows():
                                    if 'NACCID' in row:
                                        naccetpr_data[row['NACCID']] = row['NACCETPR']
                                    elif 'ID' in row:
                                        naccetpr_data[row['ID']] = row['NACCETPR']
                        except Exception as e:
                            continue
    
    return naccetpr_data

def analyze_mri_collection():
    """
    Analyze the MRI collection and create a comprehensive report.
    """
    print("🧠 Analyzing Real MRI Scans for T1 and NACCETPR Labels")
    print("=" * 60)
    
    # Directories to scan
    scan_directories = [
        "/mnt/e/MRI_scans_to_examine/",
        "/mnt/e/test_processed/",
        "/mnt/e/processed_data/"
    ]
    
    all_results = []
    
    # Find NACCETPR labels
    print("🔍 Searching for NACCETPR labels...")
    naccetpr_labels = find_naccetpr_labels()
    print(f"Found {len(naccetpr_labels)} NACCETPR entries")
    
    # Analyze each directory
    for directory in scan_directories:
        if not os.path.exists(directory):
            print(f"Directory not found: {directory}")
            continue
            
        print(f"\n📁 Analyzing: {directory}")
        
        # Find all .nii files
        nii_files = []
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.nii'):
                    nii_files.append(os.path.join(root, file))
        
        print(f"Found {len(nii_files)} .nii files")
        
        # Analyze each file
        for i, filepath in enumerate(nii_files[:20]):  # Limit to first 20 for speed
            print(f"Analyzing {i+1}/{min(20, len(nii_files))}: {os.path.basename(filepath)}")
            
            result = analyze_nii_file(filepath)
            
            # Try to match with NACCETPR
            subject_id = extract_subject_id(result['filename'])
            if subject_id and subject_id in naccetpr_labels:
                result['naccetpr'] = naccetpr_labels[subject_id]
                result['subject_id'] = subject_id
            else:
                result['naccetpr'] = None
                result['subject_id'] = subject_id
            
            all_results.append(result)
    
    # Filter for likely T1 scans
    t1_scans = [r for r in all_results if r.get('is_likely_t1', False)]
    
    print(f"\n📊 Analysis Summary:")
    print(f"Total files analyzed: {len(all_results)}")
    print(f"Likely T1 scans: {len(t1_scans)}")
    print(f"Files with NACCETPR labels: {len([r for r in all_results if r.get('naccetpr')])}")
    
    # Create detailed report
    if t1_scans:
        print(f"\n🎯 T1 Scans Found:")
        for scan in t1_scans:
            print(f"  ✅ {scan['filename']}")
            print(f"     Shape: {scan.get('shape', 'Unknown')}")
            print(f"     T1 Confidence: {scan.get('t1_confidence', 'Unknown')}")
            print(f"     NACCETPR: {scan.get('naccetpr', 'Not found')}")
            print(f"     Subject ID: {scan.get('subject_id', 'Not extracted')}")
            print(f"     Size: {scan.get('file_size_mb', 0)}MB")
            print()
    
    # Save results to CSV
    results_df = pd.DataFrame(all_results)
    output_path = "real_mri_analysis_results.csv"
    results_df.to_csv(output_path, index=False)
    print(f"📄 Detailed results saved to: {output_path}")
    
    return t1_scans, all_results

if __name__ == "__main__":
    t1_scans, all_results = analyze_mri_collection()
