#!/usr/bin/env python3
"""
Test the improved heatmap generation with different scans
"""

import numpy as np
import torch
from pathlib import Path
import json
import matplotlib.pyplot as plt

from mri_preprocessing_pipeline import MRIPreprocessingPipeline
from radiologist_focused_heatmap_generator import RadiologistF<PERSON>usedHeatmapGenerator
from nilearn_visualization_system import NilearnVisualizationSystem

def test_scan_specific_heatmaps():
    """Test that different scans produce different heatmaps"""
    
    print("🔥 Testing Improved Scan-Specific Heatmap Generation")
    print("=" * 60)
    
    # Initialize components
    preprocessing_pipeline = MRIPreprocessingPipeline()
    heatmap_generator = RadiologistFocusedHeatmapGenerator()
    visualization_system = NilearnVisualizationSystem()
    
    # Test with different cases
    test_cases = [
        ("experiment_25_scans/CASE_01_mri.npy", "CN", 28.5),
        ("experiment_25_scans/CASE_12_mri.npy", "MCI", 20.5),
        ("experiment_25_scans/CASE_18_mri.npy", "AD", 15.2)
    ]
    
    # Load metadata for realistic results
    try:
        with open('experiment_25_scans/experiment_metadata.json', 'r') as f:
            metadata = json.load(f)
        case_lookup = {case['mri_filename']: case for case in metadata['cases']}
    except:
        case_lookup = {}
    
    results = []
    
    for i, (test_case, expected_class, expected_mmse) in enumerate(test_cases):
        if not Path(test_case).exists():
            print(f"⚠️ Skipping {test_case} - file not found")
            continue
        
        print(f"\n{i+1}. Testing {test_case} ({expected_class})...")
        
        try:
            # Load and preprocess
            mri_data = preprocessing_pipeline.load_mri_file(test_case, 'npy')
            preprocessed = preprocessing_pipeline.preprocess_for_model(mri_data)
            viz_data = preprocessing_pipeline.create_nilearn_compatible_data(mri_data)
            
            # Get realistic inference results from metadata
            filename = Path(test_case).name
            if filename in case_lookup:
                case_info = case_lookup[filename]
                predicted_class = case_info['ai_prediction']['predicted_class']
                mmse_score = case_info['ai_prediction']['mmse_score']
                predicted_label = case_info['ai_prediction']['predicted_label']
            else:
                # Use expected values as fallback
                class_map = {'CN': 0, 'MCI': 1, 'AD': 2}
                predicted_class = class_map[expected_class]
                mmse_score = expected_mmse
                predicted_label = expected_class
            
            print(f"   📊 Inference: {predicted_label} (MMSE: {mmse_score:.1f})")
            
            # Generate heatmap
            class MockModel:
                def eval(self):
                    pass
                def __call__(self, x):
                    return torch.randn(x.shape[0], 3)
            
            model = MockModel()
            
            clinical_heatmap = heatmap_generator.generate_clinical_heatmap(
                model,
                preprocessed['preprocessed_tensor'],
                predicted_class,
                mmse_score
            )
            
            # Calculate statistics
            activation_pct = np.sum(clinical_heatmap > 0.1) / clinical_heatmap.size * 100
            max_activation = np.max(clinical_heatmap)
            mean_activation = np.mean(clinical_heatmap[clinical_heatmap > 0])
            
            print(f"   🔥 Heatmap stats:")
            print(f"      - Activation: {activation_pct:.2f}%")
            print(f"      - Max value: {max_activation:.3f}")
            print(f"      - Mean active: {mean_activation:.3f}")
            
            # Create visualization
            overlay_views = visualization_system.create_heatmap_overlay(
                viz_data['data'],
                clinical_heatmap,
                viz_data['affine'],
                f"{predicted_label} - MMSE {mmse_score:.1f}",
                opacity=0.7
            )
            
            # Save output
            output_dir = Path("improved_heatmap_test")
            output_dir.mkdir(exist_ok=True)
            
            output_file = output_dir / f"case_{i+1}_{predicted_label}_MMSE{mmse_score:.0f}_improved.png"
            visualization_system.save_figure_as_image(overlay_views['axial'], str(output_file))
            
            print(f"   💾 Saved: {output_file}")
            
            # Store results
            results.append({
                'case': filename,
                'predicted_class': predicted_label,
                'mmse_score': mmse_score,
                'activation_pct': activation_pct,
                'max_activation': max_activation,
                'mean_activation': mean_activation,
                'output_file': str(output_file),
                'heatmap_unique_values': len(np.unique(clinical_heatmap[clinical_heatmap > 0]))
            })
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
    
    # Analysis
    print("\n" + "=" * 60)
    print("📊 HEATMAP COMPARISON ANALYSIS")
    print("=" * 60)
    
    if len(results) >= 2:
        print("\n🔍 Uniqueness Test:")
        for i in range(len(results)):
            for j in range(i+1, len(results)):
                r1, r2 = results[i], results[j]
                
                # Check if activation levels are different
                activation_diff = abs(r1['activation_pct'] - r2['activation_pct'])
                max_diff = abs(r1['max_activation'] - r2['max_activation'])
                
                print(f"   {r1['predicted_class']} vs {r2['predicted_class']}:")
                print(f"      - Activation difference: {activation_diff:.2f}%")
                print(f"      - Max value difference: {max_diff:.3f}")
                print(f"      - Unique values: {r1['heatmap_unique_values']} vs {r2['heatmap_unique_values']}")
    
    print("\n📈 Individual Results:")
    for result in results:
        print(f"   {result['case']} ({result['predicted_class']}):")
        print(f"      - MMSE: {result['mmse_score']:.1f}")
        print(f"      - Activation: {result['activation_pct']:.2f}%")
        print(f"      - Max activation: {result['max_activation']:.3f}")
        print(f"      - Unique values: {result['heatmap_unique_values']}")
    
    # Assessment
    avg_activation = np.mean([r['activation_pct'] for r in results])
    activation_range = max([r['activation_pct'] for r in results]) - min([r['activation_pct'] for r in results])
    
    print(f"\n🎯 Overall Assessment:")
    print(f"   - Average activation: {avg_activation:.2f}%")
    print(f"   - Activation range: {activation_range:.2f}%")
    print(f"   - Target range: 2-5% ✅" if 2 <= avg_activation <= 5 else f"   - Target range: 2-5% ❌")
    print(f"   - Scan uniqueness: {'✅' if activation_range > 0.5 else '❌'}")
    
    if avg_activation >= 2 and activation_range > 0.5:
        print("\n🎉 IMPROVED HEATMAPS SUCCESSFUL!")
        print("   - Better visibility (2-5% activation)")
        print("   - Scan-specific generation")
        print("   - Clinical region focus")
    else:
        print("\n⚠️ Further improvements needed")
    
    # Save summary
    with open('improved_heatmap_test/test_summary.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to improved_heatmap_test/")
    
    # Cleanup
    visualization_system.cleanup_temp_files()
    
    return avg_activation >= 2 and activation_range > 0.5

if __name__ == "__main__":
    success = test_scan_specific_heatmaps()
    print(f"\n{'🎉 SUCCESS' if success else '❌ NEEDS IMPROVEMENT'}")
    exit(0 if success else 1)
