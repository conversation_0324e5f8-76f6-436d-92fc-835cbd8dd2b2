#!/usr/bin/env python3
"""
Test Demo Functionality
Verify that demo examples are properly integrated
"""

import json
import numpy as np
from pathlib import Path

def test_demo_integration():
    """Test that demo examples are properly set up"""
    
    print("🎯 TESTING DEMO EXAMPLES INTEGRATION")
    print("=" * 50)
    
    # Check demo folder
    demo_folder = Path("local_demo_scans")
    if not demo_folder.exists():
        print("❌ Demo folder not found!")
        return False
    
    print(f"✅ Demo folder found: {demo_folder}")
    
    # Check metadata
    metadata_file = demo_folder / "collection_metadata.json"
    if not metadata_file.exists():
        print("❌ Demo metadata not found!")
        return False
    
    with open(metadata_file, 'r') as f:
        metadata = json.load(f)
    
    print(f"✅ Demo metadata loaded: {len(metadata['scans'])} cases")
    
    # Check demo files
    missing_files = []
    for scan in metadata['scans']:
        npy_file = demo_folder / scan['numpy_file']
        nii_file = demo_folder / scan['nifti_file']
        
        if not npy_file.exists():
            missing_files.append(scan['numpy_file'])
        if not nii_file.exists():
            missing_files.append(scan['nifti_file'])
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print(f"✅ All demo files present")
    
    # Test loading a demo case
    test_case = metadata['scans'][0]
    test_file = demo_folder / test_case['numpy_file']
    
    try:
        test_data = np.load(test_file)
        print(f"✅ Test case loaded: {test_case['case_id']}")
        print(f"   Shape: {test_data.shape}")
        print(f"   Class: {test_case['class_label']}")
        print(f"   MMSE: {test_case['mmse_score']}")
        print(f"   Age: {test_case['age']}")
    except Exception as e:
        print(f"❌ Failed to load test case: {e}")
        return False
    
    # Show demo statistics
    print(f"\n📊 DEMO COLLECTION STATISTICS:")
    cn_cases = [s for s in metadata['scans'] if s['class_label'] == 'CN']
    mci_cases = [s for s in metadata['scans'] if s['class_label'] == 'MCI']
    ad_cases = [s for s in metadata['scans'] if s['class_label'] == 'AD']
    
    print(f"   CN Cases: {len(cn_cases)}")
    print(f"   MCI Cases: {len(mci_cases)}")
    print(f"   AD Cases: {len(ad_cases)}")
    print(f"   Total: {len(metadata['scans'])}")
    
    # Show MMSE ranges
    cn_mmse = [s['mmse_score'] for s in cn_cases]
    mci_mmse = [s['mmse_score'] for s in mci_cases]
    ad_mmse = [s['mmse_score'] for s in ad_cases]
    
    print(f"\n📈 MMSE SCORE RANGES:")
    if cn_mmse:
        print(f"   CN: {min(cn_mmse):.0f}-{max(cn_mmse):.0f}")
    if mci_mmse:
        print(f"   MCI: {min(mci_mmse):.0f}-{max(mci_mmse):.0f}")
    if ad_mmse:
        print(f"   AD: {min(ad_mmse):.0f}-{max(ad_mmse):.0f}")
    
    print(f"\n🎯 DEMO EXAMPLES READY FOR FRONTEND!")
    print(f"Users can now:")
    print(f"   • Click demo buttons to load cases")
    print(f"   • See expected vs actual results")
    print(f"   • Open demo folder with button")
    print(f"   • Test all CN/MCI/AD cases")
    
    return True

def show_demo_cases():
    """Show all available demo cases"""
    
    demo_folder = Path("local_demo_scans")
    metadata_file = demo_folder / "collection_metadata.json"
    
    if not metadata_file.exists():
        print("❌ Demo metadata not found")
        return
    
    with open(metadata_file, 'r') as f:
        metadata = json.load(f)
    
    print(f"\n📋 AVAILABLE DEMO CASES:")
    print("=" * 60)
    
    for scan in metadata['scans']:
        print(f"🔹 {scan['case_id']}")
        print(f"   Class: {scan['class_label']} | MMSE: {scan['mmse_score']} | Age: {scan['age']}")
        print(f"   Files: {scan['numpy_file']}, {scan['nifti_file']}")
        print()

def main():
    """Main test function"""
    
    success = test_demo_integration()
    
    if success:
        show_demo_cases()
        print(f"\n🎉 DEMO INTEGRATION TEST PASSED!")
        print(f"Frontend is ready with demo examples!")
    else:
        print(f"\n❌ DEMO INTEGRATION TEST FAILED!")
        print(f"Check demo folder and files")

if __name__ == "__main__":
    main()
