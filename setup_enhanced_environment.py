#!/usr/bin/env python3
"""
Enhanced Environment Setup for Illinois Cluster MCI Training
Sets up conda environment with all required packages for enhanced MCI model training
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedEnvironmentSetup:
    """Setup enhanced environment for MCI training on Illinois cluster"""
    
    def __init__(self, project_dir="/projects/illinois/cob/ba/sridhar"):
        self.project_dir = Path(project_dir)
        self.work_dir = self.project_dir / "enhanced_mci_training"
        self.env_name = "enhanced_mci"
        
        logger.info(f"Setting up enhanced environment: {self.env_name}")
        logger.info(f"Project directory: {self.project_dir}")
    
    def check_conda_availability(self):
        """Check if conda is available"""
        try:
            result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Conda available: {result.stdout.strip()}")
                return True
            else:
                logger.error("❌ Conda not available")
                return False
        except FileNotFoundError:
            logger.error("❌ Conda command not found")
            return False
    
    def find_existing_environment(self):
        """Find existing conda environment in hahmed directory"""
        logger.info("🔍 Looking for existing conda environment...")
        
        hahmed_dir = self.project_dir / "hahmed"
        if not hahmed_dir.exists():
            logger.warning("hahmed directory not found")
            return None
        
        # Look for conda environment files
        env_files = []
        env_files.extend(list(hahmed_dir.rglob("environment*.yml")))
        env_files.extend(list(hahmed_dir.rglob("requirements*.txt")))
        env_files.extend(list(hahmed_dir.rglob("*.yml")))
        
        if env_files:
            logger.info(f"Found {len(env_files)} environment files:")
            for env_file in env_files:
                logger.info(f"  📄 {env_file}")
            return env_files[0]  # Return first found
        
        # Check for conda environments directory
        conda_dirs = list(hahmed_dir.rglob("*conda*"))
        if conda_dirs:
            logger.info(f"Found conda directories: {conda_dirs}")
        
        return None
    
    def create_enhanced_environment(self):
        """Create enhanced conda environment"""
        logger.info(f"🐍 Creating enhanced conda environment: {self.env_name}")
        
        # Enhanced requirements for MCI training
        enhanced_requirements = """
# Enhanced MCI Training Requirements
name: enhanced_mci
channels:
  - conda-forge
  - pytorch
  - nvidia
dependencies:
  - python=3.9
  - pytorch>=1.12.0
  - torchvision
  - torchaudio
  - cudatoolkit=11.6
  - numpy>=1.21.0
  - pandas>=1.3.0
  - scikit-learn>=1.0.0
  - scipy>=1.7.0
  - matplotlib>=3.4.0
  - seaborn>=0.11.0
  - plotly>=5.0.0
  - streamlit>=1.28.0
  - nibabel>=3.2.0
  - nilearn>=0.9.0
  - opencv>=4.5.0
  - pillow>=8.3.0
  - tqdm>=4.62.0
  - jupyter
  - ipykernel
  - pip
  - pip:
    - shap>=0.41.0
    - captum>=0.5.0
    - monai>=0.9.0
    - dipy>=1.5.0
    - SimpleITK>=2.1.0
    - antspyx>=0.3.0
    - tensorboard>=2.8.0
    - wandb>=0.12.0
    - hydra-core>=1.1.0
    - omegaconf>=2.1.0
    - lightning>=1.6.0
"""
        
        # Save environment file
        env_file = self.work_dir / "enhanced_mci_environment.yml"
        with open(env_file, 'w') as f:
            f.write(enhanced_requirements)
        
        logger.info(f"📄 Environment file created: {env_file}")
        
        # Create conda environment
        try:
            cmd = f"conda env create -f {env_file} --name {self.env_name}"
            logger.info(f"Running: {cmd}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Enhanced conda environment created successfully")
                return True
            else:
                logger.error(f"❌ Failed to create environment: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error creating environment: {e}")
            return False
    
    def install_additional_packages(self):
        """Install additional packages for enhanced preprocessing"""
        logger.info("📦 Installing additional packages...")
        
        additional_packages = [
            "pip install hd-bet",  # HD-BET for skull stripping
            "pip install fsl-sub",  # FSL integration
            "pip install ants",     # ANTs for registration
            "pip install freesurfer-python",  # FreeSurfer integration
        ]
        
        for package_cmd in additional_packages:
            try:
                logger.info(f"Installing: {package_cmd}")
                result = subprocess.run(
                    f"conda activate {self.env_name} && {package_cmd}",
                    shell=True, capture_output=True, text=True
                )
                
                if result.returncode == 0:
                    logger.info(f"✅ Installed: {package_cmd}")
                else:
                    logger.warning(f"⚠️ Failed to install: {package_cmd}")
                    
            except Exception as e:
                logger.warning(f"⚠️ Error installing {package_cmd}: {e}")
    
    def create_activation_script(self):
        """Create activation script for easy environment setup"""
        activation_script = f"""#!/bin/bash
# Enhanced MCI Environment Activation Script
# Usage: source activate_enhanced_mci.sh

echo "🧠 Activating Enhanced MCI Environment"
echo "======================================"

# Load required modules
echo "📦 Loading modules..."
module load anaconda3

# Activate conda environment
echo "🐍 Activating conda environment: {self.env_name}"
conda activate {self.env_name}

# Set environment variables
export PROJECT_DIR="{self.project_dir}"
export WORK_DIR="{self.work_dir}"
export PYTHONPATH="${{PYTHONPATH}}:${{WORK_DIR}}"

# Navigate to working directory
cd "$WORK_DIR"

echo "✅ Enhanced MCI environment activated!"
echo "📁 Working directory: $(pwd)"
echo "🐍 Python version: $(python --version)"
echo "🔥 PyTorch version: $(python -c 'import torch; print(torch.__version__)')"

# Show available GPUs
if command -v nvidia-smi &> /dev/null; then
    echo "🖥️ Available GPUs:"
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader
fi

echo ""
echo "🚀 Ready for enhanced MCI training!"
"""
        
        script_file = self.work_dir / "activate_enhanced_mci.sh"
        with open(script_file, 'w') as f:
            f.write(activation_script)
        
        # Make executable
        os.chmod(script_file, 0o755)
        
        logger.info(f"📄 Activation script created: {script_file}")
        logger.info(f"Usage: source {script_file}")
        
        return script_file
    
    def create_requirements_backup(self):
        """Create requirements.txt backup"""
        requirements_txt = """
# Enhanced MCI Training Requirements
torch>=1.12.0
torchvision>=0.13.0
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
scipy>=1.7.0
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0
streamlit>=1.28.0
nibabel>=3.2.0
nilearn>=0.9.0
opencv-python>=4.5.0
Pillow>=8.3.0
tqdm>=4.62.0
shap>=0.41.0
captum>=0.5.0
monai>=0.9.0
dipy>=1.5.0
SimpleITK>=2.1.0
tensorboard>=2.8.0
wandb>=0.12.0
hydra-core>=1.1.0
omegaconf>=2.1.0
pytorch-lightning>=1.6.0
"""
        
        req_file = self.work_dir / "requirements_enhanced_mci.txt"
        with open(req_file, 'w') as f:
            f.write(requirements_txt)
        
        logger.info(f"📄 Requirements backup created: {req_file}")
        return req_file
    
    def verify_installation(self):
        """Verify the installation"""
        logger.info("🔍 Verifying installation...")
        
        test_imports = [
            "torch", "torchvision", "numpy", "pandas", "sklearn",
            "matplotlib", "plotly", "streamlit", "nibabel", "nilearn",
            "shap", "monai"
        ]
        
        failed_imports = []
        
        for package in test_imports:
            try:
                cmd = f"conda activate {self.env_name} && python -c 'import {package}; print(f\"{package}: {{getattr({package}, '__version__', 'unknown')}}\")'"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"✅ {result.stdout.strip()}")
                else:
                    failed_imports.append(package)
                    logger.error(f"❌ Failed to import {package}")
                    
            except Exception as e:
                failed_imports.append(package)
                logger.error(f"❌ Error testing {package}: {e}")
        
        if not failed_imports:
            logger.info("✅ All packages verified successfully!")
            return True
        else:
            logger.error(f"❌ Failed imports: {failed_imports}")
            return False

def main():
    """Main setup function"""
    logger.info("🚀 Starting enhanced environment setup...")
    
    setup = EnhancedEnvironmentSetup()
    
    try:
        # Check conda availability
        if not setup.check_conda_availability():
            logger.error("Please load anaconda3 module first: module load anaconda3")
            return False
        
        # Look for existing environment
        existing_env = setup.find_existing_environment()
        if existing_env:
            logger.info(f"Found existing environment file: {existing_env}")
        
        # Create enhanced environment
        if setup.create_enhanced_environment():
            logger.info("✅ Enhanced environment created")
        else:
            logger.error("❌ Failed to create enhanced environment")
            return False
        
        # Install additional packages
        setup.install_additional_packages()
        
        # Create activation script
        activation_script = setup.create_activation_script()
        
        # Create requirements backup
        req_file = setup.create_requirements_backup()
        
        # Verify installation
        if setup.verify_installation():
            logger.info("✅ Enhanced environment setup completed successfully!")
            
            print("\n" + "="*60)
            print("🎉 ENHANCED MCI ENVIRONMENT SETUP COMPLETE!")
            print("="*60)
            print(f"📁 Working directory: {setup.work_dir}")
            print(f"🐍 Environment name: {setup.env_name}")
            print(f"🚀 Activation script: {activation_script}")
            print(f"📄 Requirements: {req_file}")
            print("\n🔧 Next steps:")
            print(f"  1. source {activation_script}")
            print("  2. python explore_nacc_data.py")
            print("  3. Start enhanced MCI training")
            print("="*60)
            
            return True
        else:
            logger.error("❌ Environment verification failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
