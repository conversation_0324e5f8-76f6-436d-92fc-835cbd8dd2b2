#!/usr/bin/env python3
"""
Test Heatmap Generation
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from final_mci_streamlit_app import FinalMCIInferenceEngine
import time

def test_heatmap_generation():
    """Test the heatmap generation functionality"""
    
    print("🧠 Testing Heatmap Generation...")
    
    # Initialize inference engine
    engine = FinalMCIInferenceEngine()
    
    if engine.cnn_model is None and engine.gated_model is None:
        print("❌ No models loaded!")
        return False
    
    # Create test MRI data
    print("📊 Creating test MRI data...")
    test_mri = np.random.normal(0.4, 0.15, (91, 109, 91))
    
    # Test prediction with heatmap
    print("🔍 Running inference with heatmap generation...")
    start_time = time.time()
    
    try:
        results = engine.comprehensive_predict(test_mri)
        processing_time = time.time() - start_time
        
        print(f"✅ Processing completed in {processing_time:.2f}s")
        
        # Check results
        for model_name, model_results in results['models'].items():
            print(f"\n🤖 {model_name} Results:")
            print(f"   Cognitive Score: {model_results['cognitive_score']:.1f}/30")
            print(f"   Predicted Class: {['CN', 'MCI', 'AD'][model_results['predicted_class']]}")
            print(f"   Confidence: {model_results['confidence']:.3f}")
            print(f"   Atrophy Score: {model_results['atrophy_score']:.3f}")
            
            # Check heatmap
            if 'heatmap' in model_results:
                heatmap = model_results['heatmap']
                print(f"   Heatmap Shape: {heatmap.shape}")
                print(f"   Heatmap Range: {heatmap.min():.3f} - {heatmap.max():.3f}")
                print(f"   Heatmap Non-zero: {np.count_nonzero(heatmap)} / {heatmap.size}")
                
                # Save a slice for visualization
                mid_slice = heatmap[:, :, heatmap.shape[2]//2]
                plt.figure(figsize=(8, 6))
                plt.imshow(mid_slice, cmap='hot', alpha=0.7)
                plt.colorbar(label='Attention Intensity')
                plt.title(f'{model_name} - Heatmap (Axial Slice)')
                plt.axis('off')
                plt.savefig(f'{model_name.lower().replace(" ", "_")}_heatmap_test.png', dpi=150, bbox_inches='tight')
                plt.close()
                print(f"   ✅ Heatmap saved as {model_name.lower().replace(' ', '_')}_heatmap_test.png")
            else:
                print(f"   ❌ No heatmap generated!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_heatmap_visualization():
    """Test the heatmap visualization function"""
    
    print("\n🎨 Testing Heatmap Visualization...")
    
    try:
        from final_mci_streamlit_app import create_heatmap_visualization
        
        # Create dummy data
        mri_data = np.random.normal(0.5, 0.1, (91, 109, 91))
        heatmap_data = np.random.uniform(0, 0.5, (91, 109, 91))
        
        # Add some realistic patterns
        center = (45, 54, 45)
        for i in range(center[0]-10, center[0]+10):
            for j in range(center[1]-15, center[1]+15):
                for k in range(center[2]-10, center[2]+10):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                        if dist < 8:
                            heatmap_data[i, j, k] = 0.8 * np.exp(-dist/5)
        
        # Test visualization
        fig = create_heatmap_visualization(mri_data, heatmap_data, 22.5, "Test Model")
        
        print("✅ Heatmap visualization created successfully!")
        print(f"   Figure type: {type(fig)}")
        print(f"   Number of traces: {len(fig.data)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Visualization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("🧪 HEATMAP TESTING SUITE")
    print("=" * 40)
    
    # Test 1: Heatmap generation
    test1_success = test_heatmap_generation()
    
    # Test 2: Heatmap visualization
    test2_success = test_heatmap_visualization()
    
    print("\n📊 TEST RESULTS:")
    print("=" * 40)
    print(f"Heatmap Generation: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"Heatmap Visualization: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Heatmap functionality is working correctly")
        print("🚀 Ready for presentation!")
    else:
        print("\n⚠️ Some tests failed - check errors above")
    
    return test1_success and test2_success

if __name__ == "__main__":
    main()
