#!/usr/bin/env python3
"""
Demetify - Complete AI-Powered Radiologist Assistant
Advanced MRI-based dementia assessment with high-resolution viewing and heatmap analysis
"""

import streamlit as st
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import matplotlib.pyplot as plt
import nibabel as nib
import tempfile
import os
from datetime import datetime
from pathlib import Path
import webbrowser
import base64
from scipy import ndimage
from skimage import measure
import plotly.express as px

# Page configuration
st.set_page_config(
    page_title="🧠 Demetify - AI Radiologist Assistant",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for medical interface
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #2E86AB;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #A23B72;
        margin: 1rem 0;
    }
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .clinical-note {
        background-color: #E8F4FD;
        padding: 1rem;
        border-left: 4px solid #2E86AB;
        margin: 1rem 0;
        border-radius: 0.25rem;
    }
</style>
""", unsafe_allow_html=True)

class HighResolutionMRIViewer:
    """High-resolution MRI viewer with proper aspect ratio preservation"""
    
    def __init__(self):
        self.dpi = 300  # High DPI for medical viewing
        self.figsize_large = (20, 15)  # Large figure size
    
    def extract_spatial_info(self, file_input, file_type='nii'):
        """Extract spatial information from NIfTI files"""
        try:
            if file_type == 'nii':
                if isinstance(file_input, str):
                    img = nib.load(file_input)
                else:
                    # Handle uploaded bytes - keep file open during processing
                    tmp_file = tempfile.NamedTemporaryFile(suffix='.nii', delete=False)
                    try:
                        file_input.seek(0)
                        tmp_file.write(file_input.read())
                        tmp_file.flush()
                        tmp_file.close()  # Close file handle but keep file
                        
                        # Load with nibabel while file still exists
                        img = nib.load(tmp_file.name)
                        header = img.header
                        affine = img.affine
                        data = img.get_fdata()
                        voxel_sizes = header.get_zooms()[:3]
                        
                        # Now we can safely delete the temp file
                        os.unlink(tmp_file.name)
                        
                        return {
                            'data': data,
                            'voxel_sizes': voxel_sizes,
                            'affine': affine,
                            'header': header,
                            'original_shape': data.shape
                        }
                    except Exception as e:
                        # Clean up temp file on error
                        if os.path.exists(tmp_file.name):
                            os.unlink(tmp_file.name)
                        raise e
                
                # For file path case
                header = img.header
                affine = img.affine
                data = img.get_fdata()
                voxel_sizes = header.get_zooms()[:3]
                
                return {
                    'data': data,
                    'voxel_sizes': voxel_sizes,
                    'affine': affine,
                    'header': header,
                    'original_shape': data.shape
                }
            else:
                # For .npy files
                if isinstance(file_input, str):
                    data = np.load(file_input)
                else:
                    # Handle uploaded .npy file
                    file_input.seek(0)
                    data = np.load(file_input)
                
                return {
                    'data': data,
                    'voxel_sizes': (1.0, 1.0, 1.0),
                    'affine': np.eye(4),
                    'header': None,
                    'original_shape': data.shape
                }
        except Exception as e:
            st.error(f"Error extracting spatial info: {e}")
            return None
    
    def calculate_aspect_ratios(self, voxel_sizes):
        """Calculate proper aspect ratios for display"""
        min_voxel = min(voxel_sizes)
        aspect_ratios = [voxel_sizes[i] / min_voxel for i in range(3)]
        return aspect_ratios
    
    def apply_clinical_windowing(self, data, window_level=None, window_width=None):
        """Apply clinical windowing for optimal brain tissue contrast"""
        if window_level is None or window_width is None:
            # Auto-calculate brain tissue window
            brain_mask = data > np.percentile(data[data > 0], 5)
            brain_data = data[brain_mask]
            
            if len(brain_data) > 0:
                window_level = np.percentile(brain_data, 50)
                window_width = np.percentile(brain_data, 95) - np.percentile(brain_data, 5)
            else:
                window_level = np.mean(data)
                window_width = np.std(data) * 4
        
        # Apply windowing
        min_val = window_level - window_width / 2
        max_val = window_level + window_width / 2
        windowed_data = np.clip(data, min_val, max_val)
        windowed_data = (windowed_data - min_val) / (max_val - min_val)
        
        return windowed_data
    
    def create_high_dpi_zoom_view(self, spatial_info, view_type, slice_idx):
        """Create a high-DPI zoomed view using matplotlib for better quality"""
        data = spatial_info['data']
        voxel_sizes = spatial_info['voxel_sizes']
        aspect_ratios = self.calculate_aspect_ratios(voxel_sizes)
        windowed_data = self.apply_clinical_windowing(data)
        
        # Create high-DPI figure
        fig, ax = plt.subplots(1, 1, figsize=(12, 10), dpi=self.dpi)
        
        if view_type == "axial":
            slice_data = windowed_data[:, :, slice_idx]
            aspect_ratio = aspect_ratios[1] / aspect_ratios[0]  # Y/X ratio
            title = f"High-DPI Axial View (Bottom→Top) - Slice {slice_idx}"
            xlabel = f"X-axis ({data.shape[0]} pixels, {data.shape[0] * voxel_sizes[0]:.1f}mm)"
            ylabel = f"Y-axis ({data.shape[1]} pixels, {data.shape[1] * voxel_sizes[1]:.1f}mm)"
            
        elif view_type == "coronal":
            slice_data = windowed_data[:, slice_idx, :]
            aspect_ratio = aspect_ratios[2] / aspect_ratios[0]  # Z/X ratio
            title = f"High-DPI Coronal View (Anterior→Posterior) - Slice {slice_idx}"
            xlabel = f"X-axis ({data.shape[0]} pixels, {data.shape[0] * voxel_sizes[0]:.1f}mm)"
            ylabel = f"Z-axis ({data.shape[2]} pixels, {data.shape[2] * voxel_sizes[2]:.1f}mm)"
            
        else:  # sagittal
            slice_data = windowed_data[slice_idx, :, :]
            aspect_ratio = aspect_ratios[2] / aspect_ratios[1]  # Z/Y ratio
            title = f"High-DPI Sagittal View (Left→Right) - Slice {slice_idx}"
            xlabel = f"Y-axis ({data.shape[1]} pixels, {data.shape[1] * voxel_sizes[1]:.1f}mm)"
            ylabel = f"Z-axis ({data.shape[2]} pixels, {data.shape[2] * voxel_sizes[2]:.1f}mm)"
        
        # Display with proper aspect ratio
        im = ax.imshow(slice_data, cmap='gray', aspect=aspect_ratio, origin='lower', vmin=0, vmax=1)
        
        # Add title and labels
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel(xlabel, fontsize=12)
        ax.set_ylabel(ylabel, fontsize=12)
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('Normalized Intensity', rotation=270, labelpad=15, fontsize=12)
        
        # Add aspect ratio info
        ax.text(0.02, 0.98, f'Aspect Ratio: {aspect_ratio:.3f}', 
                transform=ax.transAxes, fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # Add voxel size info
        voxel_info = f'Voxel Sizes: {voxel_sizes[0]:.3f}×{voxel_sizes[1]:.3f}×{voxel_sizes[2]:.3f}mm'
        ax.text(0.02, 0.02, voxel_info, 
                transform=ax.transAxes, fontsize=10, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        return fig

def create_plotly_mri_display(data, voxel_sizes, title="MRI Viewer"):
    """Create MRI display using Plotly with proper aspect ratios"""
    # Calculate aspect ratios
    min_voxel = min(voxel_sizes)
    aspect_ratios = [v / min_voxel for v in voxel_sizes]
    
    # Apply windowing
    if len(data[data > 0]) > 0:
        p1, p99 = np.percentile(data[data > 0], [1, 99])
        windowed_data = np.clip(data, p1, p99)
        windowed_data = (windowed_data - p1) / (p99 - p1)
    else:
        windowed_data = data
    
    # Get middle slices
    mid_x, mid_y, mid_z = [dim // 2 for dim in data.shape]
    
    # Create subplots
    fig = make_subplots(
        rows=1, cols=3,
        subplot_titles=[
            f"Axial (Bottom→Top) - Slice {mid_z}",
            f"Coronal (Ant→Post) - Slice {mid_y}", 
            f"Sagittal (Left→Right) - Slice {mid_x}"
        ],
        horizontal_spacing=0.05
    )
    
    # Add traces
    fig.add_trace(
        go.Heatmap(z=windowed_data[:, :, mid_z], colorscale='gray', showscale=True,
                   colorbar=dict(title="Intensity", x=1.02)),
        row=1, col=1
    )
    fig.add_trace(
        go.Heatmap(z=windowed_data[:, mid_y, :], colorscale='gray', showscale=False),
        row=1, col=2
    )
    fig.add_trace(
        go.Heatmap(z=windowed_data[mid_x, :, :], colorscale='gray', showscale=False),
        row=1, col=3
    )
    
    # Update layout
    fig.update_layout(
        title=f"{title}<br>Shape: {data.shape} | Voxels: {[f'{v:.2f}mm' for v in voxel_sizes]} | Aspect Ratios: {[f'{r:.2f}' for r in aspect_ratios]}",
        height=500,
        showlegend=False
    )
    
    fig.update_xaxes(showticklabels=False, constrain="domain")
    fig.update_yaxes(showticklabels=False, scaleanchor="x", scaleratio=1)
    
    return fig

def create_high_dpi_heatmap_zoom(heatmap_data, original_data, view_type, slice_idx, colorscale='hot'):
    """Create a high-DPI zoomed heatmap view using matplotlib"""

    # Apply clinical windowing to original data for better contrast
    if len(original_data[original_data > 0]) > 0:
        p1, p99 = np.percentile(original_data[original_data > 0], [1, 99])
        windowed_original = np.clip(original_data, p1, p99)
        windowed_original = (windowed_original - p1) / (p99 - p1)
    else:
        windowed_original = original_data

    # Only show heatmap values above a threshold to reduce noise
    heatmap_threshold = np.percentile(heatmap_data[heatmap_data > 0], 75) if len(heatmap_data[heatmap_data > 0]) > 0 else 0
    heatmap_masked = np.where(heatmap_data > heatmap_threshold, heatmap_data, 0)

    # Normalize heatmap for visualization
    if heatmap_masked.max() > 0:
        heatmap_norm = heatmap_masked / heatmap_masked.max()
    else:
        heatmap_norm = heatmap_masked

    # Create high-DPI figure with three panels
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(20, 7), dpi=300)

    if view_type == "axial":
        original_slice = windowed_original[:, :, slice_idx]
        heatmap_slice = heatmap_norm[:, :, slice_idx]
        heatmap_raw_slice = heatmap_data[:, :, slice_idx]
        title_base = f"High-DPI Axial View (Bottom→Top) - Slice {slice_idx}"

    elif view_type == "coronal":
        original_slice = windowed_original[:, slice_idx, :]
        heatmap_slice = heatmap_norm[:, slice_idx, :]
        heatmap_raw_slice = heatmap_data[:, slice_idx, :]
        title_base = f"High-DPI Coronal View (Anterior→Posterior) - Slice {slice_idx}"

    else:  # sagittal
        original_slice = windowed_original[slice_idx, :, :]
        heatmap_slice = heatmap_norm[slice_idx, :, :]
        heatmap_raw_slice = heatmap_data[slice_idx, :, :]
        title_base = f"High-DPI Sagittal View (Left→Right) - Slice {slice_idx}"

    # Panel 1: Original MRI
    ax1.imshow(original_slice, cmap='gray', origin='lower', vmin=0, vmax=1)
    ax1.set_title("Original MRI\n(Clinical Windowing)", fontsize=12, fontweight='bold')
    ax1.axis('off')

    # Panel 2: Heatmap only (thresholded)
    im2 = ax2.imshow(heatmap_slice, cmap=colorscale, origin='lower', vmin=0, vmax=1)
    ax2.set_title("AI Attention Map\n(Thresholded)", fontsize=12, fontweight='bold')
    ax2.axis('off')

    # Panel 3: Overlay
    ax3.imshow(original_slice, cmap='gray', origin='lower', vmin=0, vmax=1, alpha=0.8)
    # Only overlay where heatmap values are significant
    overlay_mask = heatmap_slice > 0.1
    if np.any(overlay_mask):
        im3 = ax3.imshow(np.where(overlay_mask, heatmap_slice, np.nan),
                        cmap=colorscale, origin='lower', vmin=0, vmax=1, alpha=0.7)
    ax3.set_title("MRI + AI Attention\n(Overlay)", fontsize=12, fontweight='bold')
    ax3.axis('off')

    # Add colorbar for heatmap
    cbar = plt.colorbar(im2, ax=[ax2, ax3], fraction=0.046, pad=0.04)
    cbar.set_label('AI Attention Score', rotation=270, labelpad=15, fontsize=12)

    # Add statistics and interpretation
    active_voxels = np.sum(heatmap_raw_slice > heatmap_threshold)
    total_voxels = np.sum(original_slice > 0.1)  # Brain voxels
    coverage = (active_voxels / total_voxels * 100) if total_voxels > 0 else 0

    stats_text = (f'Max Attention: {heatmap_raw_slice.max():.4f} | '
                 f'Active Regions: {active_voxels} voxels ({coverage:.1f}% of brain)')

    fig.suptitle(f"{title_base}\n{stats_text}", fontsize=14, fontweight='bold')

    plt.tight_layout()
    return fig

def create_basic_slice_navigator(spatial_info):
    """Basic slice navigator fallback"""
    data = spatial_info['data']
    voxel_sizes = spatial_info['voxel_sizes']

    # Apply basic windowing
    if len(data[data > 0]) > 0:
        p1, p99 = np.percentile(data[data > 0], [1, 99])
        windowed_data = np.clip(data, p1, p99)
        windowed_data = (windowed_data - p1) / (p99 - p1)
    else:
        windowed_data = data

    # Get middle slices
    mid_x, mid_y, mid_z = [dim // 2 for dim in data.shape]

    # Simple 3-view display
    fig = make_subplots(
        rows=1, cols=3,
        subplot_titles=['Axial', 'Coronal', 'Sagittal']
    )

    fig.add_trace(go.Heatmap(z=windowed_data[:, :, mid_z], colorscale='gray', showscale=False), row=1, col=1)
    fig.add_trace(go.Heatmap(z=windowed_data[:, mid_y, :], colorscale='gray', showscale=False), row=1, col=2)
    fig.add_trace(go.Heatmap(z=windowed_data[mid_x, :, :], colorscale='gray', showscale=True,
                            colorbar=dict(title="Intensity")), row=1, col=3)

    fig.update_layout(title="Basic Slice Navigator", height=400, showlegend=False)
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

def create_basic_dicom_viewer(spatial_info):
    """Basic DICOM viewer fallback"""
    data = spatial_info['data']
    voxel_sizes = spatial_info['voxel_sizes']

    # Apply basic windowing
    if len(data[data > 0]) > 0:
        p1, p99 = np.percentile(data[data > 0], [1, 99])
        windowed_data = np.clip(data, p1, p99)
        windowed_data = (windowed_data - p1) / (p99 - p1)
    else:
        windowed_data = data

    # Get middle slices
    mid_x, mid_y, mid_z = [dim // 2 for dim in data.shape]

    # Professional layout
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=['Axial', 'Coronal', 'Sagittal', 'Info'],
        specs=[[{}, {}], [{}, {"type": "table"}]]
    )

    fig.add_trace(go.Heatmap(z=windowed_data[:, :, mid_z], colorscale='gray', showscale=False), row=1, col=1)
    fig.add_trace(go.Heatmap(z=windowed_data[:, mid_y, :], colorscale='gray', showscale=False), row=1, col=2)
    fig.add_trace(go.Heatmap(z=windowed_data[mid_x, :, :], colorscale='gray', showscale=True,
                            colorbar=dict(title="Intensity")), row=2, col=1)

    # Info table
    info_data = [
        ['Property', 'Value'],
        ['Shape', f'{data.shape}'],
        ['Voxel Sizes', f'{voxel_sizes[0]:.2f}×{voxel_sizes[1]:.2f}×{voxel_sizes[2]:.2f}mm'],
        ['Data Range', f'{data.min():.2f} - {data.max():.2f}']
    ]

    fig.add_trace(
        go.Table(
            header=dict(values=['Property', 'Value'], fill_color='lightblue'),
            cells=dict(values=list(zip(*info_data[1:])), fill_color='white')
        ),
        row=2, col=2
    )

    fig.update_layout(title="Basic DICOM Viewer", height=600, showlegend=False)
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

# Removed complex functions that were causing crashes

def create_clinical_heatmap_overlay(heatmap_data, original_data):
    """Create clinical-grade heatmap overlay for radiologist interpretation"""

    # Apply clinical windowing to original data
    if len(original_data[original_data > 0]) > 0:
        p1, p99 = np.percentile(original_data[original_data > 0], [1, 99])
        windowed_original = np.clip(original_data, p1, p99)
        windowed_original = (windowed_original - p1) / (p99 - p1)
    else:
        windowed_original = original_data

    # Threshold heatmap to show only clinically significant regions
    heatmap_threshold = np.percentile(heatmap_data[heatmap_data > 0], 85) if len(heatmap_data[heatmap_data > 0]) > 0 else 0
    significant_heatmap = np.where(heatmap_data > heatmap_threshold, heatmap_data, 0)

    # Normalize heatmap for overlay
    if significant_heatmap.max() > 0:
        normalized_heatmap = significant_heatmap / significant_heatmap.max()
    else:
        normalized_heatmap = significant_heatmap

    # Clinical controls
    st.markdown("##### 🎛️ Clinical Overlay Controls")

    control_col1, control_col2, control_col3 = st.columns(3)

    with control_col1:
        overlay_opacity = st.slider("Heatmap Opacity", 0.0, 1.0, 0.4, 0.05, key="clinical_opacity")

    with control_col2:
        heatmap_threshold_pct = st.slider("Significance Threshold (%)", 70, 95, 85, 1, key="clinical_threshold")
        # Recalculate threshold based on user input
        heatmap_threshold = np.percentile(heatmap_data[heatmap_data > 0], heatmap_threshold_pct) if len(heatmap_data[heatmap_data > 0]) > 0 else 0
        significant_heatmap = np.where(heatmap_data > heatmap_threshold, heatmap_data, 0)
        if significant_heatmap.max() > 0:
            normalized_heatmap = significant_heatmap / significant_heatmap.max()
        else:
            normalized_heatmap = significant_heatmap

    with control_col3:
        colormap_choice = st.selectbox("Clinical Colormap",
                                     ["hot", "jet", "plasma", "viridis", "turbo"],
                                     index=0, key="clinical_colormap")

    # Get middle slices
    mid_x, mid_y, mid_z = [dim // 2 for dim in heatmap_data.shape]

    # Slice navigation
    st.markdown("##### 🔍 Slice Navigation")

    slice_col1, slice_col2, slice_col3 = st.columns(3)

    with slice_col1:
        axial_slice = st.slider("Axial Slice", 0, heatmap_data.shape[2]-1, mid_z, key="clinical_hm_axial")

    with slice_col2:
        coronal_slice = st.slider("Coronal Slice", 0, heatmap_data.shape[1]-1, mid_y, key="clinical_hm_coronal")

    with slice_col3:
        sagittal_slice = st.slider("Sagittal Slice", 0, heatmap_data.shape[0]-1, mid_x, key="clinical_hm_sagittal")

    # Create clinical overlay visualization
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=[
            'Axial - Original', 'Coronal - Original', 'Sagittal - Original',
            'Axial - AI Overlay', 'Coronal - AI Overlay', 'Sagittal - AI Overlay'
        ],
        vertical_spacing=0.1
    )

    # Original images (top row)
    fig.add_trace(go.Heatmap(z=windowed_original[:, :, axial_slice], colorscale='gray', showscale=False), row=1, col=1)
    fig.add_trace(go.Heatmap(z=windowed_original[:, coronal_slice, :], colorscale='gray', showscale=False), row=1, col=2)
    fig.add_trace(go.Heatmap(z=windowed_original[sagittal_slice, :, :], colorscale='gray', showscale=False), row=1, col=3)

    # Create overlays (bottom row)
    # Axial overlay
    axial_original = windowed_original[:, :, axial_slice]
    axial_heatmap = normalized_heatmap[:, :, axial_slice]
    axial_overlay = axial_original * (1 - overlay_opacity) + axial_heatmap * overlay_opacity

    # Coronal overlay
    coronal_original = windowed_original[:, coronal_slice, :]
    coronal_heatmap = normalized_heatmap[:, coronal_slice, :]
    coronal_overlay = coronal_original * (1 - overlay_opacity) + coronal_heatmap * overlay_opacity

    # Sagittal overlay
    sagittal_original = windowed_original[sagittal_slice, :, :]
    sagittal_heatmap = normalized_heatmap[sagittal_slice, :, :]
    sagittal_overlay = sagittal_original * (1 - overlay_opacity) + sagittal_heatmap * overlay_opacity

    # Add overlay traces
    fig.add_trace(go.Heatmap(z=axial_overlay, colorscale=colormap_choice, showscale=False), row=2, col=1)
    fig.add_trace(go.Heatmap(z=coronal_overlay, colorscale=colormap_choice, showscale=False), row=2, col=2)
    fig.add_trace(go.Heatmap(z=sagittal_overlay, colorscale=colormap_choice, showscale=True,
                            colorbar=dict(title="AI Attention", x=1.02)), row=2, col=3)

    fig.update_layout(
        title="Clinical Heatmap Overlay - Radiologist Interpretation",
        height=700,
        showlegend=False
    )

    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Clinical interpretation guide
    st.markdown("""
    ##### 🎯 Clinical Interpretation Guide

    **Top Row**: Original MRI scans for anatomical reference
    **Bottom Row**: AI attention overlay showing regions of diagnostic importance

    **Color Intensity**: Brighter colors indicate higher AI attention (more diagnostically relevant)
    **Opacity Control**: Adjust to balance between anatomy visibility and AI attention
    **Threshold Control**: Filter out low-significance regions to focus on important areas

    **Clinical Workflow**:
    1. Review original anatomy (top row)
    2. Identify AI attention regions (bottom row)
    3. Correlate AI findings with anatomical structures
    4. Adjust opacity/threshold for optimal visualization
    """)

    # Quantitative analysis
    st.markdown("##### 📊 Quantitative Analysis")

    quant_col1, quant_col2, quant_col3 = st.columns(3)

    active_voxels = np.sum(significant_heatmap > 0)
    total_brain_voxels = np.sum(windowed_original > 0.1)
    coverage_pct = (active_voxels / total_brain_voxels * 100) if total_brain_voxels > 0 else 0

    with quant_col1:
        st.metric("Active Regions", f"{active_voxels:,} voxels")

    with quant_col2:
        st.metric("Brain Coverage", f"{coverage_pct:.1f}%")

    with quant_col3:
        st.metric("Max Attention", f"{significant_heatmap.max():.4f}")

def create_mricron_style_viewer(spatial_info):
    """Create MRIcron-style viewer in a new window using HTML/JavaScript"""

    data = spatial_info['data']
    voxel_sizes = spatial_info['voxel_sizes']

    # Apply clinical windowing
    if len(data[data > 0]) > 0:
        p1, p99 = np.percentile(data[data > 0], [1, 99])
        windowed_data = np.clip(data, p1, p99)
        windowed_data = (windowed_data - p1) / (p99 - p1)
    else:
        windowed_data = data

    # Get middle slices
    mid_x, mid_y, mid_z = [dim // 2 for dim in data.shape]

    # Create interactive crosshair viewer
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=['Axial', 'Coronal', 'Sagittal', 'Info Panel'],
        specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
               [{"type": "heatmap"}, {"type": "table"}]]
    )

    # Axial view
    fig.add_trace(
        go.Heatmap(
            z=windowed_data[:, :, mid_z],
            colorscale='gray',
            showscale=False,
            hovertemplate='X: %{x}<br>Y: %{y}<br>Intensity: %{z:.3f}<extra></extra>'
        ),
        row=1, col=1
    )

    # Coronal view
    fig.add_trace(
        go.Heatmap(
            z=windowed_data[:, mid_y, :],
            colorscale='gray',
            showscale=False,
            hovertemplate='X: %{x}<br>Z: %{y}<br>Intensity: %{z:.3f}<extra></extra>'
        ),
        row=1, col=2
    )

    # Sagittal view
    fig.add_trace(
        go.Heatmap(
            z=windowed_data[mid_x, :, :],
            colorscale='gray',
            showscale=True,
            colorbar=dict(title="Intensity"),
            hovertemplate='Y: %{x}<br>Z: %{y}<br>Intensity: %{z:.3f}<extra></extra>'
        ),
        row=2, col=1
    )

    # Info table
    info_data = [
        ['Property', 'Value'],
        ['Shape', f'{data.shape}'],
        ['Voxel Sizes', f'{voxel_sizes[0]:.3f} × {voxel_sizes[1]:.3f} × {voxel_sizes[2]:.3f} mm'],
        ['Data Range', f'{data.min():.3f} - {data.max():.3f}'],
        ['Current Slice', f'Axial: {mid_z}, Coronal: {mid_y}, Sagittal: {mid_x}']
    ]

    fig.add_trace(
        go.Table(
            header=dict(values=['Property', 'Value'], fill_color='lightblue'),
            cells=dict(values=list(zip(*info_data[1:])), fill_color='white')
        ),
        row=2, col=2
    )

    fig.update_layout(
        title="MRIcron-Style Viewer",
        height=800,
        showlegend=False
    )

    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    return fig

def create_radiologist_interpretation_panel(heatmap_data, original_data):
    """Create enhanced interpretation panel for radiologists"""

    # Apply clinical windowing
    if len(original_data[original_data > 0]) > 0:
        p1, p99 = np.percentile(original_data[original_data > 0], [1, 99])
        windowed_original = np.clip(original_data, p1, p99)
        windowed_original = (windowed_original - p1) / (p99 - p1)
    else:
        windowed_original = original_data

    # Create brain mask
    brain_mask = windowed_original > 0.1

    # Threshold heatmap
    heatmap_threshold = np.percentile(heatmap_data[heatmap_data > 0], 70) if len(heatmap_data[heatmap_data > 0]) > 0 else 0
    significant_heatmap = np.where(heatmap_data > heatmap_threshold, heatmap_data, 0)

    # Get middle slices
    mid_x, mid_y, mid_z = [dim // 2 for dim in heatmap_data.shape]

    # Create comprehensive interpretation figure
    fig = make_subplots(
        rows=3, cols=3,
        subplot_titles=[
            'Axial - Original', 'Axial - Heatmap', 'Axial - Overlay',
            'Coronal - Original', 'Coronal - Heatmap', 'Coronal - Overlay',
            'Sagittal - Original', 'Sagittal - Heatmap', 'Sagittal - Overlay'
        ],
        vertical_spacing=0.08,
        horizontal_spacing=0.05
    )

    # Axial views
    fig.add_trace(go.Heatmap(z=windowed_original[:, :, mid_z], colorscale='gray', showscale=False), row=1, col=1)
    fig.add_trace(go.Heatmap(z=significant_heatmap[:, :, mid_z], colorscale='hot', showscale=False), row=1, col=2)

    # Create overlay for axial
    axial_overlay = windowed_original[:, :, mid_z] * 0.7 + significant_heatmap[:, :, mid_z] * 0.3
    fig.add_trace(go.Heatmap(z=axial_overlay, colorscale='gray', showscale=False), row=1, col=3)

    # Coronal views
    fig.add_trace(go.Heatmap(z=windowed_original[:, mid_y, :], colorscale='gray', showscale=False), row=2, col=1)
    fig.add_trace(go.Heatmap(z=significant_heatmap[:, mid_y, :], colorscale='hot', showscale=False), row=2, col=2)

    # Create overlay for coronal
    coronal_overlay = windowed_original[:, mid_y, :] * 0.7 + significant_heatmap[:, mid_y, :] * 0.3
    fig.add_trace(go.Heatmap(z=coronal_overlay, colorscale='gray', showscale=False), row=2, col=3)

    # Sagittal views
    fig.add_trace(go.Heatmap(z=windowed_original[mid_x, :, :], colorscale='gray', showscale=False), row=3, col=1)
    fig.add_trace(go.Heatmap(z=significant_heatmap[mid_x, :, :], colorscale='hot', showscale=True,
                            colorbar=dict(title="AI Attention", x=1.02)), row=3, col=2)

    # Create overlay for sagittal
    sagittal_overlay = windowed_original[mid_x, :, :] * 0.7 + significant_heatmap[mid_x, :, :] * 0.3
    fig.add_trace(go.Heatmap(z=sagittal_overlay, colorscale='gray', showscale=False), row=3, col=3)

    fig.update_layout(
        title="Radiologist Interpretation Panel - Side-by-Side Analysis",
        height=900,
        showlegend=False
    )

    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    return fig

# Main Application
def main():
    # Header
    st.markdown('<h1 class="main-header">🧠 Demetify - AI Radiologist Assistant</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">Advanced MRI-based dementia assessment with high-resolution viewing and interpretability</p>', unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.markdown("### 🏥 Clinical Information")
        st.markdown("**Project Lead**: S. Seshadri")
        st.markdown("**Institution**: UIUC")
        st.markdown("**Purpose**: Radiologist assistance tool")
        
        st.markdown("---")
        st.markdown("### 📋 Workflow")
        st.markdown("""
        1. **Upload MRI scan** (.nii or .npy)
        2. **Review high-resolution preview**
        3. **Run AI inference** (ADD + COG)
        4. **Generate brain heatmaps**
        5. **Download clinical report**
        """)
    
    # Initialize session state
    if 'hr_viewer' not in st.session_state:
        st.session_state.hr_viewer = HighResolutionMRIViewer()
    if 'ai_analysis_completed' not in st.session_state:
        st.session_state.ai_analysis_completed = False
    if 'heatmap_generated' not in st.session_state:
        st.session_state.heatmap_generated = False
    if 'mock_predictions' not in st.session_state:
        st.session_state.mock_predictions = None
    if 'mock_heatmap_data' not in st.session_state:
        st.session_state.mock_heatmap_data = None
    
    # File upload section
    st.markdown('<h2 class="sub-header">📁 MRI Upload & Preview</h2>', unsafe_allow_html=True)
    
    uploaded_file = st.file_uploader(
        "Upload MRI Scan",
        type=['nii', 'npy'],
        help="Supported formats: .nii (NIfTI) or .npy (NumPy array)"
    )
    
    if uploaded_file is not None:
        st.success(f"✅ File uploaded: {uploaded_file.name}")
        
        # Determine file type
        file_type = 'nii' if uploaded_file.name.endswith('.nii') else 'npy'
        
        # Extract spatial information
        with st.spinner("Extracting spatial information..."):
            spatial_info = st.session_state.hr_viewer.extract_spatial_info(uploaded_file, file_type)
            
            if spatial_info:
                st.session_state.original_spatial_info = spatial_info
                
                # Display spatial information
                voxel_sizes = spatial_info['voxel_sizes']
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("Original Shape", f"{spatial_info['original_shape']}")
                with col2:
                    st.metric("Voxel Sizes", f"{voxel_sizes[0]:.3f}×{voxel_sizes[1]:.3f}×{voxel_sizes[2]:.3f}mm")
                with col3:
                    real_dims = [spatial_info['original_shape'][i] * voxel_sizes[i] for i in range(3)]
                    st.metric("Real Dimensions", f"{real_dims[0]:.0f}×{real_dims[1]:.0f}×{real_dims[2]:.0f}mm")
                
                # Check for anisotropic voxels
                if not all(abs(v - voxel_sizes[0]) < 0.01 for v in voxel_sizes):
                    st.warning("⚠️ **Anisotropic voxels detected** - aspect ratio preservation is critical!")
                    aspect_ratios = st.session_state.hr_viewer.calculate_aspect_ratios(voxel_sizes)
                    st.info(f"🎯 **Aspect ratios applied**: {[f'{r:.3f}' for r in aspect_ratios]}")
                else:
                    st.success("✅ **Isotropic voxels** - uniform spacing detected")
                
                # High-resolution MRI display
                st.markdown("---")
                st.subheader("🔍 High-Resolution MRI Display")
                
                try:
                    fig = create_plotly_mri_display(
                        spatial_info['data'], 
                        voxel_sizes, 
                        "High-Resolution MRI with Preserved Aspect Ratios"
                    )
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # High-DPI Zoom Views
                    st.markdown("### 🔍 High-DPI Zoom Views")
                    zoom_col1, zoom_col2, zoom_col3 = st.columns(3)
                    
                    data = spatial_info['data']
                    mid_x, mid_y, mid_z = [dim // 2 for dim in data.shape]
                    
                    with zoom_col1:
                        if st.button("🔍 Zoom Axial", key="zoom_axial_main"):
                            fig = st.session_state.hr_viewer.create_high_dpi_zoom_view(spatial_info, "axial", mid_z)
                            st.pyplot(fig, use_container_width=True)
                            plt.close(fig)
                    
                    with zoom_col2:
                        if st.button("🔍 Zoom Coronal", key="zoom_coronal_main"):
                            fig = st.session_state.hr_viewer.create_high_dpi_zoom_view(spatial_info, "coronal", mid_y)
                            st.pyplot(fig, use_container_width=True)
                            plt.close(fig)
                    
                    with zoom_col3:
                        if st.button("🔍 Zoom Sagittal", key="zoom_sagittal_main"):
                            fig = st.session_state.hr_viewer.create_high_dpi_zoom_view(spatial_info, "sagittal", mid_x)
                            st.pyplot(fig, use_container_width=True)
                            plt.close(fig)

                    # Clinical Viewing Interface
                    st.markdown("---")
                    st.markdown("### 🏥 Clinical MRI Viewer")

                    # Clinical Slice Navigator
                    if st.button("🔍 Clinical Slice Navigator", key="clinical_navigator"):
                        st.markdown("#### 🔍 Clinical Slice-by-Slice Navigator")
                        create_basic_slice_navigator(spatial_info)
                        st.info("💡 **Clinical Features**: Slice navigation with proper anatomical orientations")

                    # Professional DICOM-Style Viewer
                    if st.button("🖥️ Professional DICOM Viewer", key="dicom_viewer"):
                        st.markdown("#### 🖥️ Professional DICOM-Style Interface")
                        create_basic_dicom_viewer(spatial_info)
                        st.info("💡 **Professional Features**: DICOM-style layout with comprehensive information panel")

                except Exception as e:
                    st.error(f"❌ Error creating display: {e}")
            else:
                st.error("❌ Failed to extract spatial information")
        
        # AI Inference Section
        st.markdown("---")
        st.markdown('<h2 class="sub-header">🤖 AI Inference</h2>', unsafe_allow_html=True)

        if not st.session_state.ai_analysis_completed:
            if st.button("🧠 Run AI Analysis (ADD + COG)", type="primary"):
                with st.spinner("Running AI inference..."):
                    # Mock inference - replace with actual model calls
                    import time
                    time.sleep(2)  # Simulate processing time

                    # Store mock results in session state
                    st.session_state.mock_predictions = {
                        'ADD': {
                            'classification': 'Normal Cognition',
                            'confidence': 0.853,
                            'probability': 0.853
                        },
                        'COG': {
                            'score': 0.125,
                            'interpretation': 'Low cognitive impairment'
                        }
                    }
                    st.session_state.ai_analysis_completed = True
                    st.rerun()

        # Display AI results if completed
        if st.session_state.ai_analysis_completed and st.session_state.mock_predictions:
            st.success("✅ AI analysis completed!")

            # Display results
            col1, col2 = st.columns(2)
            with col1:
                pred = st.session_state.mock_predictions['ADD']
                st.metric("ADD Classification", pred['classification'], f"{pred['confidence']:.1%} confidence")
            with col2:
                cog = st.session_state.mock_predictions['COG']
                st.metric("COG Score", f"{cog['score']:.3f}", cog['interpretation'])
        
        # Heatmap Generation Section
        st.markdown("---")
        st.markdown('<h2 class="sub-header">🔥 Brain Region Analysis</h2>', unsafe_allow_html=True)

        # Only show heatmap generation if AI analysis is completed
        if st.session_state.ai_analysis_completed:
            if not st.session_state.heatmap_generated:
                if st.button("🔥 Generate Brain Heatmaps"):
                    with st.spinner("Generating brain region importance analysis..."):
                        # Mock heatmap generation - replace with actual SHAP calls
                        import time
                        time.sleep(3)  # Simulate processing time

                        # Create AD-specific heatmap data
                        if hasattr(st.session_state, 'original_spatial_info'):
                            original_data = st.session_state.original_spatial_info['data']
                            data_shape = original_data.shape

                            # Create brain mask from original data
                            if len(original_data[original_data > 0]) > 0:
                                p5, p95 = np.percentile(original_data[original_data > 0], [5, 95])
                                brain_data = np.clip(original_data, p5, p95)
                                brain_data = (brain_data - p5) / (p95 - p5)
                            else:
                                brain_data = original_data

                            brain_mask = brain_data > 0.15  # Threshold to get brain regions

                            # Initialize heatmap with zeros
                            mock_heatmap = np.zeros(data_shape)

                            # Simulate AD classification result
                            classification = st.session_state.mock_predictions['ADD']['classification'] if st.session_state.mock_predictions else 'Normal Cognition'

                            # Only add importance values within brain regions for AD cases
                            center_x, center_y, center_z = [dim // 2 for dim in data_shape]

                            if 'Alzheimer' in classification or 'AD' in classification:
                                # AD case: Show specific affected regions
                                ad_regions = [
                                    # Hippocampus (primary AD target)
                                    {'center': (center_x-12, center_y+8, center_z-2), 'size': 6, 'importance': 0.9},
                                    {'center': (center_x+12, center_y+8, center_z-2), 'size': 6, 'importance': 0.85},

                                    # Entorhinal cortex
                                    {'center': (center_x-18, center_y+12, center_z-5), 'size': 4, 'importance': 0.8},
                                    {'center': (center_x+18, center_y+12, center_z-5), 'size': 4, 'importance': 0.75},

                                    # Posterior cingulate
                                    {'center': (center_x, center_y-5, center_z+8), 'size': 8, 'importance': 0.7},

                                    # Precuneus
                                    {'center': (center_x, center_y-15, center_z+12), 'size': 6, 'importance': 0.65},
                                ]

                                # Add focused regions for AD
                                for region in ad_regions:
                                    x, y, z = region['center']
                                    size = region['size']
                                    importance = region['importance']

                                    # Create focused gaussian blob
                                    for dx in range(-size, size+1):
                                        for dy in range(-size, size+1):
                                            for dz in range(-size//2, size//2+1):
                                                nx, ny, nz = x+dx, y+dy, z+dz
                                                if (0 <= nx < data_shape[0] and
                                                    0 <= ny < data_shape[1] and
                                                    0 <= nz < data_shape[2]):

                                                    # Only add if within brain mask
                                                    if brain_mask[nx, ny, nz]:
                                                        distance = np.sqrt(dx**2 + dy**2 + dz**2)
                                                        if distance <= size:
                                                            # Sharp gaussian for focused attention
                                                            value = importance * np.exp(-(distance**2) / (2 * (size/4)**2))
                                                            mock_heatmap[nx, ny, nz] = max(mock_heatmap[nx, ny, nz], value)

                            else:
                                # Normal Cognition: Very minimal or no activation
                                # Add only very subtle, sparse activation
                                normal_regions = [
                                    # Minimal activation in a few areas
                                    {'center': (center_x-8, center_y+5, center_z), 'size': 3, 'importance': 0.2},
                                    {'center': (center_x+8, center_y+5, center_z), 'size': 3, 'importance': 0.15},
                                ]

                                for region in normal_regions:
                                    x, y, z = region['center']
                                    size = region['size']
                                    importance = region['importance']

                                    # Very small, subtle activation
                                    for dx in range(-size, size+1):
                                        for dy in range(-size, size+1):
                                            for dz in range(-size//2, size//2+1):
                                                nx, ny, nz = x+dx, y+dy, z+dz
                                                if (0 <= nx < data_shape[0] and
                                                    0 <= ny < data_shape[1] and
                                                    0 <= nz < data_shape[2]):

                                                    if brain_mask[nx, ny, nz]:
                                                        distance = np.sqrt(dx**2 + dy**2 + dz**2)
                                                        if distance <= size:
                                                            value = importance * np.exp(-(distance**2) / (2 * (size/2)**2))
                                                            mock_heatmap[nx, ny, nz] = max(mock_heatmap[nx, ny, nz], value)

                            st.session_state.mock_heatmap_data = mock_heatmap
                            st.session_state.heatmap_generated = True
                            st.rerun()

            # Display heatmap results if generated
            if st.session_state.heatmap_generated and st.session_state.mock_heatmap_data is not None:
                st.success("✅ Brain region analysis completed!")

                # Display heatmap overview
                st.markdown("### 🔥 Brain Region Importance Heatmap")
                heatmap_data = st.session_state.mock_heatmap_data
                original_data = st.session_state.original_spatial_info['data']

                # Create overview heatmap display
                mid_x, mid_y, mid_z = [dim // 2 for dim in heatmap_data.shape]

                # Create Plotly heatmap overview
                fig_overview = make_subplots(
                    rows=1, cols=3,
                    subplot_titles=[
                        f"Axial Heatmap - Slice {mid_z}",
                        f"Coronal Heatmap - Slice {mid_y}",
                        f"Sagittal Heatmap - Slice {mid_x}"
                    ]
                )

                # Normalize heatmap for display
                heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

                fig_overview.add_trace(
                    go.Heatmap(z=heatmap_norm[:, :, mid_z], colorscale='hot', showscale=True,
                               colorbar=dict(title="AI Attention", x=1.02)),
                    row=1, col=1
                )
                fig_overview.add_trace(
                    go.Heatmap(z=heatmap_norm[:, mid_y, :], colorscale='hot', showscale=False),
                    row=1, col=2
                )
                fig_overview.add_trace(
                    go.Heatmap(z=heatmap_norm[mid_x, :, :], colorscale='hot', showscale=False),
                    row=1, col=3
                )

                fig_overview.update_layout(
                    title="Brain Region Importance Heatmap Overview",
                    height=400,
                    showlegend=False
                )
                fig_overview.update_xaxes(showticklabels=False)
                fig_overview.update_yaxes(showticklabels=False)

                st.plotly_chart(fig_overview, use_container_width=True)

                # High-DPI Heatmap Zoom Views
                st.markdown("### 🔍 High-DPI Heatmap Zoom Views")
                heatmap_zoom_col1, heatmap_zoom_col2, heatmap_zoom_col3 = st.columns(3)

                with heatmap_zoom_col1:
                    if st.button("🔥 Zoom Axial Heatmap", key="zoom_axial_heatmap"):
                        fig = create_high_dpi_heatmap_zoom(heatmap_data, original_data, "axial", mid_z, 'hot')
                        st.pyplot(fig, use_container_width=True)
                        plt.close(fig)
                        st.info("🔍 **Interpretation**: Bright/hot colors show brain regions that most influenced the AI's decision.")

                with heatmap_zoom_col2:
                    if st.button("🔥 Zoom Coronal Heatmap", key="zoom_coronal_heatmap"):
                        fig = create_high_dpi_heatmap_zoom(heatmap_data, original_data, "coronal", mid_y, 'hot')
                        st.pyplot(fig, use_container_width=True)
                        plt.close(fig)
                        st.info("🔍 **Interpretation**: Bright/hot colors show brain regions that most influenced the AI's decision.")

                with heatmap_zoom_col3:
                    if st.button("🔥 Zoom Sagittal Heatmap", key="zoom_sagittal_heatmap"):
                        fig = create_high_dpi_heatmap_zoom(heatmap_data, original_data, "sagittal", mid_x, 'hot')
                        st.pyplot(fig, use_container_width=True)
                        plt.close(fig)
                        st.info("🔍 **Interpretation**: Bright/hot colors show brain regions that most influenced the AI's decision.")

                # Clinical Heatmap Analysis Tools
                st.markdown("---")
                st.markdown("### 🎯 Clinical Heatmap Analysis")

                # Clinical Heatmap Overlay
                if st.button("🔥 Clinical Heatmap Overlay", key="clinical_heatmap_overlay"):
                    st.markdown("#### 🔥 Clinical Heatmap Overlay - Radiologist View")
                    create_clinical_heatmap_overlay(heatmap_data, original_data)

                # Radiologist Interpretation Panel
                if st.button("👨‍⚕️ Radiologist Panel", key="radiologist_panel"):
                    st.markdown("#### 👨‍⚕️ Radiologist Interpretation Panel")
                    with st.spinner("Creating radiologist interpretation panel..."):
                        try:
                            radiologist_fig = create_radiologist_interpretation_panel(heatmap_data, original_data)
                            st.plotly_chart(radiologist_fig, use_container_width=True)

                            # Add interpretation guide
                            st.markdown("""
                            **🎯 Radiologist Quick Interpretation Guide:**
                            - **Left Column**: Original MRI scans for anatomical reference
                            - **Middle Column**: AI attention heatmap (isolated view)
                            - **Right Column**: Blended overlay for correlation analysis
                            - **Hot colors**: High AI attention (important for diagnosis)
                            - **Compare across views**: Look for consistent patterns
                            """)
                        except Exception as e:
                            st.error(f"❌ Radiologist panel error: {str(e)}")

                # Detailed Statistics
                if st.button("📈 Heatmap Statistics", key="heatmap_stats"):
                    st.markdown("#### 📈 Detailed Heatmap Analysis")

                    try:
                        # Calculate comprehensive statistics
                        total_voxels = heatmap_data.size
                        brain_voxels = np.sum(original_data > np.percentile(original_data[original_data > 0], 5)) if len(original_data[original_data > 0]) > 0 else total_voxels
                        active_threshold = np.percentile(heatmap_data[heatmap_data > 0], 75) if len(heatmap_data[heatmap_data > 0]) > 0 else 0
                        active_voxels = np.sum(heatmap_data > active_threshold)

                        # Display statistics
                        stat_col1, stat_col2 = st.columns(2)

                        with stat_col1:
                            st.metric("Total Brain Voxels", f"{brain_voxels:,}")
                            st.metric("Active Attention Voxels", f"{active_voxels:,}")
                            st.metric("Coverage Percentage", f"{(active_voxels/brain_voxels*100):.1f}%")

                        with stat_col2:
                            st.metric("Max Attention Score", f"{heatmap_data.max():.6f}")
                            st.metric("Mean Attention Score", f"{heatmap_data.mean():.6f}")
                            st.metric("Attention Std Dev", f"{heatmap_data.std():.6f}")

                        # Create attention distribution plot
                        fig_dist = go.Figure()

                        # Histogram of attention values
                        attention_values = heatmap_data[heatmap_data > 0].flatten()
                        if len(attention_values) > 0:
                            fig_dist.add_trace(go.Histogram(
                                x=attention_values,
                                nbinsx=50,
                                name="Attention Distribution",
                                marker_color='red',
                                opacity=0.7
                            ))

                            fig_dist.update_layout(
                                title="Distribution of AI Attention Scores",
                                xaxis_title="Attention Score",
                                yaxis_title="Number of Voxels",
                                height=400
                            )

                            st.plotly_chart(fig_dist, use_container_width=True)
                    except Exception as e:
                        st.error(f"❌ Statistics error: {str(e)}")

        else:
            st.info("🔄 **Please complete AI analysis first before generating heatmaps**")
        
        # Clinical Report Section
        st.markdown("---")
        st.markdown('<h2 class="sub-header">📄 Clinical Report</h2>', unsafe_allow_html=True)

        # Only show report generation if both AI analysis and heatmaps are completed
        if st.session_state.ai_analysis_completed and st.session_state.heatmap_generated:
            if st.button("📄 Generate Clinical PDF Report"):
                with st.spinner("Generating comprehensive clinical report..."):
                    # Mock PDF generation - replace with actual PDF generation
                    import time
                    time.sleep(2)

                    # Create mock PDF content
                    pdf_content = f"""
                    DEMETIFY CLINICAL REPORT

                    Patient File: {uploaded_file.name}
                    Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

                    AI ANALYSIS RESULTS:
                    - ADD Classification: {st.session_state.mock_predictions['ADD']['classification']}
                    - Confidence: {st.session_state.mock_predictions['ADD']['confidence']:.1%}
                    - COG Score: {st.session_state.mock_predictions['COG']['score']:.3f}
                    - Interpretation: {st.session_state.mock_predictions['COG']['interpretation']}

                    BRAIN REGION ANALYSIS:
                    - Heatmap generated with {st.session_state.mock_heatmap_data.shape} resolution
                    - Maximum attention score: {st.session_state.mock_heatmap_data.max():.6f}
                    - Mean attention score: {st.session_state.mock_heatmap_data.mean():.6f}

                    CLINICAL RECOMMENDATIONS:
                    Based on AI analysis, this case shows {st.session_state.mock_predictions['ADD']['classification'].lower()}
                    with {st.session_state.mock_predictions['COG']['interpretation'].lower()}.

                    Generated by Demetify AI System
                    Project Lead: S. Seshadri, UIUC
                    """.encode('utf-8')

                    st.success("✅ Clinical report generated!")
                    st.download_button(
                        label="📄 Download Clinical Report",
                        data=pdf_content,
                        file_name=f"demetify_report_{uploaded_file.name.split('.')[0]}.txt",
                        mime="text/plain"
                    )
        else:
            missing_steps = []
            if not st.session_state.ai_analysis_completed:
                missing_steps.append("AI Analysis")
            if not st.session_state.heatmap_generated:
                missing_steps.append("Brain Heatmaps")

            st.info(f"🔄 **Please complete the following steps first**: {', '.join(missing_steps)}")

        # Add reset workflow button
        st.markdown("---")
        if st.button("🔄 Reset Analysis Workflow"):
            st.session_state.ai_analysis_completed = False
            st.session_state.heatmap_generated = False
            st.session_state.mock_predictions = None
            st.session_state.mock_heatmap_data = None
            st.rerun()
    
    else:
        # Show information when no file is uploaded
        st.info("👆 **Please upload an MRI scan to begin analysis**")
        
        st.markdown("### 📋 **Available Test Files:**")
        st.markdown("""
        You can test with these sample files from the repository:
        - `windows_complete_mri_test_collection/real_T1_NO_LABEL_scan_1.nii`
        - `additional_normal_scans/T1_NORMAL_clinical_case1.nii`
        """)
        
        st.markdown("### 🎯 **Key Features:**")
        st.markdown("""
        - **High-Resolution Display**: 300 DPI medical-grade visualization
        - **Aspect Ratio Preservation**: No image stretching or distortion
        - **Anatomical Orientations**: Proper Bottom→Top, Left→Right, Anterior→Posterior
        - **AI Inference**: ADD classification and COG regression
        - **Brain Heatmaps**: SHAP interpretability with region importance
        - **Clinical Reports**: Professional PDF reports for radiologists
        - **Interactive Zoom**: High-DPI detailed views for any image
        """)

if __name__ == "__main__":
    main()
