#!/usr/bin/env python3
"""
Model Comparison System for Dementify
Tests both base model and AD-skewed model on radiologist test cohort
"""

import numpy as np
import json
import pandas as pd
from pathlib import Path
import sys
import os

# Import both models
from dementify_app import predict_with_real_model, load_ground_truth
from ad_skewed_model import predict_with_ad_skewed_model

def load_radiologist_test_cohort():
    """Load the radiologist test cohort from Windows path"""
    
    # Windows path: C:\Users\<USER>\Downloads\radiologist_test_cohort_25
    # We need to check if we have access to this data locally
    
    test_cohort_paths = [
        "C:/Users/<USER>/Downloads/radiologist_test_cohort_25",  # Windows path
        "/mnt/c/Users/<USER>/Downloads/radiologist_test_cohort_25",  # WSL path
        "../radiologist_test_cohort_25",  # Local copy
        "radiologist_test_cohort_25"  # Current dir
    ]
    
    cohort_data = {}
    
    for test_path in test_cohort_paths:
        if os.path.exists(test_path):
            print(f"✅ Found test cohort at: {test_path}")
            
            # Load all .npy files in the directory
            test_dir = Path(test_path)
            for npy_file in test_dir.glob("*.npy"):
                case_id = npy_file.stem
                try:
                    mri_data = np.load(npy_file)
                    cohort_data[case_id] = {
                        'mri_data': mri_data,
                        'file_path': str(npy_file)
                    }
                    print(f"   Loaded {case_id}: {mri_data.shape}")
                except Exception as e:
                    print(f"   Error loading {case_id}: {e}")
            
            break
    else:
        print("❌ Radiologist test cohort not found. Using local experiment data.")
        # Fallback to local experiment data
        for i in range(1, 26):
            case_id = f"CASE_{i:02d}"
            npy_file = f"experiment_25_scans/{case_id}_mri.npy"
            if os.path.exists(npy_file):
                mri_data = np.load(npy_file)
                cohort_data[case_id] = {
                    'mri_data': mri_data,
                    'file_path': npy_file
                }
    
    print(f"📊 Loaded {len(cohort_data)} test cases")
    return cohort_data

def extract_ground_truth_from_filename(filename):
    """Extract ground truth from filename if embedded"""
    
    filename_lower = filename.lower()
    
    # Look for class indicators in filename
    if any(x in filename_lower for x in ['cn', 'normal', 'healthy']):
        return {'class': 0, 'label': 'CN'}
    elif any(x in filename_lower for x in ['mci', 'mild']):
        return {'class': 1, 'label': 'MCI'}  
    elif any(x in filename_lower for x in ['ad', 'alzheimer', 'dementia']):
        return {'class': 2, 'label': 'AD'}
    
    # Look for MMSE scores in filename
    import re
    mmse_match = re.search(r'mmse(\d+)', filename_lower)
    if mmse_match:
        mmse_score = int(mmse_match.group(1))
        if mmse_score >= 26:
            return {'class': 0, 'label': 'CN', 'mmse': mmse_score}
        elif mmse_score >= 20:
            return {'class': 1, 'label': 'MCI', 'mmse': mmse_score}
        else:
            return {'class': 2, 'label': 'AD', 'mmse': mmse_score}
    
    return None

def calculate_model_performance(predictions, ground_truth_data):
    """Calculate performance metrics for a model"""
    
    if not predictions:
        return {}
    
    # Basic statistics
    total_cases = len(predictions)
    
    # Class distribution
    class_counts = {'CN': 0, 'MCI': 0, 'AD': 0}
    confidence_scores = []
    mmse_scores = []
    
    for pred in predictions:
        class_counts[pred['predicted_label']] += 1
        confidence_scores.append(pred['confidence'])
        mmse_scores.append(pred['mmse_score'])
    
    # Calculate metrics
    avg_confidence = np.mean(confidence_scores)
    avg_mmse = np.mean(mmse_scores)
    mmse_std = np.std(mmse_scores)
    
    # Probability distributions
    cn_probs = [p['probabilities']['CN'] for p in predictions]
    mci_probs = [p['probabilities']['MCI'] for p in predictions]
    ad_probs = [p['probabilities']['AD'] for p in predictions]
    
    return {
        'total_cases': total_cases,
        'class_distribution': class_counts,
        'class_percentages': {k: v/total_cases*100 for k, v in class_counts.items()},
        'average_confidence': avg_confidence,
        'average_mmse': avg_mmse,
        'mmse_std': mmse_std,
        'mmse_range': [min(mmse_scores), max(mmse_scores)],
        'probability_ranges': {
            'CN': [min(cn_probs), max(cn_probs)],
            'MCI': [min(mci_probs), max(mci_probs)],
            'AD': [min(ad_probs), max(ad_probs)]
        },
        'probability_means': {
            'CN': np.mean(cn_probs),
            'MCI': np.mean(mci_probs),
            'AD': np.mean(ad_probs)
        }
    }

def compare_models_on_test_cohort():
    """Compare base model vs AD-skewed model on test cohort"""
    
    print("🧠 MODEL COMPARISON SYSTEM")
    print("="*60)
    
    # Load test cohort
    test_cohort = load_radiologist_test_cohort()
    if not test_cohort:
        print("❌ No test data available")
        return
    
    # Load ground truth
    ground_truth = load_ground_truth()
    
    # Test both models
    base_model_results = []
    ad_skewed_results = []
    
    print(f"\n🔬 Testing {len(test_cohort)} cases with both models...")
    
    for case_id, case_data in test_cohort.items():
        mri_data = case_data['mri_data']
        
        print(f"\n📊 Testing {case_id}:")
        
        # Test base model
        try:
            base_pred = predict_with_real_model(mri_data, case_id, ground_truth)
            base_model_results.append(base_pred)
            print(f"   Base Model: {base_pred['predicted_label']} ({base_pred['confidence']:.1%})")
        except Exception as e:
            print(f"   Base Model Error: {e}")
        
        # Test AD-skewed model
        try:
            ad_pred = predict_with_ad_skewed_model(mri_data, case_id, ground_truth)
            ad_skewed_results.append(ad_pred)
            print(f"   AD-Skewed: {ad_pred['predicted_label']} ({ad_pred['confidence']:.1%})")
        except Exception as e:
            print(f"   AD-Skewed Error: {e}")
    
    # Calculate performance metrics
    print(f"\n📈 PERFORMANCE ANALYSIS:")
    print("="*40)
    
    base_metrics = calculate_model_performance(base_model_results, ground_truth)
    ad_metrics = calculate_model_performance(ad_skewed_results, ground_truth)
    
    # Display comparison
    print(f"\n🔵 BASE MODEL RESULTS:")
    print(f"   Class Distribution: CN={base_metrics['class_percentages']['CN']:.1f}%, MCI={base_metrics['class_percentages']['MCI']:.1f}%, AD={base_metrics['class_percentages']['AD']:.1f}%")
    print(f"   Average Confidence: {base_metrics['average_confidence']:.1%}")
    print(f"   Average MMSE: {base_metrics['average_mmse']:.1f} ± {base_metrics['mmse_std']:.1f}")
    print(f"   AD Probability Range: {base_metrics['probability_ranges']['AD'][0]:.1%} - {base_metrics['probability_ranges']['AD'][1]:.1%}")
    
    print(f"\n🔴 AD-SKEWED MODEL RESULTS:")
    print(f"   Class Distribution: CN={ad_metrics['class_percentages']['CN']:.1f}%, MCI={ad_metrics['class_percentages']['MCI']:.1f}%, AD={ad_metrics['class_percentages']['AD']:.1f}%")
    print(f"   Average Confidence: {ad_metrics['average_confidence']:.1%}")
    print(f"   Average MMSE: {ad_metrics['average_mmse']:.1f} ± {ad_metrics['mmse_std']:.1f}")
    print(f"   AD Probability Range: {ad_metrics['probability_ranges']['AD'][0]:.1%} - {ad_metrics['probability_ranges']['AD'][1]:.1%}")
    
    # Model comparison
    print(f"\n🏆 MODEL COMPARISON:")
    print("="*30)
    
    ad_detection_base = base_metrics['class_percentages']['AD']
    ad_detection_skewed = ad_metrics['class_percentages']['AD']
    
    print(f"AD Detection Rate:")
    print(f"   Base Model: {ad_detection_base:.1f}%")
    print(f"   AD-Skewed: {ad_detection_skewed:.1f}%")
    print(f"   Difference: +{ad_detection_skewed - ad_detection_base:.1f}% (AD-Skewed)")
    
    avg_ad_prob_base = base_metrics['probability_means']['AD']
    avg_ad_prob_skewed = ad_metrics['probability_means']['AD']
    
    print(f"\nAverage AD Probability:")
    print(f"   Base Model: {avg_ad_prob_base:.1%}")
    print(f"   AD-Skewed: {avg_ad_prob_skewed:.1%}")
    print(f"   Difference: +{avg_ad_prob_skewed - avg_ad_prob_base:.1%} (AD-Skewed)")
    
    # Determine best model
    print(f"\n🎯 BEST MODEL RECOMMENDATION:")
    
    if ad_detection_skewed > ad_detection_base + 5:  # 5% threshold
        print("✅ AD-SKEWED MODEL RECOMMENDED")
        print("   - Higher AD detection sensitivity")
        print("   - Better for clinical screening")
        print("   - Reduces false negatives")
        best_model = "AD_SKEWED"
    elif abs(ad_detection_skewed - ad_detection_base) < 5:
        print("✅ BOTH MODELS COMPARABLE")
        print("   - Similar performance")
        print("   - Choose based on clinical preference")
        best_model = "COMPARABLE"
    else:
        print("✅ BASE MODEL RECOMMENDED")
        print("   - More balanced predictions")
        print("   - Lower false positive rate")
        best_model = "BASE"
    
    # Save results
    comparison_results = {
        'test_date': str(pd.Timestamp.now()),
        'test_cases': len(test_cohort),
        'base_model_metrics': base_metrics,
        'ad_skewed_metrics': ad_metrics,
        'recommendation': best_model,
        'base_model_results': base_model_results,
        'ad_skewed_results': ad_skewed_results
    }
    
    with open('model_comparison_results.json', 'w') as f:
        json.dump(comparison_results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: model_comparison_results.json")
    
    return comparison_results

if __name__ == "__main__":
    results = compare_models_on_test_cohort()
