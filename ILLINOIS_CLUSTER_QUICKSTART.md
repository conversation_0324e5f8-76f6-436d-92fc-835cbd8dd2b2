# 🚀 Illinois Cluster Quick Start Guide - Enhanced MCI Training

## 📋 **Step-by-Step Execution Plan**

### **Step 1: Login and Initial Setup**
```bash
# 1. Login to Illinois Campus Cluster
ssh -l vmt2 cc-login1.campuscluster.illinois.edu

# 2. Navigate to project directory
cd /projects/illinois/cob/ba/sridhar

# 3. Create and navigate to working directory
mkdir -p enhanced_mci_training
cd enhanced_mci_training

# 4. Copy the enhanced scripts (upload these files first)
# - illinois_cluster_setup.sh
# - explore_nacc_data.py
# - setup_enhanced_environment.py
# - slurm_enhanced_mci_training.sh
# - enhanced_ncomms2022_model.py
# - enhanced_preprocessing.py
# - comprehensive_interpretability.py
# - clinical_features_reporting.py
```

### **Step 2: Environment Setup**
```bash
# 1. Run cluster setup script
chmod +x illinois_cluster_setup.sh
./illinois_cluster_setup.sh

# 2. Load anaconda and setup enhanced environment
module load anaconda3
python setup_enhanced_environment.py

# 3. Activate enhanced environment
source activate_enhanced_mci.sh
```

### **Step 3: Data Exploration**
```bash
# 1. Explore NACC data structure
python explore_nacc_data.py

# 2. Review exploration results
cat data_exploration_summary.txt

# 3. Check data availability
ls -la /projects/illinois/cob/ba/sridhar/nacc_data_dump/
ls -la /projects/illinois/cob/ba/sridhar/NACC_tabular_data/
ls -la "/projects/illinois/cob/ba/sridhar/Nacc imaging file/"
ls -la /projects/illinois/cob/ba/sridhar/preprocessedT1scans23june/
```

### **Step 4: Create MCI Dataset**
```bash
# 1. Create MCI dataset from NACC data
python create_mci_dataset.py \
    --nacc_tabular_dir "/projects/illinois/cob/ba/sridhar/NACC_tabular_data" \
    --imaging_dir "/projects/illinois/cob/ba/sridhar/Nacc imaging file" \
    --preprocessed_dir "/projects/illinois/cob/ba/sridhar/preprocessedT1scans23june" \
    --output_dir "data/mci_dataset"

# 2. Verify dataset creation
ls -la data/mci_dataset/
head -5 data/mci_dataset/train.csv
```

### **Step 5: Enhanced Preprocessing**
```bash
# 1. Run enhanced preprocessing on sample data
python enhanced_preprocessing_pipeline.py \
    --input_dir "/projects/illinois/cob/ba/sridhar/preprocessedT1scans23june" \
    --output_dir "data/enhanced_preprocessed" \
    --apply_skull_stripping \
    --apply_bias_correction \
    --apply_normalization

# 2. Validate preprocessing quality
python validate_preprocessing.py --data_dir "data/enhanced_preprocessed"
```

### **Step 6: Submit Training Job**
```bash
# 1. Make SLURM script executable
chmod +x slurm_enhanced_mci_training.sh

# 2. Create necessary directories
mkdir -p {logs,checkpoints,results,tensorboard}

# 3. Submit training job
sbatch slurm_enhanced_mci_training.sh

# 4. Monitor job status
squeue -u vmt2
watch -n 30 'squeue -u vmt2'

# 5. Check job output
tail -f logs/enhanced_mci_training_*.out
```

### **Step 7: Monitor Training Progress**
```bash
# 1. Check training logs
ls -la logs/
tail -f logs/training_fold_*_*.log

# 2. Monitor GPU usage
ssh [compute_node]  # Get node name from squeue
nvidia-smi -l 5

# 3. Check TensorBoard logs
module load anaconda3
source activate enhanced_mci
tensorboard --logdir tensorboard --port 6006 --bind_all
```

### **Step 8: Evaluate Results**
```bash
# 1. Check training completion
ls -la checkpoints/
ls -la results/

# 2. Generate evaluation report
python evaluate_enhanced_model.py \
    --checkpoint_dir checkpoints \
    --test_data_dir data/test \
    --output_dir results/final_evaluation

# 3. Create comprehensive heatmaps
python generate_comprehensive_heatmaps.py \
    --model_checkpoints checkpoints \
    --test_samples data/test_samples \
    --output_dir results/interpretability
```

## 🔧 **Useful Commands**

### **Job Management:**
```bash
# Submit job
sbatch slurm_enhanced_mci_training.sh

# Check job status
squeue -u vmt2

# Cancel job
scancel [JOB_ID]

# Job details
scontrol show job [JOB_ID]

# Job accounting
sacct -j [JOB_ID] --format=JobID,JobName,MaxRSS,Elapsed
```

### **Environment Management:**
```bash
# Load modules
module load anaconda3

# List environments
conda env list

# Activate environment
conda activate enhanced_mci

# Check packages
conda list | grep torch
pip list | grep shap
```

### **Data Management:**
```bash
# Check disk usage
du -sh /projects/illinois/cob/ba/sridhar/enhanced_mci_training/

# Clean up temporary files
find . -name "*.tmp" -delete
find . -name "*.temp" -delete

# Archive results
tar -czf enhanced_mci_results_$(date +%Y%m%d).tar.gz results/ checkpoints/
```

## 📊 **Expected Outputs**

### **After Data Exploration:**
- `data_exploration_summary.txt` - Human-readable summary
- `nacc_data_exploration_*.json` - Detailed JSON report
- Understanding of data structure and MCI classification strategy

### **After Environment Setup:**
- `enhanced_mci` conda environment
- `activate_enhanced_mci.sh` activation script
- All required packages installed and verified

### **After Training:**
- `checkpoints/fold_*/` - Model checkpoints for each fold
- `results/training_summary_*.json` - Training metrics
- `tensorboard/` - TensorBoard logs
- `logs/` - Detailed training logs

### **After Evaluation:**
- `results/evaluation_*/` - Model performance metrics
- `results/heatmaps_*/` - Comprehensive interpretability maps
- `results/clinical_reports/` - PDF reports with MCI analysis

## ⚠️ **Important Notes**

### **File Management:**
- Work only in `/projects/illinois/cob/ba/sridhar/` directory
- Clean up intermediate files regularly
- Keep only essential results and final models

### **Resource Usage:**
- Use 4x A100 GPUs efficiently with parallel training
- Monitor memory usage (128GB allocated)
- 24-hour time limit - plan accordingly

### **Best Practices:**
- Test with small datasets first
- Monitor job progress regularly
- Save checkpoints frequently
- Document any issues or modifications

## 🆘 **Troubleshooting**

### **Common Issues:**
1. **Environment not found**: Check conda environment creation
2. **CUDA out of memory**: Reduce batch size or enable gradient checkpointing
3. **Data not found**: Verify data paths in exploration results
4. **Job timeout**: Increase time limit or optimize training

### **Getting Help:**
- Check SLURM logs: `logs/enhanced_mci_training_*.err`
- Illinois cluster documentation: https://docs.ncsa.illinois.edu/systems/icc/
- Contact cluster support if needed

## 🎯 **Success Criteria**

✅ **Environment Setup Complete**
✅ **Data Exploration Successful** 
✅ **MCI Dataset Created**
✅ **Enhanced Preprocessing Working**
✅ **Training Jobs Submitted**
✅ **Model Checkpoints Generated**
✅ **Interpretability Maps Created**
✅ **Clinical Reports Generated**

---

**Ready to start? Begin with Step 1 and follow the sequence!** 🚀
