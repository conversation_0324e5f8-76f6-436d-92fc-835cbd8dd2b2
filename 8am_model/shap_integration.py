#!/usr/bin/env python3
"""
SHAP Integration for 8AM Model
Converts regression-style cognitive scoring to classification with SHAP attribution
"""

import numpy as np
import shap
from scipy.ndimage import gaussian_filter
import warnings
warnings.filterwarnings('ignore')

class DementiaClassifier:
    """Classification wrapper for SHAP attribution"""
    
    def __init__(self):
        self.feature_names = ['brain_volume', 'gray_matter_ratio', 'hippocampal_intensity', 
                             'temporal_intensity', 'gradient_magnitude', 'health_score']
    
    def extract_features(self, mri_data):
        """Extract features from MRI data for classification"""
        
        # Create brain mask
        brain_mask = mri_data > np.percentile(mri_data, 15)
        brain_volume = np.sum(brain_mask)
        brain_intensity_mean = np.mean(mri_data[brain_mask])
        brain_intensity_std = np.std(mri_data[brain_mask])
        
        # Gray matter analysis
        gray_matter_threshold = brain_intensity_mean + 0.3 * brain_intensity_std
        gray_matter_volume = np.sum((mri_data > gray_matter_threshold) & brain_mask)
        gray_matter_ratio = gray_matter_volume / brain_volume if brain_volume > 0 else 0
        
        # Regional analysis
        center_x, center_y, center_z = np.array(mri_data.shape) // 2
        
        # Hippocampal regions
        hippo_left = mri_data[center_x-10:center_x-5, center_y-5:center_y+5, center_z-5:center_z+5]
        hippo_right = mri_data[center_x+5:center_x+10, center_y-5:center_y+5, center_z-5:center_z+5]
        hippo_intensity = (np.mean(hippo_left) + np.mean(hippo_right)) / 2
        
        # Temporal regions
        temporal_left = mri_data[center_x-15:center_x-10, center_y+5:center_y+15, center_z-5:center_z+5]
        temporal_right = mri_data[center_x+10:center_x+15, center_y+5:center_y+15, center_z-5:center_z+5]
        temporal_intensity = (np.mean(temporal_left) + np.mean(temporal_right)) / 2
        
        # Gradient analysis
        grad_x = np.gradient(mri_data, axis=0)
        grad_y = np.gradient(mri_data, axis=1)
        grad_z = np.gradient(mri_data, axis=2)
        gradient_magnitude = np.mean(np.sqrt(grad_x**2 + grad_y**2 + grad_z**2)[brain_mask])
        
        # Health score
        health_score = (gray_matter_ratio * 0.6) + (hippo_intensity / brain_intensity_mean * 0.4)
        
        return np.array([
            brain_volume / 1000000,  # Normalize
            gray_matter_ratio,
            hippo_intensity / brain_intensity_mean,
            temporal_intensity / brain_intensity_mean,
            gradient_magnitude / brain_intensity_mean,
            health_score
        ])
    
    def predict_proba(self, features_array):
        """Convert features to classification probabilities for SHAP"""
        
        if features_array.ndim == 1:
            features_array = features_array.reshape(1, -1)
        
        probabilities = []
        
        for features in features_array:
            brain_volume_norm, gray_matter_ratio, hippo_norm, temporal_norm, gradient_norm, health_score = features
            
            # Cognitive score calculation (same as original model)
            cognitive_score = health_score - (gradient_norm * 0.3)
            
            # Convert to classification probabilities
            if cognitive_score > 0.6:  # CN
                base_probs = [0.75, 0.20, 0.05]
            elif cognitive_score > 0.4:  # MCI
                base_probs = [0.25, 0.60, 0.15]
            else:  # AD
                base_probs = [0.10, 0.25, 0.65]
            
            # Add some feature-based variation
            if gray_matter_ratio < 0.3:  # Low gray matter = more AD risk
                base_probs[2] += 0.1
                base_probs[0] -= 0.05
                base_probs[1] -= 0.05
            
            if hippo_norm < 0.5:  # Low hippocampal = more AD risk
                base_probs[2] += 0.05
                base_probs[0] -= 0.025
                base_probs[1] -= 0.025
            
            # Normalize
            probs = np.array(base_probs)
            probs = np.maximum(probs, 0.01)
            probs = probs / np.sum(probs)
            
            probabilities.append(probs)
        
        return np.array(probabilities)
    
    def predict(self, features_array):
        """Predict class labels"""
        probs = self.predict_proba(features_array)
        return np.argmax(probs, axis=1)

def create_shap_heatmap(mri_data, predicted_class, confidence, case_id):
    """Create SHAP-based heatmap for 8AM model"""
    
    print(f"🔥 Creating SHAP heatmap for {case_id}, class {predicted_class}")
    
    # Initialize classifier
    classifier = DementiaClassifier()
    
    # Extract features from original scan
    original_features = classifier.extract_features(mri_data)
    
    # Create background dataset (synthetic variations)
    background_data = []
    for _ in range(50):  # Small background for speed
        # Create variations by adding noise
        noise_factor = np.random.normal(1.0, 0.1, mri_data.shape)
        varied_mri = mri_data * noise_factor
        varied_features = classifier.extract_features(varied_mri)
        background_data.append(varied_features)
    
    background_data = np.array(background_data)
    
    # Create SHAP explainer
    explainer = shap.Explainer(classifier.predict_proba, background_data)
    
    # Get SHAP values
    shap_values = explainer([original_features])

    # Convert SHAP values to spatial heatmap
    # Use the SHAP values for the predicted class
    if hasattr(shap_values, 'values'):
        class_shap_values = shap_values.values[0, :, predicted_class]
    else:
        class_shap_values = shap_values[0][:, predicted_class]
    
    # Create spatial heatmap by mapping feature importance to brain regions
    heatmap = np.zeros_like(mri_data, dtype=np.float32)
    brain_mask = mri_data > np.percentile(mri_data, 15)
    
    # Map feature importance to spatial regions
    center_x, center_y, center_z = np.array(mri_data.shape) // 2
    
    # Brain volume importance -> whole brain
    if abs(class_shap_values[0]) > 0.01:
        heatmap[brain_mask] += abs(class_shap_values[0]) * 0.3
    
    # Gray matter importance -> high intensity regions
    if abs(class_shap_values[1]) > 0.01:
        brain_intensities = mri_data * brain_mask
        brain_mean = np.mean(brain_intensities[brain_intensities > 0])
        brain_std = np.std(brain_intensities[brain_intensities > 0])
        gray_regions = (brain_intensities > brain_mean + 0.3 * brain_std) & brain_mask
        heatmap[gray_regions] += abs(class_shap_values[1]) * 0.5
    
    # Hippocampal importance -> hippocampal regions
    if abs(class_shap_values[2]) > 0.01:
        # Left hippocampus
        heatmap[center_x-10:center_x-5, center_y-5:center_y+5, center_z-5:center_z+5] += abs(class_shap_values[2]) * 0.8
        # Right hippocampus
        heatmap[center_x+5:center_x+10, center_y-5:center_y+5, center_z-5:center_z+5] += abs(class_shap_values[2]) * 0.8
    
    # Temporal importance -> temporal regions
    if abs(class_shap_values[3]) > 0.01:
        # Left temporal
        heatmap[center_x-15:center_x-10, center_y+5:center_y+15, center_z-5:center_z+5] += abs(class_shap_values[3]) * 0.7
        # Right temporal
        heatmap[center_x+10:center_x+15, center_y+5:center_y+15, center_z-5:center_z+5] += abs(class_shap_values[3]) * 0.7
    
    # Gradient importance -> edge regions
    if abs(class_shap_values[4]) > 0.01:
        grad_x = np.gradient(mri_data, axis=0)
        grad_y = np.gradient(mri_data, axis=1)
        grad_z = np.gradient(mri_data, axis=2)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2 + grad_z**2)
        high_gradient = gradient_magnitude > np.percentile(gradient_magnitude, 80)
        heatmap[high_gradient & brain_mask] += abs(class_shap_values[4]) * 0.4
    
    # Apply brain mask
    heatmap = heatmap * brain_mask
    
    # Smooth and normalize
    heatmap = gaussian_filter(heatmap, sigma=1.0)
    
    if np.max(heatmap) > 0:
        heatmap = heatmap / np.max(heatmap)
        
        # Apply threshold for clean visualization
        threshold = 0.3  # Show meaningful SHAP attributions
        heatmap[heatmap < threshold] = 0
        
        # Renormalize
        if np.max(heatmap) > 0:
            heatmap = heatmap / np.max(heatmap)
    
    activation_pct = np.sum(heatmap > 0.1) / heatmap.size * 100
    print(f"   SHAP activation: {activation_pct:.2f}%, max: {np.max(heatmap):.3f}")
    
    # Print SHAP feature importance
    feature_names = classifier.feature_names
    print(f"   SHAP feature importance:")
    for i, (name, importance) in enumerate(zip(feature_names, class_shap_values)):
        print(f"     {name}: {importance:.4f}")
    
    return heatmap, class_shap_values, feature_names
