
# AI vs Radiologist MRI Classification Experiment

## Overview
This experiment compares AI model performance with radiologist assessment for MRI-based dementia classification.

## Contents
- **25 MRI scans** in .npy format
- **25 visualizations** in .png format  
- **experiment_metadata.json**: Complete experiment details
- **radiologist_assessment_form.json**: Form for Dr<PERSON> to fill
- **experiment_summary.csv**: Summary table

## Class Distribution
- CN (Normal): 8 cases
- MCI (Mild Cognitive Impairment): 9 cases  
- AD (Alzheimer's Disease): 8 cases

## MMSE Score Range
- Mean: 21.4
- Range: 9.2 - 29.8

## AI Performance Preview
- Accuracy: 84.0%
- Mean Confidence: 62.7%

## Instructions for Dr. <PERSON>
1. Review each MRI visualization
2. Estimate MMSE score (8-30 range)
3. Classify as CN/MCI/AD
4. Rate confidence (1-5 scale)
5. Add any clinical notes
6. Record assessment time

## Study Protocol
- Double-blind assessment
- AI-first vs Radiologist-first comparison
- Statistical analysis of agreement
