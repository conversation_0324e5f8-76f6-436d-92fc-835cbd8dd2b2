# 🧠 Normal Cognition + Alzheimer's Disease MRI Collection

## 📁 **Collection Overview**

This focused collection contains 5 MRI scans specifically for testing Normal vs Alzheimer's Disease classification.

### **🎯 Collection Focus:**
- **Normal Cognition**: Healthy controls with no cognitive impairment
- **Alzheimer's Disease**: Confirmed AD cases with expected pathology
- **Clinical Scans**: Real T1 scans for additional validation

### **🏷️ Filename Convention:**
- **T1_NORMAL_**: Normal cognition cases
- **T1_ALZHEIMERS_**: Alzheimer's disease cases  
- **T1_REAL_**: Real clinical scans (unknown diagnosis)

### **📊 Expected Results:**

#### **Normal Cases:**
- **T1_NORMAL_demo_case3.npy**
  - Expected ADD: 0 (confidence: 100%)
  - Expected COG: 1.0
  - Description: Normal Cognition - High confidence demo case


#### **Alzheimer's Cases:**
- **T1_ALZHEIMERS_demo_case1.npy**
  - Expected ADD: 1 (confidence: 100%)
  - Expected COG: 2.0
  - Description: Alzheimer's Disease - High confidence demo case

- **T1_ALZHEIMERS_demo_case2.npy**
  - Expected ADD: 1 (confidence: 100%)
  - Expected COG: 2.0
  - Description: Alzheimer's Disease - High confidence demo case


#### **Clinical Scans:**
- **T1_REAL_clinical_scan1.nii**
  - Expected ADD: Unknown
  - Expected COG: Unknown
  - Description: Real clinical T1 scan - Unknown diagnosis

- **T1_REAL_clinical_scan2.nii**
  - Expected ADD: Unknown
  - Expected COG: Unknown
  - Description: Real clinical T1 scan - Unknown diagnosis


### **🧪 Testing Strategy:**

1. **Validate with Demo Files First:**
   - Test Normal cases → Expect ADD < 50%, COG < 1.5
   - Test AD cases → Expect ADD > 80%, COG > 1.8

2. **Test Clinical Scans:**
   - Use for real-world validation
   - Compare results with clinical expectations

3. **Performance Metrics:**
   - **Sensitivity**: Correctly identifying AD cases
   - **Specificity**: Correctly identifying Normal cases
   - **Accuracy**: Overall classification performance

### **📝 Usage Instructions:**

1. **Copy to Windows Downloads**: Use provided copy script
2. **Launch Demetify**: Run deployment package
3. **Test in order**: Normal → AD → Clinical
4. **Document results**: Compare with expected values
5. **Validate performance**: Check classification accuracy

### **✅ Success Criteria:**
- Normal cases show low ADD probability (< 50%)
- AD cases show high ADD probability (> 80%)
- Brain region heatmaps generate correctly
- PDF reports download successfully

**Total Files**: 5
**Demo Files**: 3
**Clinical Files**: 2
**Total Size**: ~110.2MB

🎯 **Perfect for focused Normal vs Alzheimer's testing!**
