#!/usr/bin/env python3
"""
Demetify Starter - Works with python3
Simple launcher for Demetify
"""

import subprocess
import sys
import os

def main():
    print("Demetify - AI-Powered Radiologist Assistant")
    print("=" * 50)
    print()
    
    # Check if we're in the right directory
    if not os.path.exists("ncomms2022_frontend.py"):
        print("ERROR: ncomms2022_frontend.py not found")
        print("Please run this script from the demetify_deployment directory")
        return 1
    
    print("Installing dependencies...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements_ncomms2022.txt"
        ])
        print("OK: Dependencies installed")
    except subprocess.CalledProcessError as e:
        print(f"WARNING: Some dependencies may have failed to install: {e}")
    
    print()
    print("Launching Demetify...")
    print("Open your browser to: http://localhost:8501")
    print("Press Ctrl+C to stop the application")
    print()
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "ncomms2022_frontend.py"
        ])
    except KeyboardInterrupt:
        print("\nDemetify stopped by user")
    except FileNotFoundError:
        print("ERROR: Streamlit not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
        print("Retrying launch...")
        subprocess.run([sys.executable, "-m", "streamlit", "run", "ncomms2022_frontend.py"])
    except Exception as e:
        print(f"ERROR: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
