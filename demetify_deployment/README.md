# 🧠 Demetify - AI-Powered Radiologist Assistant

## 📦 **Deployment Package**

This is a complete, self-contained deployment package for Demetify, including all models and dependencies.

---

## 🚀 **Quick Start**

### **Option 1: Automatic Setup (Recommended)**
```bash
python3 setup_and_run.py
```

### **Option 2: Simple Launch**
```bash
python3 start_demetify.py
```

### **Option 3: Manual Setup**
```bash
python3 -m pip install -r requirements_ncomms2022.txt
python3 -m streamlit run ncomms2022_frontend.py
```

### **Access Application**
- Open browser to: `http://localhost:8501`
- Upload MRI scans (.nii or .npy format)
- Get AI analysis with brain region importance
- Download professional PDF reports

---

## 📁 **Package Contents**

### **Core Application Files**
- `ncomms2022_frontend.py` - Main Streamlit application
- `ncomms2022_model.py` - AI model wrapper and inference
- `ncomms2022_preprocessing.py` - MRI preprocessing pipeline
- `requirements_ncomms2022.txt` - Python dependencies

### **AI Model Components**
- `CNN_baseline_new_cross0/` - Pretrained model weights
- `backends/` - Neural network architectures
- `models.py` - Model definitions
- `model_wrappers.py` - Model loading utilities
- `utils.py` - Utility functions
- `config.json` - Model configuration
- `task_config.json` - Task-specific settings

### **Demo Data**
- `demo/` - Sample MRI files for testing
  - `demo1.npy` - Alzheimer's Disease case
  - `demo2.npy` - Alzheimer's Disease case  
  - `demo3.npy` - Cognitive Normal case
  - `demo.csv` - Metadata for demo files

---

## 🖥️ **System Requirements**

### **Minimum Requirements**
- **OS**: Windows 10/11, macOS 10.14+, or Linux
- **RAM**: 8GB (16GB recommended)
- **Storage**: 2GB free space
- **Python**: 3.8 or higher

### **Recommended Requirements**
- **RAM**: 16GB or more
- **GPU**: CUDA-compatible (optional, for faster processing)
- **CPU**: Multi-core processor

---

## 🔧 **Installation Options**

### **Option 1: Standard Installation**
```bash
# Clone or extract this package
cd demetify_deployment

# Install dependencies
pip install -r requirements_ncomms2022.txt

# Run application
streamlit run ncomms2022_frontend.py
```

### **Option 2: Virtual Environment (Recommended)**
```bash
# Create virtual environment
python -m venv demetify_env

# Activate environment
# Windows:
demetify_env\Scripts\activate
# macOS/Linux:
source demetify_env/bin/activate

# Install dependencies
pip install -r requirements_ncomms2022.txt

# Run application
streamlit run ncomms2022_frontend.py
```

### **Option 3: Conda Environment**
```bash
# Create conda environment
conda create -n demetify python=3.9

# Activate environment
conda activate demetify

# Install dependencies
pip install -r requirements_ncomms2022.txt

# Run application
streamlit run ncomms2022_frontend.py
```

---

## 🌐 **Network Deployment**

### **Local Network Access**
```bash
streamlit run ncomms2022_frontend.py --server.address 0.0.0.0 --server.port 8501
```
- Access from other devices: `http://[YOUR_IP]:8501`

### **Custom Port**
```bash
streamlit run ncomms2022_frontend.py --server.port 8080
```

### **Production Deployment**
For production deployment, consider:
- **Docker containerization**
- **Reverse proxy (nginx)**
- **SSL/TLS certificates**
- **Load balancing**

---

## 📊 **Usage Instructions**

1. **Upload MRI Scan**: Drag & drop .nii or .npy files
2. **AI Analysis**: Automatic preprocessing and inference
3. **Brain Region Analysis**: Generate importance heatmaps
4. **PDF Report**: Download professional clinical report
5. **Full-Screen Viewing**: Interactive MRI and heatmap exploration

---

## 🔒 **Security Considerations**

- **Data Privacy**: All processing is local, no data sent to external servers
- **File Validation**: Only .nii and .npy files accepted
- **Memory Management**: Automatic cleanup of temporary files
- **Session Isolation**: Each user session is independent

---

## 🛠️ **Troubleshooting**

### **Common Issues**

**"Module not found" errors:**
```bash
pip install -r requirements_ncomms2022.txt
```

**Memory errors:**
- Ensure 8GB+ RAM available
- Close other applications
- Use smaller MRI files for testing

**Model loading errors:**
- Verify `CNN_baseline_new_cross0/` directory exists
- Check file permissions
- Ensure sufficient disk space

**Port already in use:**
```bash
streamlit run ncomms2022_frontend.py --server.port 8502
```

---

## 📞 **Support**

For technical support or questions:
- **Project Lead**: S. Seshadri
- **Institution**: University of Illinois Urbana-Champaign
- **Purpose**: Radiologist Decision Support Tool

---

## 📄 **License**

This software is provided for research and clinical decision support purposes. Please refer to the LICENSE file for detailed terms and conditions.

---

## 🎯 **Features**

- ✅ **AI-Powered Analysis**: Advanced deep learning for dementia detection
- ✅ **Brain Region Importance**: Interpretable AI with heatmap visualization
- ✅ **Professional Reports**: Clinical-grade PDF documentation
- ✅ **Multiple Formats**: Support for .nii and .npy MRI files
- ✅ **Real-time Processing**: Fast inference and visualization
- ✅ **User-Friendly Interface**: Intuitive web-based application

**Demetify - Accelerating Radiological Diagnosis** 🧠✨
