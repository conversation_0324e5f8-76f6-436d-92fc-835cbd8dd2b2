# NCOMMs2022 Frontend Requirements
# Core dependencies for the Alzheimer's disease assessment frontend

# Streamlit and web framework
streamlit>=1.28.0
plotly>=5.14.0

# Deep learning and ML
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.2.0

# Medical imaging
nibabel>=3.2.0
scikit-image>=0.20.0

# Data handling
pandas>=2.0.0
matplotlib>=3.7.0

# PDF generation
reportlab>=3.6.0

# SHAP for interpretability (optional)
shap>=0.41.0

# Utilities
tqdm>=4.65.0
pathlib2>=2.3.0

# Optional GPU support
# Uncomment if using CUDA
# nvidia-cublas-cu11
# nvidia-cuda-cupti-cu11
# nvidia-cuda-nvrtc-cu11
# nvidia-cuda-runtime-cu11
# nvidia-cudnn-cu11
