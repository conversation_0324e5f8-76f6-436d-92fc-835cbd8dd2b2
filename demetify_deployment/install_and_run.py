#!/usr/bin/env python3
"""
Complete Demetify Installation and Launch Script
Handles pip installation and all dependencies
"""

import subprocess
import sys
import os
import urllib.request
import tempfile

def run_command(cmd, description="", capture_output=True):
    """Run a command and return success status."""
    try:
        if description:
            print(f"Running: {description}")
        
        if capture_output:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return result.returncode == 0, result.stdout, result.stderr
        else:
            result = subprocess.run(cmd, shell=True)
            return result.returncode == 0, "", ""
    except Exception as e:
        return False, "", str(e)

def install_pip():
    """Install pip using get-pip.py."""
    print("Installing pip...")
    
    try:
        # Download get-pip.py
        with tempfile.NamedTemporaryFile(mode='w+b', suffix='.py', delete=False) as f:
            urllib.request.urlretrieve('https://bootstrap.pypa.io/get-pip.py', f.name)
            get_pip_path = f.name
        
        # Install pip
        success, stdout, stderr = run_command(f"python3 {get_pip_path}")
        
        # Clean up
        os.unlink(get_pip_path)
        
        if success:
            print("✅ pip installed successfully")
            return True
        else:
            print(f"❌ pip installation failed: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing pip: {e}")
        return False

def check_and_install_pip():
    """Check if pip is available, install if not."""
    # Check if pip is available
    success, _, _ = run_command("python3 -m pip --version")
    if success:
        print("✅ pip is available")
        return True
    
    print("pip not found, installing...")
    return install_pip()

def install_dependencies():
    """Install required dependencies."""
    print("Installing Demetify dependencies...")
    
    # Core packages needed for Demetify
    packages = [
        "streamlit>=1.28.0",
        "torch>=1.9.0",
        "torchvision>=0.10.0", 
        "numpy>=1.21.0",
        "pandas>=1.3.0",
        "plotly>=5.0.0",
        "nibabel>=3.2.0",
        "scipy>=1.7.0",
        "matplotlib>=3.4.0"
    ]
    
    print("Installing packages one by one...")
    failed_packages = []
    
    for package in packages:
        print(f"Installing {package}...")
        success, stdout, stderr = run_command(f"python3 -m pip install {package}")
        if not success:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
        else:
            print(f"✅ {package} installed")
    
    if failed_packages:
        print(f"\n⚠️ Some packages failed to install: {failed_packages}")
        print("Trying to install from requirements file...")
        success, _, _ = run_command("python3 -m pip install -r requirements_ncomms2022.txt")
        if success:
            print("✅ Requirements installed successfully")
        else:
            print("❌ Some dependencies may be missing")
    
    return True

def test_installation():
    """Test if key components are working."""
    print("\nTesting installation...")
    
    try:
        # Test imports
        import streamlit
        print("✅ Streamlit available")
        
        import torch
        print("✅ PyTorch available")
        
        import numpy
        print("✅ NumPy available")
        
        # Test model loading
        sys.path.append('.')
        from ncomms2022_model import ModelManager
        
        manager = ModelManager()
        available_models = manager.get_available_models()
        
        if available_models:
            print(f"✅ Models found: {available_models}")
            return True
        else:
            print("❌ No models found")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def launch_demetify():
    """Launch the Demetify application."""
    print("\n" + "="*50)
    print("🧠 Launching Demetify...")
    print("Open your browser to: http://localhost:8501")
    print("Press Ctrl+C to stop the application")
    print("="*50)
    
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "ncomms2022_frontend.py"])
    except KeyboardInterrupt:
        print("\nDemetify stopped by user")
    except Exception as e:
        print(f"❌ Error launching Demetify: {e}")

def main():
    print("🧠 Demetify Complete Installation Script")
    print("="*50)
    
    # Check if we're in the right directory
    if not os.path.exists("ncomms2022_frontend.py"):
        print("❌ ERROR: ncomms2022_frontend.py not found")
        print("Please run this script from the demetify_deployment directory")
        return 1
    
    # Check Python version
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ ERROR: Python 3.8+ required, found {version.major}.{version.minor}")
        return 1
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    
    # Install pip if needed
    if not check_and_install_pip():
        print("❌ Failed to install pip. Please install manually:")
        print("   sudo apt update && sudo apt install python3-pip")
        return 1
    
    # Install dependencies
    install_dependencies()
    
    # Test installation
    if not test_installation():
        print("❌ Installation test failed. Some components may not work.")
        print("Attempting to launch anyway...")
    
    # Launch application
    launch_demetify()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
