#!/usr/bin/env python3
"""
Demetify Deployment Verification Script
Checks that all components are properly included and working
"""

import os
import sys
from pathlib import Path

def check_file_exists(filepath, description):
    """Check if a file exists and report status."""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        print(f"✅ {description}: {filepath} ({size:,} bytes)")
        return True
    else:
        print(f"❌ {description}: {filepath} - NOT FOUND")
        return False

def check_directory_exists(dirpath, description):
    """Check if a directory exists and report contents."""
    if os.path.exists(dirpath):
        files = list(os.listdir(dirpath))
        print(f"✅ {description}: {dirpath} ({len(files)} files)")
        return True
    else:
        print(f"❌ {description}: {dirpath} - NOT FOUND")
        return False

def main():
    print("🔍 Demetify Deployment Verification")
    print("="*50)
    
    all_good = True
    
    print("\n📁 Core Application Files:")
    core_files = [
        ("ncomms2022_frontend.py", "Main Streamlit application"),
        ("ncomms2022_model.py", "AI model wrapper"),
        ("ncomms2022_preprocessing.py", "MRI preprocessing pipeline"),
        ("requirements_ncomms2022.txt", "Dependencies list"),
        ("models.py", "Model definitions"),
        ("model_wrappers.py", "Model utilities"),
        ("utils.py", "Utility functions"),
        ("config.json", "Model configuration"),
        ("task_config.json", "Task settings")
    ]
    
    for filepath, description in core_files:
        if not check_file_exists(filepath, description):
            all_good = False
    
    print("\n🧠 AI Model Files:")
    model_files = [
        ("CNN_baseline_new_cross0/ADD_58.pth", "Alzheimer's detection weights"),
        ("CNN_baseline_new_cross0/COG_58.pth", "Cognitive assessment weights"),
        ("CNN_baseline_new_cross0/backbone_58.pth", "Feature extraction weights")
    ]
    
    for filepath, description in model_files:
        if not check_file_exists(filepath, description):
            all_good = False
    
    print("\n🏗️ Neural Network Architectures:")
    backend_files = [
        ("backends/DenseNet.py", "DenseNet architecture"),
        ("backends/ResNet.py", "ResNet architecture"),
        ("backends/SENet.py", "SENet architecture")
    ]
    
    for filepath, description in backend_files:
        if not check_file_exists(filepath, description):
            all_good = False
    
    print("\n📊 Demo Data:")
    demo_files = [
        ("demo/mri/demo1.npy", "AD case 1"),
        ("demo/mri/demo2.npy", "AD case 2"),
        ("demo/mri/demo3.npy", "Normal case"),
        ("demo/demo.csv", "Demo metadata"),
        ("demo/demo_eval.csv", "Demo evaluation data")
    ]
    
    for filepath, description in demo_files:
        if not check_file_exists(filepath, description):
            all_good = False
    
    print("\n🚀 Deployment Scripts:")
    script_files = [
        ("install_and_run.py", "Complete installation script"),
        ("start_demetify.py", "Simple launcher"),
        ("setup_and_run.py", "Setup and run script"),
        ("deploy.py", "Advanced deployment"),
        ("deploy.sh", "Shell script launcher")
    ]
    
    for filepath, description in script_files:
        if not check_file_exists(filepath, description):
            all_good = False
    
    print("\n🔧 Testing Core Functionality:")
    
    # Test model loading
    try:
        sys.path.append('.')
        from ncomms2022_model import ModelManager
        
        manager = ModelManager()
        available_models = manager.get_available_models()
        
        if available_models:
            print(f"✅ Model loading: Found {len(available_models)} models")
            
            # Test loading a model
            model = manager.load_model(available_models[0], device='cpu')
            print("✅ Model instantiation: Success")
        else:
            print("❌ Model loading: No models found")
            all_good = False
            
    except Exception as e:
        print(f"❌ Model loading: Error - {e}")
        all_good = False
    
    # Test preprocessing
    try:
        from ncomms2022_preprocessing import NCOMMs2022Preprocessor
        
        preprocessor = NCOMMs2022Preprocessor()
        print("✅ Preprocessing: Module loads successfully")
        
        # Test with demo file
        if os.path.exists("demo/mri/demo1.npy"):
            processed_data = preprocessor.preprocess_mri(
                "demo/mri/demo1.npy", 
                file_type='npy', 
                apply_skull_stripping=False,
                apply_normalization=False
            )
            
            if processed_data is not None:
                print(f"✅ Demo processing: Success - shape {processed_data.shape}")
            else:
                print("❌ Demo processing: Failed")
                all_good = False
        
    except Exception as e:
        print(f"❌ Preprocessing: Error - {e}")
        all_good = False
    
    print("\n" + "="*50)
    
    if all_good:
        print("🎉 DEPLOYMENT VERIFICATION PASSED!")
        print("✅ All components are properly included and working")
        print("🚀 Ready to launch Demetify!")
        print("\nTo start the application, run:")
        print("   python3 install_and_run.py")
        return 0
    else:
        print("❌ DEPLOYMENT VERIFICATION FAILED!")
        print("Some components are missing or not working properly")
        print("Please check the missing files and try again")
        return 1

if __name__ == "__main__":
    sys.exit(main())
