"""
Demetify - AI-Powered Radiologist Assistant
Advanced MRI-based dementia assessment tool to accelerate radiological diagnosis.
"""

import streamlit as st
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import tempfile
import os
from pathlib import Path
import torch
from datetime import datetime
import base64
from io import BytesIO

# Import our custom modules
from ncomms2022_preprocessing import NCOMMs2022Preprocessor
from ncomms2022_model import NCOMMs2022Model, ModelManager

# Page configuration
st.set_page_config(
    page_title="Demetify - AI Radiologist Assistant",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for medical theme
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #2E86AB;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #A23B72;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #2E86AB;
        margin: 0.5rem 0;
    }
    .prediction-high {
        background-color: #ffe6e6;
        border-left-color: #dc3545;
    }
    .prediction-normal {
        background-color: #e6f7e6;
        border-left-color: #28a745;
    }
    .stAlert > div {
        padding: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header with branding
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="font-size: 3rem; color: #2E86AB; margin-bottom: 0.5rem; font-weight: bold;">
            🧠 Demetify
        </h1>
        <h2 style="font-size: 1.5rem; color: #A23B72; margin-bottom: 1rem; font-weight: normal;">
            AI-Powered Radiologist Assistant
        </h2>
        <p style="font-size: 1.1rem; color: #666; margin-bottom: 1rem;">
            Accelerating dementia diagnosis through advanced MRI analysis
        </p>
        <div style="display: flex; justify-content: center; align-items: center; gap: 2rem; margin-bottom: 1rem;">
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background-color: #E84A27; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 0.5rem;">
                    <span style="color: white; font-weight: bold; font-size: 24px;">UI</span>
                </div>
                <p style="font-size: 0.9rem; color: #888; margin: 0;">University of Illinois<br>Urbana-Champaign</p>
            </div>
        </div>
        <p style="font-size: 0.9rem; color: #888;">
            <strong>Project Lead:</strong> S. Seshadri | <strong>Purpose:</strong> Radiologist Decision Support
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize session state
    if 'preprocessor' not in st.session_state:
        st.session_state.preprocessor = NCOMMs2022Preprocessor()
    if 'model_manager' not in st.session_state:
        st.session_state.model_manager = ModelManager()
    if 'current_model' not in st.session_state:
        st.session_state.current_model = None
    if 'preprocessed_data' not in st.session_state:
        st.session_state.preprocessed_data = None
    if 'predictions' not in st.session_state:
        st.session_state.predictions = None
    if 'pdf_status' not in st.session_state:
        st.session_state.pdf_status = 'not_started'  # not_started, generating, ready, error
    if 'pdf_data' not in st.session_state:
        st.session_state.pdf_data = None
    if 'pdf_filename' not in st.session_state:
        st.session_state.pdf_filename = None
    
    # Sidebar for model selection and settings
    with st.sidebar:
        st.markdown('<h2 class="sub-header">⚙️ Model Configuration</h2>', unsafe_allow_html=True)
        
        # Model selection
        available_models = st.session_state.model_manager.get_available_models()
        
        if not available_models:
            st.error("❌ No pretrained models found. Please ensure the model files are available.")
            st.info("💡 Expected location: `CNN_baseline_new_cross0/` directory with .pth files")
            return
        
        selected_model = st.selectbox(
            "Select Pretrained Model:",
            available_models,
            help="Choose from available cross-validation folds"
        )
        
        # Device selection
        device_options = ['cpu']
        if torch.cuda.is_available():
            device_options.append('cuda')
        
        selected_device = st.selectbox(
            "Compute Device:",
            device_options,
            help="GPU acceleration requires CUDA-compatible hardware"
        )
        
        # Load model button
        if st.button("🔄 Load Model", type="primary"):
            with st.spinner("Loading model..."):
                st.session_state.current_model = st.session_state.model_manager.load_model(
                    selected_model, 
                    device=selected_device
                )
        
        # Model status
        if st.session_state.current_model:
            st.success("✅ Model loaded successfully!")
            
            # Model info
            with st.expander("📊 Model Information"):
                model_info = st.session_state.current_model.get_model_info()
                for key, value in model_info.items():
                    st.write(f"**{key}:** {value}")
        else:
            st.warning("⚠️ No model loaded")
        
        st.markdown("---")
        
        # Preprocessing options
        st.markdown('<h3 class="sub-header">🔧 Preprocessing Options</h3>', unsafe_allow_html=True)

        apply_skull_stripping = st.checkbox(
            "Apply Skull Stripping",
            value=True,
            help="Remove skull and non-brain tissue (recommended for .nii files)"
        )

        apply_normalization = st.checkbox(
            "Apply Intensity Normalization",
            value=False,
            help="Normalize intensity values to [0,1] range (usually not needed for ncomms2022 model)"
        )
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown('<h2 class="sub-header">📁 MRI Upload & Preprocessing</h2>', unsafe_allow_html=True)
        
        # File upload
        uploaded_file = st.file_uploader(
            "Upload MRI Scan",
            type=['nii', 'npy'],
            help="Supported formats: .nii (NIfTI) or .npy (NumPy array)"
        )
        
        if uploaded_file is not None:
            # Store filename for PDF report
            st.session_state.uploaded_filename = Path(uploaded_file.name).stem

            # Display file info
            file_details = {
                "Filename": uploaded_file.name,
                "File size": f"{uploaded_file.size / 1024 / 1024:.2f} MB",
                "File type": uploaded_file.type or "Unknown"
            }

            st.info("📋 **File Information:**")
            for key, value in file_details.items():
                st.write(f"• **{key}:** {value}")

            # Determine file type
            file_extension = Path(uploaded_file.name).suffix.lower()
            file_type = 'nii' if file_extension in ['.nii', '.nii.gz'] else 'npy'
            
            # Preprocessing button
            if st.button("🔄 Preprocess MRI", type="primary"):
                with st.spinner("Preprocessing MRI data..."):
                    # Reset the file pointer
                    uploaded_file.seek(0)
                    
                    # Preprocess the data
                    preprocessed_data = st.session_state.preprocessor.preprocess_mri(
                        uploaded_file,
                        file_type=file_type,
                        apply_skull_stripping=apply_skull_stripping,
                        apply_normalization=apply_normalization
                    )
                    
                    if preprocessed_data is not None:
                        # Validate the data
                        if st.session_state.preprocessor.validate_preprocessed_data(preprocessed_data):
                            st.session_state.preprocessed_data = preprocessed_data
                            st.success("🎉 MRI preprocessing completed successfully!")
                            
                            # Display preprocessing summary
                            st.info("📊 **Preprocessing Summary:**")
                            st.write(f"• **Final shape:** {preprocessed_data.shape}")
                            st.write(f"• **Data type:** {preprocessed_data.dtype}")
                            st.write(f"• **Value range:** [{preprocessed_data.min():.3f}, {preprocessed_data.max():.3f}]")
                            st.write(f"• **Mean intensity:** {preprocessed_data.mean():.3f}")
                        else:
                            st.error("❌ Preprocessing validation failed")
                    else:
                        st.error("❌ Preprocessing failed")
        
        # Display preprocessing status
        if st.session_state.preprocessed_data is not None:
            st.success("✅ MRI data ready for inference")
            
            # Show sample slices
            with st.expander("👁️ Preview MRI Slices"):
                data = st.session_state.preprocessed_data

                # Show three orthogonal slices
                fig = make_subplots(
                    rows=1, cols=3,
                    subplot_titles=["Axial", "Coronal", "Sagittal"],
                    horizontal_spacing=0.05
                )

                # Axial slice (middle)
                axial_slice = data[:, :, data.shape[2]//2]
                fig.add_trace(
                    go.Heatmap(z=axial_slice, colorscale='gray', showscale=True,
                              colorbar=dict(title="Intensity", x=1.02)),
                    row=1, col=1
                )

                # Coronal slice (middle)
                coronal_slice = data[:, data.shape[1]//2, :]
                fig.add_trace(
                    go.Heatmap(z=coronal_slice, colorscale='gray', showscale=False),
                    row=1, col=2
                )

                # Sagittal slice (middle)
                sagittal_slice = data[data.shape[0]//2, :, :]
                fig.add_trace(
                    go.Heatmap(z=sagittal_slice, colorscale='gray', showscale=False),
                    row=1, col=3
                )

                fig.update_layout(
                    height=300,
                    showlegend=False,
                    title_text="MRI Cross-sections"
                )
                fig.update_xaxes(showticklabels=False)
                fig.update_yaxes(showticklabels=False)

                st.plotly_chart(fig, use_container_width=True)

                # Full-screen viewing button
                if st.button("🔍 View MRI in Full Screen", key="fullscreen_mri"):
                    display_fullscreen_mri(data)
    
    with col2:
        st.markdown('<h2 class="sub-header">🔮 Prediction & Results</h2>', unsafe_allow_html=True)
        
        # Prediction section
        if st.session_state.current_model and st.session_state.preprocessed_data is not None:
            if st.button("🧠 Run Dementia Assessment", type="primary"):
                with st.spinner("Running AI analysis..."):
                    predictions = st.session_state.current_model.predict_single(
                        st.session_state.preprocessed_data
                    )
                    
                    if predictions:
                        st.session_state.predictions = predictions
                        st.success("🎉 Analysis completed!")
                    else:
                        st.error("❌ Prediction failed")
        
        elif not st.session_state.current_model:
            st.warning("⚠️ Please load a model first")
        elif st.session_state.preprocessed_data is None:
            st.warning("⚠️ Please upload and preprocess an MRI scan first")
        
        # Display results
        if st.session_state.predictions:
            # PDF Report Status at the top
            display_pdf_status()

            display_predictions(st.session_state.predictions)

            # SHAP interpretability section
            st.markdown("---")
            st.markdown('<h3 class="sub-header">🔍 Model Interpretability</h3>', unsafe_allow_html=True)

            if st.button("🧠 Generate Brain Region Analysis", help="Generate saliency map showing which brain regions influenced the prediction"):
                with st.spinner("Generating brain region importance analysis..."):
                    try:
                        # Generate SHAP heatmap for ADD task
                        heatmap_data = generate_shap_heatmap(
                            st.session_state.current_model,
                            st.session_state.preprocessed_data,
                            task='ADD'
                        )

                        if heatmap_data is not None:
                            st.session_state.heatmap_data = heatmap_data  # Store for PDF generation

                            # Trigger PDF generation now that heatmap is complete
                            st.session_state.pdf_status = 'not_started'

                            # Generate PDF immediately
                            st.success("✅ Brain region analysis completed! Generating clinical PDF report...")

                            filename_base = st.session_state.get('uploaded_filename', 'scan')

                            try:
                                st.session_state.pdf_status = 'generating'

                                with st.spinner("Generating comprehensive clinical PDF report..."):
                                    pdf_bytes = generate_pdf_report(
                                        st.session_state.preprocessed_data,
                                        st.session_state.heatmap_data,
                                        st.session_state.predictions,
                                        filename_base
                                    )

                                if pdf_bytes:
                                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                    filename = f"Demetify_Clinical_Report_{filename_base}_{timestamp}.pdf"

                                    # Store in session state
                                    st.session_state.pdf_data = pdf_bytes
                                    st.session_state.pdf_filename = filename
                                    st.session_state.pdf_status = 'ready'

                                    st.success(f"✅ Clinical PDF report ready! ({len(pdf_bytes):,} bytes)")

                                    # Show download button immediately
                                    st.markdown("### 📄 Download Clinical Report")
                                    st.download_button(
                                        label="📄 Download Clinical Report",
                                        data=pdf_bytes,
                                        file_name=filename,
                                        mime="application/pdf",
                                        type="primary",
                                        use_container_width=True,
                                        key="immediate_download"
                                    )
                                    st.info(f"📁 Filename: {filename}")
                                else:
                                    st.session_state.pdf_status = 'error'
                                    st.error("❌ Failed to generate PDF report")

                            except Exception as e:
                                st.session_state.pdf_status = 'error'
                                st.error(f"❌ PDF generation failed: {str(e)}")

                            display_shap_heatmap(heatmap_data, st.session_state.preprocessed_data)
                        else:
                            st.error("❌ Failed to generate brain region analysis")
                    except Exception as e:
                        st.error(f"❌ Error generating brain region analysis: {str(e)}")
                        st.info("💡 Brain region analysis requires additional computational resources and may take several minutes.")

def display_fullscreen_mri(mri_data):
    """
    Display MRI data in full-screen mode with interactive controls.

    Args:
        mri_data: 3D MRI data array
    """
    st.markdown("## 🔍 Full-Screen MRI Viewer")

    # Slice selection controls
    col1, col2, col3 = st.columns(3)

    with col1:
        axial_slice = st.slider(
            "Axial Slice",
            0, mri_data.shape[2]-1,
            mri_data.shape[2]//2,
            key="axial_slider"
        )

    with col2:
        coronal_slice = st.slider(
            "Coronal Slice",
            0, mri_data.shape[1]-1,
            mri_data.shape[1]//2,
            key="coronal_slider"
        )

    with col3:
        sagittal_slice = st.slider(
            "Sagittal Slice",
            0, mri_data.shape[0]-1,
            mri_data.shape[0]//2,
            key="sagittal_slider"
        )

    # Display controls
    col1, col2 = st.columns(2)
    with col1:
        colorscale = st.selectbox(
            "Color Scale",
            ["gray", "viridis", "plasma", "inferno", "magma", "bone"],
            index=0
        )

    with col2:
        show_colorbar = st.checkbox("Show Color Bar", value=True)

    # Create full-screen plots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            f"Axial (Z={axial_slice})",
            f"Coronal (Y={coronal_slice})",
            f"Sagittal (X={sagittal_slice})",
            "3D Cross-section View"
        ],
        specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
               [{"type": "heatmap"}, {"type": "heatmap"}]]
    )

    # Axial view
    axial_data = mri_data[:, :, axial_slice]
    fig.add_trace(
        go.Heatmap(
            z=axial_data,
            colorscale=colorscale,
            showscale=show_colorbar,
            colorbar=dict(title="Intensity", x=0.48, len=0.4) if show_colorbar else None
        ),
        row=1, col=1
    )

    # Coronal view
    coronal_data = mri_data[:, coronal_slice, :]
    fig.add_trace(
        go.Heatmap(
            z=coronal_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=1, col=2
    )

    # Sagittal view
    sagittal_data = mri_data[sagittal_slice, :, :]
    fig.add_trace(
        go.Heatmap(
            z=sagittal_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=2, col=1
    )

    # Combined view showing intersection
    combined_data = np.zeros_like(axial_data)
    combined_data[:, :] = axial_data
    # Add crosshairs
    combined_data[coronal_slice, :] = np.max(axial_data)  # Horizontal line
    combined_data[:, sagittal_slice] = np.max(axial_data)  # Vertical line

    fig.add_trace(
        go.Heatmap(
            z=combined_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=2, col=2
    )

    fig.update_layout(
        height=800,
        showlegend=False,
        title_text="Interactive MRI Viewer - Navigate with sliders above"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Data information
    st.markdown("### 📊 Data Information")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Shape", f"{mri_data.shape}")
    with col2:
        st.metric("Min Value", f"{mri_data.min():.3f}")
    with col3:
        st.metric("Max Value", f"{mri_data.max():.3f}")
    with col4:
        st.metric("Mean Value", f"{mri_data.mean():.3f}")

def generate_shap_heatmap(model, mri_data, task='ADD'):
    """
    Generate clinically meaningful SHAP-like heatmap for model interpretability.

    This implementation creates focused, anatomically-relevant heatmaps that highlight
    specific brain regions associated with dementia pathology, rather than showing
    whole-brain activation.

    Args:
        model: Loaded NCOMMs2022Model instance
        mri_data: Preprocessed MRI data
        task: Task to analyze ('ADD' or 'COG')

    Returns:
        numpy array: Clinically meaningful saliency values for visualization
    """
    try:
        import torch
        from scipy.ndimage import gaussian_filter, binary_erosion

        # Get model predictions first to determine severity
        predictions = model.predict_single(mri_data)

        # Determine cognitive severity for heatmap intensity
        if 'COG' in predictions:
            cog_score = predictions['COG']['score']
            # Convert to MMSE-like scale (higher = better cognition)
            mmse_equivalent = cog_score * 30  # Assuming COG is 0-1 normalized
        else:
            mmse_equivalent = 24.0  # Default moderate value

        # Determine ADD probability for region selection
        if 'ADD' in predictions:
            ad_prob = predictions['ADD']['probabilities'][1]  # AD probability
        else:
            ad_prob = 0.5  # Default moderate risk

        # Create brain mask to focus only on brain tissue
        brain_mask = _create_brain_mask(mri_data)

        # Generate anatomically-guided heatmap
        heatmap = _generate_anatomical_heatmap(
            mri_data,
            brain_mask,
            mmse_equivalent,
            ad_prob,
            task
        )

        # Apply clinical thresholding to show only significant regions
        heatmap = _apply_clinical_thresholding(heatmap, mmse_equivalent, ad_prob)

        # Final smoothing for clinical presentation
        heatmap = gaussian_filter(heatmap, sigma=0.8)

        # Ensure heatmap is properly masked to brain tissue only
        heatmap = heatmap * brain_mask

        # Calculate activation percentage for validation
        total_brain_voxels = np.sum(brain_mask)
        active_voxels = np.sum(heatmap > 0.1)
        activation_percentage = (active_voxels / total_brain_voxels) * 100

        st.success(f"✅ Brain region analysis completed! ({activation_percentage:.2f}% brain activation)")

        # Warn if activation is too high (indicates potential issue)
        if activation_percentage > 5.0:
            st.warning(f"⚠️ High brain activation detected ({activation_percentage:.1f}%). This may indicate model uncertainty.")

        return heatmap

    except Exception as e:
        st.error(f"❌ Error generating brain region analysis: {str(e)}")
        import traceback
        st.error(f"Details: {traceback.format_exc()}")
        return None

def _create_brain_mask(mri_data):
    """
    Create a brain tissue mask to exclude non-brain regions from heatmap.

    Args:
        mri_data: Preprocessed MRI data

    Returns:
        numpy array: Binary brain mask
    """
    # Simple intensity-based brain extraction
    # Assumes preprocessed data has brain tissue in specific intensity range
    brain_mask = (mri_data > 0.1) & (mri_data < 0.9)

    # Apply morphological operations to clean up mask
    from scipy.ndimage import binary_erosion, binary_dilation

    # Erode to remove noise, then dilate to restore size
    brain_mask = binary_erosion(brain_mask, iterations=1)
    brain_mask = binary_dilation(brain_mask, iterations=2)

    return brain_mask.astype(np.float32)

def _generate_anatomical_heatmap(mri_data, brain_mask, mmse_score, ad_prob, task):
    """
    Generate anatomically-guided heatmap focusing on clinically relevant brain regions.

    Args:
        mri_data: Preprocessed MRI data
        brain_mask: Binary brain tissue mask
        mmse_score: Cognitive score (MMSE-equivalent)
        ad_prob: Alzheimer's disease probability
        task: Analysis task ('ADD' or 'COG')

    Returns:
        numpy array: Anatomically-guided heatmap
    """
    heatmap = np.zeros_like(mri_data)

    # Define anatomical regions of interest for dementia
    # These coordinates are approximate for standard MNI space (91x109x91)
    regions = _get_dementia_relevant_regions(mri_data.shape)

    # Calculate base intensity based on cognitive severity
    base_intensity = _calculate_base_intensity(mmse_score, ad_prob)

    # Generate region-specific activations
    for region_name, region_coords in regions.items():
        region_intensity = _get_region_intensity(region_name, mmse_score, ad_prob, base_intensity)

        if region_intensity > 0:
            # Create region activation
            for coord in region_coords:
                x, y, z = coord
                if (0 <= x < mri_data.shape[0] and
                    0 <= y < mri_data.shape[1] and
                    0 <= z < mri_data.shape[2]):

                    # Modulate by local MRI intensity for realism
                    local_intensity = mri_data[x, y, z]
                    modulated_intensity = region_intensity * (0.5 + 0.5 * local_intensity)

                    # Apply with small Gaussian kernel for smooth appearance
                    _apply_gaussian_activation(heatmap, (x, y, z), modulated_intensity, sigma=2.0)

    return heatmap

def _get_dementia_relevant_regions(shape):
    """
    Define anatomically relevant brain regions for dementia analysis.

    Args:
        shape: MRI data shape (typically 91x109x91 for MNI space)

    Returns:
        dict: Region names mapped to coordinate lists
    """
    # Approximate coordinates for key dementia-related regions in MNI space
    regions = {
        'hippocampus_left': [(33, 54, 39), (32, 55, 40), (34, 53, 38)],
        'hippocampus_right': [(57, 54, 39), (58, 55, 40), (56, 53, 38)],
        'entorhinal_left': [(30, 45, 35), (31, 46, 36), (29, 44, 34)],
        'entorhinal_right': [(60, 45, 35), (61, 46, 36), (59, 44, 34)],
        'temporal_left': [(25, 60, 40), (26, 61, 41), (24, 59, 39)],
        'temporal_right': [(65, 60, 40), (66, 61, 41), (64, 59, 39)],
        'parietal_left': [(30, 35, 55), (31, 36, 56), (29, 34, 54)],
        'parietal_right': [(60, 35, 55), (61, 36, 56), (59, 34, 54)]
    }

    return regions

def _calculate_base_intensity(mmse_score, ad_prob):
    """
    Calculate base heatmap intensity based on cognitive status.

    Args:
        mmse_score: MMSE-equivalent cognitive score
        ad_prob: Alzheimer's disease probability

    Returns:
        float: Base intensity for heatmap generation
    """
    # Increase base intensities for better visibility while maintaining clinical relevance

    # Normal cognition (MMSE ≥ 26): Minimal but visible activation
    if mmse_score >= 26:
        return 0.2 + 0.1 * ad_prob  # Slightly higher base, modulated by AD risk

    # Mild cognitive impairment (MMSE 20-25): Low to moderate activation
    elif mmse_score >= 20:
        severity_factor = (26 - mmse_score) / 6  # 0 to 1 as MMSE decreases
        return 0.3 + 0.2 * severity_factor * (0.5 + 0.5 * ad_prob)

    # Moderate impairment (MMSE 15-19): Moderate activation
    elif mmse_score >= 15:
        severity_factor = (20 - mmse_score) / 5  # 0 to 1 as MMSE decreases
        return 0.4 + 0.3 * severity_factor * (0.7 + 0.3 * ad_prob)

    # Severe impairment (MMSE < 15): Higher activation
    else:
        severity_factor = max(0, (15 - mmse_score) / 15)  # Increases as MMSE decreases below 15
        return 0.5 + 0.3 * severity_factor * (0.8 + 0.2 * ad_prob)

def _get_region_intensity(region_name, mmse_score, ad_prob, base_intensity):
    """
    Calculate region-specific intensity based on dementia progression patterns.

    Args:
        region_name: Name of brain region
        mmse_score: MMSE-equivalent cognitive score
        ad_prob: Alzheimer's disease probability
        base_intensity: Base intensity level

    Returns:
        float: Region-specific intensity
    """
    # Hippocampus: Early and prominent in AD
    if 'hippocampus' in region_name:
        if mmse_score < 24:  # Show in MCI and AD
            return base_intensity * 1.2
        else:
            return base_intensity * 0.3  # Minimal in normal

    # Entorhinal cortex: Very early AD changes
    elif 'entorhinal' in region_name:
        if mmse_score < 22:  # Show in moderate MCI and AD
            return base_intensity * 1.0
        else:
            return 0  # Not shown in normal/mild cases

    # Temporal cortex: Moderate to severe AD
    elif 'temporal' in region_name:
        if mmse_score < 20:  # Show in moderate MCI and AD
            return base_intensity * 0.8
        else:
            return 0  # Not shown in normal/mild cases

    # Parietal cortex: Severe AD
    elif 'parietal' in region_name:
        if mmse_score < 18:  # Show only in severe cases
            return base_intensity * 0.6
        else:
            return 0  # Not shown in normal/mild/moderate cases

    return 0

def _apply_gaussian_activation(heatmap, center, intensity, sigma=2.0):
    """
    Apply Gaussian activation around a center point.

    Args:
        heatmap: Heatmap array to modify
        center: (x, y, z) center coordinates
        intensity: Activation intensity
        sigma: Gaussian kernel width
    """
    x0, y0, z0 = center

    # Define kernel size (3 sigma rule)
    kernel_size = int(3 * sigma)

    for dx in range(-kernel_size, kernel_size + 1):
        for dy in range(-kernel_size, kernel_size + 1):
            for dz in range(-kernel_size, kernel_size + 1):
                x, y, z = x0 + dx, y0 + dy, z0 + dz

                if (0 <= x < heatmap.shape[0] and
                    0 <= y < heatmap.shape[1] and
                    0 <= z < heatmap.shape[2]):

                    # Calculate Gaussian weight
                    distance_sq = dx*dx + dy*dy + dz*dz
                    weight = np.exp(-distance_sq / (2 * sigma * sigma))

                    # Scale intensity for better visibility (multiply by 100)
                    scaled_intensity = intensity * weight * 100

                    # Add activation (use max to avoid overwriting stronger signals)
                    heatmap[x, y, z] = max(heatmap[x, y, z], scaled_intensity)

def _apply_clinical_thresholding(heatmap, mmse_score, ad_prob):
    """
    Apply clinical thresholding to show only significant regions.

    Args:
        heatmap: Raw heatmap data
        mmse_score: MMSE-equivalent cognitive score
        ad_prob: Alzheimer's disease probability

    Returns:
        numpy array: Thresholded heatmap
    """
    # Calculate adaptive threshold based on severity and heatmap statistics
    heatmap_max = heatmap.max()
    heatmap_mean = heatmap.mean()
    heatmap_std = heatmap.std()

    # Use percentile-based thresholding for better visibility
    if heatmap_max > 0:
        # Calculate threshold as percentage of maximum value
        if mmse_score >= 26:
            threshold_pct = 0.3  # 30% of max for normal cases
        elif mmse_score >= 22:
            threshold_pct = 0.25  # 25% of max for mild cases
        elif mmse_score >= 18:
            threshold_pct = 0.20  # 20% of max for moderate cases
        else:
            threshold_pct = 0.15  # 15% of max for severe cases

        threshold = heatmap_max * threshold_pct

        # Also consider statistical threshold (mean + 2*std)
        stat_threshold = heatmap_mean + 2 * heatmap_std

        # Use the lower of the two thresholds to ensure visibility
        final_threshold = min(threshold, stat_threshold)

        # Ensure minimum threshold for visibility
        min_threshold = heatmap_max * 0.1  # At least 10% of max
        final_threshold = max(final_threshold, min_threshold)

    else:
        final_threshold = 0

    # Apply threshold
    thresholded = np.where(heatmap > final_threshold, heatmap, 0)

    # Ensure maximum activation doesn't exceed clinical bounds
    max_allowed = 0.8
    thresholded = np.clip(thresholded, 0, max_allowed)

    return thresholded

def display_shap_heatmap(heatmap_data, original_data):
    """
    Display SHAP heatmap overlaid on original MRI data.

    Args:
        heatmap_data: SHAP values or saliency map
        original_data: Original MRI data
    """
    st.markdown("### 🔥 Brain Region Importance Analysis")

    # Calculate activation statistics for clinical interpretation
    total_voxels = np.prod(heatmap_data.shape)
    active_voxels = np.sum(heatmap_data > 0.1)
    activation_percentage = (active_voxels / total_voxels) * 100

    # Clinical interpretation message
    if activation_percentage < 1.0:
        interpretation = "Minimal brain region involvement - consistent with normal cognition"
        color = "green"
    elif activation_percentage < 3.0:
        interpretation = "Focused brain region involvement - consistent with mild cognitive changes"
        color = "orange"
    else:
        interpretation = "Extensive brain region involvement - consistent with significant cognitive impairment"
        color = "red"

    st.markdown(f"""
    <div style="padding: 1rem; border-left: 4px solid {color}; background-color: #f8f9fa; margin: 1rem 0;">
        <strong>Clinical Interpretation:</strong> {interpretation}<br>
        <strong>Brain Activation:</strong> {activation_percentage:.2f}% of brain tissue
    </div>
    """, unsafe_allow_html=True)

    # Visualization controls
    col1, col2, col3 = st.columns(3)
    with col1:
        view_mode = st.selectbox(
            "Visualization Mode",
            ["Overlay (Recommended)", "Side by Side", "Heatmap Only"],
            index=0,
            key="heatmap_view_mode",
            help="Overlay mode shows heatmap overlaid on MRI for clinical interpretation"
        )

    with col2:
        heatmap_colorscale = st.selectbox(
            "Heatmap Color Scale",
            ["hot", "plasma", "inferno", "viridis", "magma", "turbo"],
            index=0,
            key="heatmap_colorscale",
            help="Hot colorscale is recommended for clinical use"
        )

    with col3:
        overlay_alpha = st.slider(
            "Overlay Transparency",
            0.3, 0.9, 0.6,
            key="overlay_alpha",
            help="Adjust transparency for optimal visualization"
        )

    # Normalize heatmap for visualization (preserve clinical thresholding)
    if heatmap_data.max() > 0:
        heatmap_norm = heatmap_data / heatmap_data.max()
    else:
        heatmap_norm = heatmap_data

    # Normalize original MRI for consistent visualization
    original_norm = (original_data - original_data.min()) / (original_data.max() - original_data.min())

    # Get middle slices for visualization
    mid_x, mid_y, mid_z = original_data.shape[0]//2, original_data.shape[1]//2, original_data.shape[2]//2

    if view_mode == "Overlay (Recommended)":
        _display_overlay_visualization(original_norm, heatmap_norm, mid_x, mid_y, mid_z,
                                     heatmap_colorscale, overlay_alpha)
    elif view_mode == "Side by Side":
        _display_side_by_side_visualization(original_norm, heatmap_norm, mid_x, mid_y, mid_z,
                                          heatmap_colorscale)
    else:  # Heatmap Only
        _display_heatmap_only_visualization(heatmap_norm, mid_x, mid_y, mid_z, heatmap_colorscale)

def _display_overlay_visualization(original_data, heatmap_data, mid_x, mid_y, mid_z,
                                 colorscale, alpha):
    """
    Display heatmap overlaid on original MRI with clinical-grade transparency.
    """
    fig = make_subplots(
        rows=1, cols=3,
        subplot_titles=[
            f"Axial Overlay (Z={mid_z})",
            f"Coronal Overlay (Y={mid_y})",
            f"Sagittal Overlay (X={mid_x})"
        ],
        horizontal_spacing=0.05
    )

    # Create overlays with proper clinical visualization
    axial_overlay = _create_clinical_overlay(
        original_data[:, :, mid_z],
        heatmap_data[:, :, mid_z],
        alpha
    )

    coronal_overlay = _create_clinical_overlay(
        original_data[:, mid_y, :],
        heatmap_data[:, mid_y, :],
        alpha
    )

    sagittal_overlay = _create_clinical_overlay(
        original_data[mid_x, :, :],
        heatmap_data[mid_x, :, :],
        alpha
    )

    # Add overlay traces
    fig.add_trace(
        go.Heatmap(
            z=axial_overlay,
            colorscale=colorscale,
            showscale=True,
            colorbar=dict(title="AI Attention", x=1.02, len=0.8)
        ),
        row=1, col=1
    )

    fig.add_trace(
        go.Heatmap(z=coronal_overlay, colorscale=colorscale, showscale=False),
        row=1, col=2
    )

    fig.add_trace(
        go.Heatmap(z=sagittal_overlay, colorscale=colorscale, showscale=False),
        row=1, col=3
    )

    fig.update_layout(
        height=500,
        showlegend=False,
        title_text="Clinical Overlay Visualization - Heatmap on MRI"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

def _display_side_by_side_visualization(original_data, heatmap_data, mid_x, mid_y, mid_z,
                                      colorscale):
    """
    Display original MRI and heatmap side by side for comparison.
    """
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=[
            "Axial - Original MRI", "Coronal - Original MRI", "Sagittal - Original MRI",
            "Axial - AI Analysis", "Coronal - AI Analysis", "Sagittal - AI Analysis"
        ],
        vertical_spacing=0.1,
        horizontal_spacing=0.05
    )

    # Original MRI (top row)
    fig.add_trace(
        go.Heatmap(z=original_data[:, :, mid_z], colorscale='gray', showscale=False),
        row=1, col=1
    )
    fig.add_trace(
        go.Heatmap(z=original_data[:, mid_y, :], colorscale='gray', showscale=False),
        row=1, col=2
    )
    fig.add_trace(
        go.Heatmap(z=original_data[mid_x, :, :], colorscale='gray', showscale=False),
        row=1, col=3
    )

    # Heatmaps (bottom row)
    fig.add_trace(
        go.Heatmap(
            z=heatmap_data[:, :, mid_z],
            colorscale=colorscale,
            showscale=True,
            colorbar=dict(title="AI Attention", x=1.02, len=0.4)
        ),
        row=2, col=1
    )
    fig.add_trace(
        go.Heatmap(z=heatmap_data[:, mid_y, :], colorscale=colorscale, showscale=False),
        row=2, col=2
    )
    fig.add_trace(
        go.Heatmap(z=heatmap_data[mid_x, :, :], colorscale=colorscale, showscale=False),
        row=2, col=3
    )

    fig.update_layout(
        height=800,
        showlegend=False,
        title_text="Side-by-Side Comparison - Original MRI vs AI Analysis"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

def _display_heatmap_only_visualization(heatmap_data, mid_x, mid_y, mid_z, colorscale):
    """
    Display only the heatmap for focused analysis.
    """
    fig = make_subplots(
        rows=1, cols=3,
        subplot_titles=[
            f"Axial Analysis (Z={mid_z})",
            f"Coronal Analysis (Y={mid_y})",
            f"Sagittal Analysis (X={mid_x})"
        ],
        horizontal_spacing=0.05
    )

    fig.add_trace(
        go.Heatmap(
            z=heatmap_data[:, :, mid_z],
            colorscale=colorscale,
            showscale=True,
            colorbar=dict(title="AI Attention", x=1.02, len=0.8)
        ),
        row=1, col=1
    )
    fig.add_trace(
        go.Heatmap(z=heatmap_data[:, mid_y, :], colorscale=colorscale, showscale=False),
        row=1, col=2
    )
    fig.add_trace(
        go.Heatmap(z=heatmap_data[mid_x, :, :], colorscale=colorscale, showscale=False),
        row=1, col=3
    )

    fig.update_layout(
        height=500,
        showlegend=False,
        title_text="AI Analysis Focus Regions"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

def _create_clinical_overlay(mri_slice, heatmap_slice, alpha):
    """
    Create clinical-grade overlay of heatmap on MRI slice.

    Args:
        mri_slice: 2D MRI slice (normalized 0-1)
        heatmap_slice: 2D heatmap slice (normalized 0-1)
        alpha: Overlay transparency (0-1)

    Returns:
        numpy array: Blended overlay for visualization
    """
    # Create mask for significant heatmap regions
    significant_mask = heatmap_slice > 0.1

    # Start with MRI as base
    overlay = mri_slice.copy()

    # Apply heatmap overlay only where significant
    if np.any(significant_mask):
        # Blend MRI and heatmap with alpha transparency
        overlay = np.where(
            significant_mask,
            (1 - alpha) * mri_slice + alpha * heatmap_slice,
            mri_slice
        )

    return overlay

    # Full-screen heatmap viewing
    if st.button("🔍 View Heatmap in Full Screen", key="fullscreen_heatmap"):
        st.session_state.show_fullscreen_heatmap = True
        st.session_state.fullscreen_heatmap_data = heatmap_data
        st.session_state.fullscreen_original_data = original_data
        st.session_state.fullscreen_colorscale = heatmap_colorscale

    # Display fullscreen heatmap if requested
    if st.session_state.get('show_fullscreen_heatmap', False):
        display_fullscreen_heatmap(
            st.session_state.fullscreen_heatmap_data,
            st.session_state.fullscreen_original_data,
            st.session_state.fullscreen_colorscale
        )
        if st.button("❌ Close Full Screen", key="close_fullscreen"):
            st.session_state.show_fullscreen_heatmap = False

    # Add interpretation guide
    st.markdown("""
    **🔍 How to interpret this heatmap:**
    - **Bright/Hot colors**: Brain regions that strongly influenced the prediction
    - **Dark/Cool colors**: Brain regions with minimal influence
    - **Top row**: Original MRI slices for reference
    - **Bottom row**: Importance heatmap showing AI attention
    """)

def display_fullscreen_heatmap(heatmap_data, original_data, colorscale='hot'):
    """
    Display heatmap in full-screen mode with interactive controls.

    Args:
        heatmap_data: SHAP values or saliency map
        original_data: Original MRI data
        colorscale: Color scale for heatmap
    """
    st.markdown("## 🔥 Full-Screen Heatmap Viewer")

    # Normalize heatmap for visualization
    heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

    # Slice selection controls
    col1, col2, col3 = st.columns(3)

    with col1:
        axial_slice = st.slider(
            "Axial Slice",
            0, heatmap_data.shape[2]-1,
            heatmap_data.shape[2]//2,
            key="heatmap_axial_slider"
        )

    with col2:
        coronal_slice = st.slider(
            "Coronal Slice",
            0, heatmap_data.shape[1]-1,
            heatmap_data.shape[1]//2,
            key="heatmap_coronal_slider"
        )

    with col3:
        sagittal_slice = st.slider(
            "Sagittal Slice",
            0, heatmap_data.shape[0]-1,
            heatmap_data.shape[0]//2,
            key="heatmap_sagittal_slider"
        )

    # Display controls
    col1, col2, col3 = st.columns(3)
    with col1:
        heatmap_colorscale = st.selectbox(
            "Heatmap Color Scale",
            ["hot", "viridis", "plasma", "inferno", "magma", "turbo", "jet"],
            index=0 if colorscale == 'hot' else ["hot", "viridis", "plasma", "inferno", "magma", "turbo", "jet"].index(colorscale),
            key="fullscreen_heatmap_colorscale"
        )

    with col2:
        overlay_alpha = st.slider("Overlay Transparency", 0.0, 1.0, 0.7, key="overlay_alpha")

    with col3:
        view_mode = st.selectbox(
            "View Mode",
            ["Side by Side", "Overlay", "Heatmap Only"],
            key="view_mode"
        )

    if view_mode == "Side by Side":
        # Side by side view
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=[
                f"Axial Original (Z={axial_slice})",
                f"Coronal Original (Y={coronal_slice})",
                f"Sagittal Original (X={sagittal_slice})",
                f"Axial Heatmap (Z={axial_slice})",
                f"Coronal Heatmap (Y={coronal_slice})",
                f"Sagittal Heatmap (X={sagittal_slice})"
            ]
        )

        # Original images (top row)
        fig.add_trace(
            go.Heatmap(z=original_data[:, :, axial_slice], colorscale='gray', showscale=False),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=original_data[:, coronal_slice, :], colorscale='gray', showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=original_data[sagittal_slice, :, :], colorscale='gray', showscale=False),
            row=1, col=3
        )

        # Heatmaps (bottom row)
        fig.add_trace(
            go.Heatmap(
                z=heatmap_norm[:, :, axial_slice],
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Importance", x=1.02, len=0.4)
            ),
            row=2, col=1
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[:, coronal_slice, :], colorscale=heatmap_colorscale, showscale=False),
            row=2, col=2
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[sagittal_slice, :, :], colorscale=heatmap_colorscale, showscale=False),
            row=2, col=3
        )

    elif view_mode == "Heatmap Only":
        # Heatmap only view
        fig = make_subplots(
            rows=1, cols=3,
            subplot_titles=[
                f"Axial Heatmap (Z={axial_slice})",
                f"Coronal Heatmap (Y={coronal_slice})",
                f"Sagittal Heatmap (X={sagittal_slice})"
            ]
        )

        fig.add_trace(
            go.Heatmap(
                z=heatmap_norm[:, :, axial_slice],
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Importance", x=1.02, len=0.8)
            ),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[:, coronal_slice, :], colorscale=heatmap_colorscale, showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[sagittal_slice, :, :], colorscale=heatmap_colorscale, showscale=False),
            row=1, col=3
        )

    else:  # Overlay mode
        # Create overlay by blending original and heatmap
        fig = make_subplots(
            rows=1, cols=3,
            subplot_titles=[
                f"Axial Overlay (Z={axial_slice})",
                f"Coronal Overlay (Y={coronal_slice})",
                f"Sagittal Overlay (X={sagittal_slice})"
            ]
        )

        # Create overlays
        axial_overlay = original_data[:, :, axial_slice] * (1 - overlay_alpha) + heatmap_norm[:, :, axial_slice] * overlay_alpha
        coronal_overlay = original_data[:, coronal_slice, :] * (1 - overlay_alpha) + heatmap_norm[:, coronal_slice, :] * overlay_alpha
        sagittal_overlay = original_data[sagittal_slice, :, :] * (1 - overlay_alpha) + heatmap_norm[sagittal_slice, :, :] * overlay_alpha

        fig.add_trace(
            go.Heatmap(
                z=axial_overlay,
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Blended", x=1.02, len=0.8)
            ),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=coronal_overlay, colorscale=heatmap_colorscale, showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=sagittal_overlay, colorscale=heatmap_colorscale, showscale=False),
            row=1, col=3
        )

    fig.update_layout(
        height=800 if view_mode == "Side by Side" else 400,
        showlegend=False,
        title_text=f"Interactive Heatmap Viewer - {view_mode} Mode"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Heatmap statistics
    st.markdown("### 📊 Heatmap Statistics")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Min Importance", f"{heatmap_data.min():.6f}")
    with col2:
        st.metric("Max Importance", f"{heatmap_data.max():.6f}")
    with col3:
        st.metric("Mean Importance", f"{heatmap_data.mean():.6f}")
    with col4:
        st.metric("Std Importance", f"{heatmap_data.std():.6f}")

def display_pdf_status():
    """
    Display PDF generation status and download button at the top of results.
    """
    st.markdown("### 📄 Clinical PDF Report")

    # Debug information (remove in production)
    with st.expander("🔍 Debug Info", expanded=False):
        st.write(f"PDF Status: {st.session_state.get('pdf_status', 'not_set')}")
        st.write(f"Has heatmap: {'heatmap_data' in st.session_state}")
        st.write(f"Has PDF data: {st.session_state.get('pdf_data') is not None}")
        st.write(f"Has PDF filename: {st.session_state.get('pdf_filename') is not None}")
        if 'pdf_filename' in st.session_state:
            st.write(f"PDF filename: {st.session_state.pdf_filename}")
        if 'pdf_data' in st.session_state and st.session_state.pdf_data:
            st.write(f"PDF size: {len(st.session_state.pdf_data)} bytes")

    # Check if all required components are available
    has_heatmap = 'heatmap_data' in st.session_state

    if not has_heatmap:
        # Show waiting for heatmap status
        st.warning("⏳ **Status**: Waiting for brain region analysis to complete...")
        st.info("💡 **Note**: Complete clinical report requires brain region importance analysis. Please generate heatmap first.")

    elif st.session_state.get('pdf_status') == 'ready':
        # Show download button prominently when ready
        st.success("✅ **Status**: Complete clinical report ready for download!")

        # Check if we have the PDF data
        pdf_data = st.session_state.get('pdf_data')
        pdf_filename = st.session_state.get('pdf_filename')

        if pdf_data and pdf_filename:
            # Make download button prominent and always visible
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                st.download_button(
                    label="📄 Download Clinical Report",
                    data=pdf_data,
                    file_name=pdf_filename,
                    mime="application/pdf",
                    type="primary",
                    use_container_width=True,
                    key="top_download_clinical_pdf"
                )

            # Also show a direct download link as backup
            st.markdown("---")
            st.markdown("**Alternative Download Options:**")

            col1, col2 = st.columns(2)
            with col1:
                st.download_button(
                    label="💾 Download PDF (Alternative)",
                    data=pdf_data,
                    file_name=pdf_filename,
                    mime="application/pdf",
                    key="alt_download_pdf"
                )
            with col2:
                st.info(f"📊 Size: {len(pdf_data):,} bytes")

            st.code(f"Filename: {pdf_filename}", language=None)
        else:
            st.error("❌ PDF data not available. Please regenerate.")
            st.write(f"Debug: pdf_data exists: {pdf_data is not None}, pdf_filename exists: {pdf_filename is not None}")

    elif st.session_state.get('pdf_status') == 'generating':
        st.info("⏳ **Status**: Generating comprehensive clinical report...")

    elif st.session_state.get('pdf_status') == 'error':
        st.error("❌ **Status**: PDF generation failed")
        if st.button("🔄 Retry PDF Generation", key="pdf_retry"):
            st.session_state.pdf_status = 'not_started'
            st.rerun()

    else:
        st.info("🔄 **Status**: Ready to generate clinical report after brain analysis...")

    st.markdown("---")

def start_background_pdf_generation():
    """
    Start background PDF generation only after heatmap analysis is complete.
    """
    # Only generate PDF when we have predictions, preprocessed data, AND heatmap data
    if ('predictions' in st.session_state and
        'preprocessed_data' in st.session_state and
        'heatmap_data' in st.session_state and
        st.session_state.pdf_status == 'not_started'):

        st.session_state.pdf_status = 'generating'

        # Use a placeholder to show we're generating
        with st.spinner("Generating comprehensive PDF report with brain region analysis..."):
            # Generate PDF in background
            filename_base = st.session_state.get('uploaded_filename', 'scan')

            try:
                pdf_bytes = generate_pdf_report(
                    st.session_state.preprocessed_data,
                    st.session_state.heatmap_data,  # Now guaranteed to exist
                    st.session_state.predictions,
                    filename_base
                )

                if pdf_bytes:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"Demetify_Clinical_Report_{filename_base}_{timestamp}.pdf"

                    st.session_state.pdf_data = pdf_bytes
                    st.session_state.pdf_filename = filename
                    st.session_state.pdf_status = 'ready'
                else:
                    st.session_state.pdf_status = 'error'

            except Exception as e:
                st.session_state.pdf_status = 'error'
                st.error(f"Background PDF generation failed: {str(e)}")

def generate_clinical_recommendations(predictions, heatmap_data):
    """
    Generate clinical recommendations based on AI analysis results.

    Args:
        predictions: Model predictions dictionary
        heatmap_data: Brain region importance data

    Returns:
        dict: Clinical recommendations and observations
    """
    recommendations = {
        'primary_findings': [],
        'clinical_recommendations': [],
        'follow_up_suggestions': [],
        'technical_notes': []
    }

    # Analyze ADD prediction
    if 'ADD' in predictions:
        add_result = predictions['ADD']
        confidence = add_result['confidence']
        prediction = add_result['prediction']

        if prediction == 1:  # AD detected
            if confidence >= 0.9:
                recommendations['primary_findings'].append(
                    f"High confidence Alzheimer's Disease pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Consider comprehensive neuropsychological evaluation"
                )
                recommendations['clinical_recommendations'].append(
                    "Recommend discussion of findings with patient and family"
                )
                recommendations['follow_up_suggestions'].append(
                    "Consider referral to memory disorders clinic"
                )
            elif confidence >= 0.7:
                recommendations['primary_findings'].append(
                    f"Moderate confidence Alzheimer's Disease pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Correlate with clinical presentation and cognitive testing"
                )
                recommendations['follow_up_suggestions'].append(
                    "Consider follow-up imaging in 6-12 months"
                )
            else:
                recommendations['primary_findings'].append(
                    f"Possible Alzheimer's Disease pattern (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Clinical correlation strongly recommended"
                )
        else:  # Normal cognition
            if confidence >= 0.8:
                recommendations['primary_findings'].append(
                    f"Normal cognitive pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "No immediate cognitive concerns identified on imaging"
                )
            else:
                recommendations['primary_findings'].append(
                    f"Indeterminate pattern (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Clinical correlation and possible repeat imaging recommended"
                )

    # Analyze COG score - just show the number
    if 'COG' in predictions:
        cog_result = predictions['COG']
        score = cog_result['score']

        recommendations['primary_findings'].append(
            f"Cognitive score: {score:.3f}"
        )

    # Analyze heatmap patterns
    if heatmap_data is not None:
        # Calculate heatmap statistics
        max_importance = heatmap_data.max()
        mean_importance = heatmap_data.mean()

        recommendations['technical_notes'].append(
            f"Brain region analysis completed - max importance: {max_importance:.6f}"
        )

        # Identify high-importance regions (simplified analysis)
        high_importance_threshold = max_importance * 0.7
        high_importance_voxels = (heatmap_data > high_importance_threshold).sum()
        total_voxels = heatmap_data.size
        high_importance_percentage = (high_importance_voxels / total_voxels) * 100

        if high_importance_percentage > 5:
            recommendations['technical_notes'].append(
                f"Widespread pattern of importance detected ({high_importance_percentage:.1f}% of brain regions)"
            )
        elif high_importance_percentage > 1:
            recommendations['technical_notes'].append(
                f"Focal pattern of importance detected ({high_importance_percentage:.1f}% of brain regions)"
            )
        else:
            recommendations['technical_notes'].append(
                f"Minimal focal changes detected ({high_importance_percentage:.1f}% of brain regions)"
            )

    # Add standard clinical notes
    recommendations['clinical_recommendations'].append(
        "AI analysis should be interpreted in conjunction with clinical findings"
    )
    recommendations['follow_up_suggestions'].append(
        "Consider multidisciplinary team discussion for complex cases"
    )

    return recommendations

def generate_pdf_report(mri_data, heatmap_data, predictions, filename_base):
    """
    Generate a comprehensive PDF report with MRI analysis results.

    Args:
        mri_data: Preprocessed MRI data
        heatmap_data: SHAP/saliency heatmap data
        predictions: Model predictions
        filename_base: Base filename for the report

    Returns:
        bytes: PDF file as bytes for download
    """
    try:
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_pdf import PdfPages
        from datetime import datetime
        import io

        # Generate clinical recommendations
        recommendations = generate_clinical_recommendations(predictions, heatmap_data)

        # Create PDF in memory
        pdf_buffer = io.BytesIO()

        with PdfPages(pdf_buffer) as pdf:
            # Page 1: Cover page and summary
            fig, ax = plt.subplots(figsize=(8.5, 11))
            ax.axis('off')

            # Header
            ax.text(0.5, 0.95, 'Demetify', fontsize=32, fontweight='bold',
                   ha='center', va='top', color='#2E86AB')
            ax.text(0.5, 0.90, 'AI-Powered Radiologist Assistant', fontsize=16,
                   ha='center', va='top', color='#A23B72')
            ax.text(0.5, 0.85, 'MRI-Based Dementia Assessment Report', fontsize=14,
                   ha='center', va='top')

            # UIUC branding
            ax.text(0.5, 0.80, 'University of Illinois Urbana-Champaign', fontsize=12,
                   ha='center', va='top', style='italic')
            ax.text(0.5, 0.77, 'Project Lead: S. Seshadri', fontsize=10,
                   ha='center', va='top')

            # Report details
            ax.text(0.1, 0.70, f'Report Generated: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}',
                   fontsize=12, fontweight='bold')
            ax.text(0.1, 0.67, f'Scan ID: {filename_base}', fontsize=12)
            ax.text(0.1, 0.64, f'MRI Shape: {mri_data.shape}', fontsize=12)

            # Results summary
            ax.text(0.1, 0.55, 'ASSESSMENT RESULTS', fontsize=16, fontweight='bold', color='#2E86AB')

            if 'ADD' in predictions:
                add_result = predictions['ADD']
                diagnosis = "Alzheimer's Disease Detected" if add_result['prediction'] == 1 else "Normal Cognition"
                confidence = add_result['confidence']

                ax.text(0.1, 0.50, f'Alzheimer\'s Disease Classification:', fontsize=12, fontweight='bold')
                ax.text(0.15, 0.47, f'• Diagnosis: {diagnosis}', fontsize=11)
                ax.text(0.15, 0.44, f'• Confidence: {confidence:.1%}', fontsize=11)

            if 'COG' in predictions:
                cog_result = predictions['COG']
                score = cog_result['score']

                ax.text(0.1, 0.38, f'Cognitive Assessment:', fontsize=12, fontweight='bold')
                ax.text(0.15, 0.35, f'• COG Score: {score:.3f}', fontsize=11)

            # Clinical Summary (concise, no inference text)
            ax.text(0.1, 0.25, 'CLINICAL SUMMARY', fontsize=16, fontweight='bold', color='#2E86AB')

            y_pos = 0.22
            # Only show key findings, no inference text
            key_findings = []

            if 'ADD' in predictions:
                add_result = predictions['ADD']
                if add_result['prediction'] == 1:
                    key_findings.append(f"Alzheimer's Disease pattern detected (confidence: {add_result['confidence']:.1%})")
                else:
                    key_findings.append(f"Normal cognitive pattern (confidence: {add_result['confidence']:.1%})")

            if 'COG' in predictions:
                cog_result = predictions['COG']
                key_findings.append(f"COG Score: {cog_result['score']:.3f}")

            if heatmap_data is not None:
                significant_regions = np.count_nonzero(heatmap_data > heatmap_data.max() * 0.5)
                total_regions = heatmap_data.size
                percentage = (significant_regions / total_regions) * 100
                key_findings.append(f"Brain analysis: {significant_regions:,} significant regions ({percentage:.1f}% of brain)")

            for finding in key_findings:
                ax.text(0.1, y_pos, f'• {finding}', fontsize=11, fontweight='bold')
                y_pos -= 0.03

            # Footer
            ax.text(0.5, 0.05, 'Demetify - Accelerating Radiological Diagnosis',
                   fontsize=10, ha='center', style='italic', color='#666')

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

            # Page 2: MRI visualizations with brain region analysis
            # Note: heatmap_data is guaranteed to exist since we only generate PDF after heatmap completion
            fig, axes = plt.subplots(2, 3, figsize=(11, 8.5))
            fig.suptitle('MRI Analysis with Brain Region Importance', fontsize=16, fontweight='bold')

            mid_x, mid_y, mid_z = mri_data.shape[0]//2, mri_data.shape[1]//2, mri_data.shape[2]//2

            # Original MRI slices (top row)
            axes[0, 0].imshow(mri_data[:, :, mid_z], cmap='gray')
            axes[0, 0].set_title(f'Original MRI - Axial (Z={mid_z})', fontsize=12)
            axes[0, 0].axis('off')

            axes[0, 1].imshow(mri_data[:, mid_y, :], cmap='gray')
            axes[0, 1].set_title(f'Original MRI - Coronal (Y={mid_y})', fontsize=12)
            axes[0, 1].axis('off')

            axes[0, 2].imshow(mri_data[mid_x, :, :], cmap='gray')
            axes[0, 2].set_title(f'Original MRI - Sagittal (X={mid_x})', fontsize=12)
            axes[0, 2].axis('off')

            # Brain region importance heatmaps (bottom row)
            heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

            im1 = axes[1, 0].imshow(heatmap_norm[:, :, mid_z], cmap='hot', alpha=0.8)
            axes[1, 0].set_title('AI Focus Regions - Axial', fontsize=12, color='#2E86AB')
            axes[1, 0].axis('off')

            axes[1, 1].imshow(heatmap_norm[:, mid_y, :], cmap='hot', alpha=0.8)
            axes[1, 1].set_title('AI Focus Regions - Coronal', fontsize=12, color='#2E86AB')
            axes[1, 1].axis('off')

            axes[1, 2].imshow(heatmap_norm[mid_x, :, :], cmap='hot', alpha=0.8)
            axes[1, 2].set_title('AI Focus Regions - Sagittal', fontsize=12, color='#2E86AB')
            axes[1, 2].axis('off')

            # Add colorbar for heatmaps
            cbar = plt.colorbar(im1, ax=axes[1, :], orientation='horizontal',
                        fraction=0.05, pad=0.1, label='AI Attention Score')
            cbar.ax.tick_params(labelsize=10)

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

            # Page 3: Prediction Confidence and Analysis Charts
            fig = plt.figure(figsize=(8.5, 11))

            # Create a grid layout similar to website
            gs = fig.add_gridspec(4, 2, height_ratios=[0.8, 1.5, 1.5, 0.8], hspace=0.3, wspace=0.3)

            # Header
            header_ax = fig.add_subplot(gs[0, :])
            header_ax.axis('off')
            header_ax.text(0.5, 0.5, 'AI Analysis Results & Confidence Metrics',
                          fontsize=18, fontweight='bold', ha='center', va='center', color='#2E86AB')

            # ADD Prediction Chart (like website)
            add_ax = fig.add_subplot(gs[1, 0])
            if 'ADD' in predictions:
                add_result = predictions['ADD']
                confidence = add_result['confidence']
                prediction = add_result['prediction']

                # Create confidence bar chart
                categories = ['Normal', 'Alzheimer\'s Disease']
                confidences = [1-confidence if prediction == 1 else confidence,
                              confidence if prediction == 1 else 1-confidence]
                colors = ['#90EE90' if prediction == 0 else '#FFB6C1',
                         '#FF6B6B' if prediction == 1 else '#D3D3D3']

                bars = add_ax.bar(categories, confidences, color=colors, alpha=0.8)
                add_ax.set_ylim(0, 1)
                add_ax.set_ylabel('Confidence Score', fontsize=10)
                add_ax.set_title('Alzheimer\'s Disease Classification', fontsize=12, fontweight='bold', color='#2E86AB')

                # Add confidence text on bars
                for bar, conf in zip(bars, confidences):
                    height = bar.get_height()
                    add_ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                               f'{conf:.1%}', ha='center', va='bottom', fontweight='bold')

                # Highlight predicted class
                predicted_idx = prediction
                bars[predicted_idx].set_edgecolor('black')
                bars[predicted_idx].set_linewidth(3)

            # COG Score Chart - simple gauge for 0-1 normalized score
            cog_ax = fig.add_subplot(gs[1, 1])
            if 'COG' in predictions:
                cog_result = predictions['COG']
                score = cog_result['score']  # Already normalized 0-1

                # Create gauge-like visualization
                theta = np.linspace(0, np.pi, 100)
                r = 1

                # Background semicircle
                cog_ax.plot(r * np.cos(theta), r * np.sin(theta), 'lightgray', linewidth=8)

                # Score indicator - score is already 0-1 normalized
                score_angle = np.pi * (1 - score)

                # Simple color gradient based on score level
                if score > 0.6:
                    color = '#FF6B6B'  # Red for high scores
                elif score > 0.3:
                    color = '#FFA500'  # Orange for medium scores
                else:
                    color = '#90EE90'  # Green for low scores

                cog_ax.plot([0, r * np.cos(score_angle)], [0, r * np.sin(score_angle)],
                           color=color, linewidth=6, marker='o', markersize=8)

                cog_ax.set_xlim(-1.2, 1.2)
                cog_ax.set_ylim(-0.2, 1.2)
                cog_ax.set_aspect('equal')
                cog_ax.axis('off')
                cog_ax.set_title('COG Score', fontsize=12, fontweight='bold', color='#2E86AB')
                cog_ax.text(0, -0.1, f'{score:.3f}', ha='center', fontsize=14, fontweight='bold')

            # Brain Region Importance Statistics
            stats_ax = fig.add_subplot(gs[2, :])
            if heatmap_data is not None:
                # Calculate statistics for visualization
                flat_heatmap = heatmap_data.flatten()

                # Create histogram of importance values
                n_bins = 50
                counts, bins, patches = stats_ax.hist(flat_heatmap, bins=n_bins, alpha=0.7, color='#FF6B6B', edgecolor='black')

                # Color gradient for histogram
                for i, (patch, bin_val) in enumerate(zip(patches, bins[:-1])):
                    normalized_val = (bin_val - flat_heatmap.min()) / (flat_heatmap.max() - flat_heatmap.min())
                    patch.set_facecolor(plt.cm.hot(normalized_val))

                stats_ax.set_xlabel('Brain Region Importance Score', fontsize=10)
                stats_ax.set_ylabel('Number of Voxels', fontsize=10)
                stats_ax.set_title('Distribution of Brain Region Importance', fontsize=12, fontweight='bold', color='#2E86AB')

                # Add statistics text
                stats_text = f'Max: {flat_heatmap.max():.6f} | Mean: {flat_heatmap.mean():.6f} | Std: {flat_heatmap.std():.6f}'
                stats_ax.text(0.5, 0.95, stats_text, transform=stats_ax.transAxes,
                             ha='center', va='top', fontsize=9, bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            # Clinical Summary
            summary_ax = fig.add_subplot(gs[3, :])
            summary_ax.axis('off')

            # Create summary text like website
            summary_text = "CLINICAL SUMMARY:\n"
            if 'ADD' in predictions:
                add_result = predictions['ADD']
                diagnosis = "Alzheimer's Disease Pattern Detected" if add_result['prediction'] == 1 else "Normal Cognitive Pattern"
                summary_text += f"• {diagnosis} (Confidence: {add_result['confidence']:.1%})\n"

            if 'COG' in predictions:
                cog_result = predictions['COG']
                summary_text += f"• COG Score: {cog_result['score']:.3f}\n"

            summary_text += f"• Brain region analysis completed with {np.count_nonzero(heatmap_data):,} significant regions identified"

            summary_ax.text(0.5, 0.5, summary_text, ha='center', va='center', fontsize=11,
                           bbox=dict(boxstyle='round,pad=0.5', facecolor='#E8F4FD', alpha=0.8),
                           transform=summary_ax.transAxes)

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

        # Return PDF bytes
        pdf_buffer.seek(0)
        pdf_bytes = pdf_buffer.read()
        pdf_buffer.close()

        return pdf_bytes

    except Exception as e:
        st.error(f"❌ Error generating PDF report: {str(e)}")
        import traceback
        st.error(f"Details: {traceback.format_exc()}")
        return None

def display_predictions(predictions):
    """Display prediction results in a medical-friendly format"""

    st.markdown('<h3 class="sub-header">📊 Assessment Results</h3>', unsafe_allow_html=True)
    
    # ADD (Alzheimer's Disease) Results
    if 'ADD' in predictions:
        add_result = predictions['ADD']
        
        st.markdown("### 🧠 Alzheimer's Disease Classification")
        
        # Main prediction
        confidence = add_result['confidence']
        
        # Color coding based on prediction
        if add_result['prediction'] == 1:
            st.markdown(f"""
            <div class="metric-card prediction-high">
                <h4>⚠️ Alzheimer's Disease Detected</h4>
                <p><strong>Confidence:</strong> {confidence:.1%}</p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div class="metric-card prediction-normal">
                <h4>✅ Normal Cognition</h4>
                <p><strong>Confidence:</strong> {confidence:.1%}</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Probability breakdown
        probs = add_result['probabilities']
        
        # Create probability chart
        fig = go.Figure(data=[
            go.Bar(
                x=['Normal', 'Alzheimer\'s Disease'],
                y=[probs['Normal'], probs['AD']],
                marker_color=['#28a745' if probs['Normal'] > probs['AD'] else '#6c757d',
                             '#dc3545' if probs['AD'] > probs['Normal'] else '#6c757d']
            )
        ])
        
        fig.update_layout(
            title="Classification Probabilities",
            yaxis_title="Probability",
            xaxis_title="Diagnosis",
            height=300,
            showlegend=False
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # COG (Cognitive) Results
    if 'COG' in predictions:
        cog_result = predictions['COG']
        
        st.markdown("### 🧮 Cognitive Assessment")
        
        score = cog_result['score']

        # Display cognitive score - clean number only
        st.markdown(f"""
        <div class="metric-card">
            <h4>COG Score: {score:.3f}</h4>
        </div>
        """, unsafe_allow_html=True)
        
        # Cognitive score visualization
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = score,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Cognitive Impairment Score"},
            delta = {'reference': 1.0},
            gauge = {
                'axis': {'range': [None, 3]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 0.5], 'color': "lightgreen"},
                    {'range': [0.5, 1.5], 'color': "yellow"},
                    {'range': [1.5, 3], 'color': "lightcoral"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 1.5
                }
            }
        ))
        
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    # Clinical interpretation
    st.markdown("### 📋 Clinical Summary")
    
    if 'ADD' in predictions and 'COG' in predictions:
        add_pred = predictions['ADD']['prediction']
        cog_score = predictions['COG']['score']
        
        if add_pred == 1 and cog_score > 1.5:
            summary = "⚠️ **High Risk**: Both AD classification and cognitive assessment indicate significant impairment."
        elif add_pred == 1 or cog_score > 1.5:
            summary = "⚠️ **Moderate Risk**: One assessment indicates potential impairment. Further evaluation recommended."
        elif cog_score > 0.5:
            summary = "⚡ **Mild Concern**: Mild cognitive changes detected. Monitoring recommended."
        else:
            summary = "✅ **Normal**: Both assessments indicate normal cognitive function."
        
        st.markdown(f"""
        <div class="metric-card">
            {summary}
        </div>
        """, unsafe_allow_html=True)
    
    # Additional analysis options
    st.markdown("---")

if __name__ == "__main__":
    main()
