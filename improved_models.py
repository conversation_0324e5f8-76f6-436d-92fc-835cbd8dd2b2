#!/usr/bin/env python3
"""
Improved MCI Models with Proper Regularization to Fix Overfitting
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ImprovedRealMCIModel(nn.Module):
    """Improved Real MCI model with proper regularization"""
    
    def __init__(self, num_classes=3, dropout_rate=0.5):
        super(ImprovedRealMCIModel, self).__init__()
        
        # More aggressive dropout and regularization
        self.features = nn.Sequential(
            # Block 1 - Reduced initial channels
            nn.Conv3d(1, 16, kernel_size=3, padding=1),
            nn.BatchNorm3d(16),
            nn.ReLU(inplace=True),
            nn.Dropout3d(0.2),
            nn.MaxPool3d(2),
            
            # Block 2
            nn.Conv3d(16, 32, kernel_size=3, padding=1),
            nn.BatchNorm3d(32),
            nn.ReLU(inplace=True),
            nn.Dropout3d(0.3),
            nn.MaxPool3d(2),
            
            # Block 3
            nn.Conv3d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm3d(64),
            nn.<PERSON>L<PERSON>(inplace=True),
            nn.Dropout3d(0.4),
            nn.MaxPool3d(2),
            
            # Block 4 - Additional layer for better feature extraction
            nn.Conv3d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.Dropout3d(0.5),
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )
        
        self.feature_size = 128 * 4 * 4 * 4
        
        # Smaller, more regularized classifier
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.8),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.6),
            nn.Linear(64, num_classes)
        )
        
        # Regularized auxiliary heads
        self.atrophy_head = nn.Sequential(
            nn.Linear(self.feature_size, 128),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.7),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        self.clinical_head = nn.Sequential(
            nn.Linear(self.feature_size, 128),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.7),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(64, 3)
        )
        
        # Initialize weights properly
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Proper weight initialization"""
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        features = self.features(x)
        features = features.view(features.size(0), -1)
        
        classification = self.classifier(features)
        atrophy = self.atrophy_head(features)
        clinical = self.clinical_head(features)
        
        return {
            'classification': classification,
            'atrophy': atrophy,
            'clinical': clinical,
            'features': features
        }

class ImprovedGatedConv3D(nn.Module):
    """Improved Gated 3D Convolution with regularization"""
    
    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1, dropout_rate=0.1):
        super(ImprovedGatedConv3D, self).__init__()
        
        # Main convolution
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        
        # Gate convolution
        self.gate_conv = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        
        # Batch normalization
        self.bn = nn.BatchNorm3d(out_channels)
        
        # Dropout for regularization
        self.dropout = nn.Dropout3d(dropout_rate)
        
        # Initialize weights
        nn.init.kaiming_normal_(self.conv.weight, mode='fan_out', nonlinearity='relu')
        nn.init.kaiming_normal_(self.gate_conv.weight, mode='fan_out', nonlinearity='sigmoid')
        
    def forward(self, x):
        # Main features
        main = self.conv(x)
        
        # Gate features
        gate = torch.sigmoid(self.gate_conv(x))
        
        # Gated output
        output = main * gate
        
        # Apply batch norm and dropout
        output = self.bn(output)
        output = self.dropout(output)
        
        return output

class ImprovedGatedCNNModel(nn.Module):
    """Improved Gated CNN model with proper regularization"""
    
    def __init__(self, num_classes=3, dropout_rate=0.5):
        super(ImprovedGatedCNNModel, self).__init__()
        
        # More conservative architecture with better regularization
        self.features = nn.Sequential(
            # Block 1
            ImprovedGatedConv3D(1, 16, dropout_rate=0.1),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            
            # Block 2
            ImprovedGatedConv3D(16, 32, dropout_rate=0.2),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            
            # Block 3
            ImprovedGatedConv3D(32, 64, dropout_rate=0.3),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            
            # Block 4
            ImprovedGatedConv3D(64, 128, dropout_rate=0.4),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )
        
        self.feature_size = 128 * 4 * 4 * 4
        
        # Heavily regularized classifier
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.8),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.6),
            nn.Linear(64, num_classes)
        )
        
        # Regularized auxiliary heads
        self.atrophy_head = nn.Sequential(
            nn.Linear(self.feature_size, 128),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.7),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        self.clinical_head = nn.Sequential(
            nn.Linear(self.feature_size, 128),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.7),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(64, 3)
        )
        
        # Initialize weights properly
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Proper weight initialization"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        features = self.features(x)
        features = features.view(features.size(0), -1)
        
        classification = self.classifier(features)
        atrophy = self.atrophy_head(features)
        clinical = self.clinical_head(features)
        
        return {
            'classification': classification,
            'atrophy': atrophy,
            'clinical': clinical,
            'features': features
        }

def create_balanced_predictions():
    """Create more balanced dummy predictions for testing"""
    
    # More realistic class distributions
    cn_prob = np.random.uniform(0.4, 0.8, 100)
    mci_prob = np.random.uniform(0.3, 0.7, 100) 
    ad_prob = np.random.uniform(0.2, 0.9, 100)
    
    # Normalize to sum to 1
    total = cn_prob + mci_prob + ad_prob
    cn_prob /= total
    mci_prob /= total
    ad_prob /= total
    
    return np.column_stack([cn_prob, mci_prob, ad_prob])

if __name__ == "__main__":
    # Test the improved models
    print("🧠 Testing Improved Models...")
    
    # Test improved original model
    improved_original = ImprovedRealMCIModel(dropout_rate=0.6)
    print(f"✅ Improved Original Model: {sum(p.numel() for p in improved_original.parameters()):,} parameters")
    
    # Test improved gated model
    improved_gated = ImprovedGatedCNNModel(dropout_rate=0.6)
    print(f"✅ Improved Gated Model: {sum(p.numel() for p in improved_gated.parameters()):,} parameters")
    
    # Test with dummy input
    dummy_input = torch.randn(1, 1, 91, 109, 91)
    
    with torch.no_grad():
        original_output = improved_original(dummy_input)
        gated_output = improved_gated(dummy_input)
        
        print(f"✅ Original output shape: {original_output['classification'].shape}")
        print(f"✅ Gated output shape: {gated_output['classification'].shape}")
        
        # Check for more balanced predictions
        original_probs = F.softmax(original_output['classification'], dim=1)
        gated_probs = F.softmax(gated_output['classification'], dim=1)
        
        print(f"📊 Original probs: {original_probs.numpy()[0]}")
        print(f"📊 Gated probs: {gated_probs.numpy()[0]}")
    
    print("🎉 Improved models ready for training!")
