#!/usr/bin/env python3
"""
Analyze and fix COG score calculation issues
"""

import sys
sys.path.append('.')
from ncomms2022_model import ModelManager
import numpy as np
import torch

def analyze_cog_score_calculation():
    """Analyze how COG scores are calculated and what the proper range should be."""
    
    print("🔍 Analyzing COG Score Calculation")
    print("=" * 60)
    
    # Load the model to understand the architecture
    try:
        manager = ModelManager()
        model = manager.load_model('CNN_baseline_new_cross0', device='cpu')
        
        print("✅ Model loaded successfully")
        
        # Check the model architecture for COG task
        print(f"\n📊 Model Architecture Analysis:")
        print(f"Tasks: {model.tasks}")
        
        # Check the COG MLP output size
        if hasattr(model, 'MLPs'):
            for i, task in enumerate(model.tasks):
                if task == 'COG':
                    mlp = model.MLPs[i]
                    print(f"COG MLP architecture: {mlp}")
                    
                    # Check the final layer
                    if hasattr(mlp, 'layers'):
                        final_layer = mlp.layers[-1]
                        print(f"COG final layer: {final_layer}")
                        print(f"COG output size: {final_layer.out_features}")
        
        # Test with demo data to see actual score ranges
        print(f"\n🧪 Testing COG Score Ranges with Demo Data:")
        
        demo_files = [
            "ncomms2022/demo/mri/demo1.npy",  # AD case
            "ncomms2022/demo/mri/demo2.npy",  # AD case  
            "ncomms2022/demo/mri/demo3.npy"   # Normal case
        ]
        
        for i, demo_file in enumerate(demo_files):
            try:
                # Load and preprocess
                from ncomms2022_preprocessing import NCOMMs2022Preprocessor
                preprocessor = NCOMMs2022Preprocessor()
                
                processed_data = preprocessor.preprocess_mri(
                    demo_file, 
                    file_type='npy',
                    apply_skull_stripping=False,
                    apply_normalization=False
                )
                
                if processed_data is not None:
                    # Get raw model output
                    with torch.no_grad():
                        input_tensor = torch.from_numpy(processed_data).unsqueeze(0).unsqueeze(0).float()
                        
                        # Get backbone features
                        backbone_output = model.backbone(input_tensor)
                        
                        # Get COG task output
                        cog_task_idx = model.tasks.index('COG')
                        raw_cog_output = model.MLPs[cog_task_idx](backbone_output)
                        raw_score = raw_cog_output.cpu().numpy()[0][0]
                        
                        print(f"Demo {i+1} ({demo_file.split('/')[-1]}):")
                        print(f"  Raw COG output: {raw_score:.6f}")
                        
                        # Test current interpretation
                        interpretation = model.interpret_cog_score(raw_score)
                        print(f"  Current interpretation: {interpretation}")
                        
            except Exception as e:
                print(f"Error processing {demo_file}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def understand_cog_scale():
    """Understand what the COG scale should represent."""
    
    print(f"\n📚 Understanding COG Scale from Research")
    print("=" * 60)
    
    print("""
🧠 **COG Score Interpretation (Based on Research):**

From the model_wrappers.py code, I can see the COG task uses:

1. **Model Type**: Regression ("type": "reg")
2. **Output Size**: 1 (single continuous value)
3. **Thresholding Logic**:
   - if output < thres['NC']: prediction = 0 (Normal Cognition)
   - elif thres['NC'] <= output <= thres['DE']: prediction = 1 (MCI)
   - else: prediction = 2 (Dementia)

4. **Default Thresholds** (from code):
   - NC threshold: 0.5
   - DE threshold: 1.5

🎯 **Proper COG Score Range:**
- **< 0.5**: Normal Cognition (0)
- **0.5 - 1.5**: Mild Cognitive Impairment (1) 
- **> 1.5**: Dementia/Cognitive Impairment (2)

❌ **Current Issues:**
1. No bounds checking - scores can go beyond expected range
2. Interpretation thresholds don't match training thresholds
3. Visualization normalization assumes wrong range (-2 to +2)

✅ **Fixes Needed:**
1. Update interpretation thresholds to match training (0.5, 1.5)
2. Add bounds checking for extreme values
3. Fix visualization normalization
4. Add proper scale documentation
""")

def create_fixed_cog_interpretation():
    """Create the corrected COG score interpretation function."""
    
    print(f"\n🔧 Creating Fixed COG Score Interpretation")
    print("=" * 60)
    
    fixed_code = '''
def interpret_cog_score(self, score):
    """
    Interpret COG score based on model training thresholds.
    
    The COG model was trained as a regression task with thresholds:
    - Normal Cognition (NC): score < 0.5
    - Mild Cognitive Impairment (MCI): 0.5 <= score <= 1.5  
    - Dementia/Cognitive Impairment (DE): score > 1.5
    
    Args:
        score: COG regression score (typically 0-3 range)
        
    Returns:
        str: Interpretation of the score
    """
    # Clamp extreme values to reasonable range
    score = max(-1.0, min(4.0, score))
    
    if score < 0.5:
        return "Normal Cognition"
    elif score <= 1.5:
        return "Mild Cognitive Impairment"
    else:
        return "Cognitive Impairment"

def get_cog_confidence_level(self, score):
    """Get confidence level for COG score interpretation."""
    # Distance from nearest threshold
    if score < 0.5:
        confidence = min(1.0, (0.5 - score) / 0.5)
    elif score <= 1.5:
        # In MCI range - confidence based on distance from boundaries
        dist_to_normal = abs(score - 0.5)
        dist_to_dementia = abs(score - 1.5)
        confidence = min(dist_to_normal, dist_to_dementia) / 0.5
    else:
        confidence = min(1.0, (score - 1.5) / 1.5)
    
    return max(0.1, confidence)  # Minimum 10% confidence

def normalize_cog_for_visualization(self, score):
    """Normalize COG score for gauge visualization (0-1 range)."""
    # Expected range is roughly 0-3, normalize to 0-1
    return max(0, min(1, score / 3.0))
'''
    
    print("Fixed COG interpretation functions:")
    print(fixed_code)
    
    return fixed_code

def main():
    """Main function to analyze and document COG score issues."""
    
    print("🎯 COG Score Analysis and Fix")
    print("=" * 70)
    
    # Analyze current calculation
    success = analyze_cog_score_calculation()
    
    # Explain the proper scale
    understand_cog_scale()
    
    # Create fixed interpretation
    create_fixed_cog_interpretation()
    
    print(f"\n🏆 **Summary of COG Score Issues and Fixes:**")
    print(f"")
    print(f"❌ **Current Problems:**")
    print(f"1. Interpretation thresholds (0.5, 1.5) don't match current code (0.5, 1.5)")
    print(f"2. No bounds checking - scores can be extreme values")
    print(f"3. Visualization assumes wrong range (-2 to +2)")
    print(f"4. No confidence indication for borderline cases")
    print(f"")
    print(f"✅ **Proposed Fixes:**")
    print(f"1. Use correct thresholds: < 0.5 (Normal), 0.5-1.5 (MCI), > 1.5 (Dementia)")
    print(f"2. Add bounds checking: clamp scores to reasonable range")
    print(f"3. Fix visualization: normalize 0-3 range to 0-1 for gauge")
    print(f"4. Add confidence levels based on distance from thresholds")
    print(f"")
    print(f"🎯 **Expected Behavior After Fix:**")
    print(f"- Normal cases: COG score 0.0-0.5 → 'Normal Cognition'")
    print(f"- MCI cases: COG score 0.5-1.5 → 'Mild Cognitive Impairment'") 
    print(f"- AD cases: COG score 1.5+ → 'Cognitive Impairment'")

if __name__ == "__main__":
    main()
