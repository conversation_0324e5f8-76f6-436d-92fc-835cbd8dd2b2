"""
Comprehensive Interpretability System for Dementia Classification
Unified heatmap generation showing all atrophy patterns with clinical region mapping
Based on radiological assessment standards and continuous severity scoring
"""

import torch
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import streamlit as st
from typing import Dict, List, Tuple, Optional
import logging
from scipy.ndimage import gaussian_filter, label
from scipy import ndimage
import matplotlib.pyplot as plt
import matplotlib.patches as patches

logger = logging.getLogger(__name__)

class ClinicalBrainAtlas:
    """
    Clinical brain atlas for radiological region mapping
    Based on standard radiological assessment regions
    """
    
    def __init__(self):
        # Define clinical regions based on radiological assessment
        self.clinical_regions = {
            'hippocampus': {
                'description': 'Medial temporal lobe - primary AD target',
                'coordinates': [(70, 90), (100, 140), (60, 120)],  # Approximate MNI coordinates
                'clinical_significance': 'high',
                'mta_relevance': True
            },
            'temporal_cortex': {
                'description': 'Temporal cortical regions',
                'coordinates': [(60, 120), (80, 160), (40, 140)],
                'clinical_significance': 'high',
                'mta_relevance': True
            },
            'parietal_cortex': {
                'description': 'Parietal regions - Koedam score',
                'coordinates': [(40, 80), (60, 120), (80, 140)],
                'clinical_significance': 'moderate',
                'koedam_relevance': True
            },
            'frontal_cortex': {
                'description': 'Frontal cortical regions',
                'coordinates': [(20, 60), (80, 160), (60, 140)],
                'clinical_significance': 'moderate',
                'gca_relevance': True
            },
            'occipital_cortex': {
                'description': 'Occipital regions',
                'coordinates': [(120, 160), (60, 120), (60, 140)],
                'clinical_significance': 'low',
                'gca_relevance': True
            },
            'subcortical': {
                'description': 'Basal ganglia and thalamus',
                'coordinates': [(80, 100), (100, 120), (80, 100)],
                'clinical_significance': 'moderate',
                'vascular_relevance': True
            }
        }
        
        # Clinical scoring thresholds
        self.clinical_thresholds = {
            'mta_normal': 0.2,
            'mta_mild': 0.4,
            'mta_moderate': 0.6,
            'mta_severe': 0.8,
            'gca_normal': 0.25,
            'gca_mild': 0.5,
            'gca_moderate': 0.75,
            'koedam_normal': 0.3,
            'koedam_mild': 0.6
        }
    
    def get_region_mask(self, region_name: str, shape: Tuple[int, int, int]) -> np.ndarray:
        """Generate approximate region mask for clinical region"""
        if region_name not in self.clinical_regions:
            return np.zeros(shape, dtype=bool)
        
        coords = self.clinical_regions[region_name]['coordinates']
        mask = np.zeros(shape, dtype=bool)
        
        # Create approximate region mask (simplified for demonstration)
        z_range, y_range, x_range = coords
        z_start, z_end = max(0, z_range[0]), min(shape[0], z_range[1])
        y_start, y_end = max(0, y_range[0]), min(shape[1], y_range[1])
        x_start, x_end = max(0, x_range[0]), min(shape[2], x_range[1])
        
        mask[z_start:z_end, y_start:y_end, x_start:x_end] = True
        
        # Apply Gaussian smoothing for more realistic region boundaries
        mask_smooth = gaussian_filter(mask.astype(float), sigma=3.0) > 0.1
        
        return mask_smooth

class ComprehensiveInterpretabilityEngine:
    """
    Comprehensive interpretability engine for unified atrophy detection
    Generates single heatmap showing all atrophy patterns with clinical correlation
    """
    
    def __init__(self, device: str = 'cuda'):
        self.device = device
        self.brain_atlas = ClinicalBrainAtlas()
        
        # Interpretability parameters
        self.smoothing_sigma = 1.5
        self.significance_threshold = 0.1
        self.clinical_correlation_weight = 0.3
    
    def generate_comprehensive_heatmap(self, model, mri_data: np.ndarray, 
                                     predictions: Dict) -> Dict:
        """
        Generate comprehensive heatmap showing all atrophy patterns
        
        Args:
            model: Trained model
            mri_data: Preprocessed MRI data
            predictions: Model predictions
            
        Returns:
            Dictionary containing heatmap data and clinical analysis
        """
        try:
            st.info("🧠 Generating comprehensive atrophy heatmap...")
            
            # Generate base saliency map
            base_saliency = self._generate_gradient_saliency(model, mri_data)
            
            if base_saliency is None:
                return None
            
            # Enhance saliency with clinical region weighting
            enhanced_saliency = self._apply_clinical_weighting(
                base_saliency, mri_data, predictions
            )
            
            # Generate regional analysis
            regional_analysis = self._analyze_regional_atrophy(
                enhanced_saliency, mri_data, predictions
            )
            
            # Create clinical correlation scores
            clinical_scores = self._calculate_clinical_scores(
                enhanced_saliency, regional_analysis
            )
            
            # Generate interpretability report
            interpretation_report = self._generate_interpretation_report(
                predictions, regional_analysis, clinical_scores
            )
            
            result = {
                'heatmap_data': enhanced_saliency,
                'regional_analysis': regional_analysis,
                'clinical_scores': clinical_scores,
                'interpretation_report': interpretation_report,
                'original_mri': mri_data
            }
            
            st.success("✅ Comprehensive heatmap generated successfully!")
            return result
            
        except Exception as e:
            st.error(f"❌ Error generating comprehensive heatmap: {str(e)}")
            logger.error(f"Heatmap generation failed: {e}")
            return None
    
    def _generate_gradient_saliency(self, model, mri_data: np.ndarray) -> Optional[np.ndarray]:
        """Generate gradient-based saliency map"""
        try:
            # Prepare input tensor
            input_tensor = torch.from_numpy(mri_data).float()
            input_tensor = input_tensor.unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
            input_tensor = input_tensor.to(self.device)
            input_tensor.requires_grad_(True)
            
            # Forward pass
            model.eval()
            outputs = model(input_tensor)
            
            # Use atrophy continuous score as primary target
            if 'ATROPHY_CONTINUOUS' in outputs:
                target_output = outputs['ATROPHY_CONTINUOUS']
            elif 'DEMENTIA_CLASSIFICATION' in outputs:
                # Use AD probability from classification
                class_probs = torch.softmax(outputs['DEMENTIA_CLASSIFICATION'], dim=1)
                target_output = class_probs[:, 2]  # AD class
            else:
                st.error("❌ No suitable output for gradient computation")
                return None
            
            # Backward pass
            target_output.backward()
            gradients = input_tensor.grad
            
            if gradients is None:
                st.error("❌ Failed to compute gradients")
                return None
            
            # Extract saliency map
            saliency_map = torch.abs(gradients[0, 0]).cpu().numpy()
            
            # Apply smoothing
            saliency_map = gaussian_filter(saliency_map, sigma=self.smoothing_sigma)
            
            # Normalize to [0, 1]
            saliency_map = (saliency_map - saliency_map.min()) / (saliency_map.max() - saliency_map.min())
            
            return saliency_map
            
        except Exception as e:
            logger.error(f"Gradient saliency generation failed: {e}")
            return None
    
    def _apply_clinical_weighting(self, saliency_map: np.ndarray, 
                                mri_data: np.ndarray, predictions: Dict) -> np.ndarray:
        """Apply clinical region weighting to enhance relevant areas"""
        try:
            enhanced_saliency = saliency_map.copy()
            
            # Get predicted severity for weighting
            atrophy_score = predictions.get('atrophy_score', 0.5)
            predicted_class = predictions.get('predicted_class', 'MCI')
            
            # Apply region-specific weighting based on prediction
            for region_name, region_info in self.brain_atlas.clinical_regions.items():
                region_mask = self.brain_atlas.get_region_mask(region_name, saliency_map.shape)
                
                # Calculate region-specific weight
                weight = self._calculate_region_weight(
                    region_name, predicted_class, atrophy_score, region_info
                )
                
                # Apply weighting
                enhanced_saliency[region_mask] *= weight
            
            # Normalize enhanced saliency
            enhanced_saliency = (enhanced_saliency - enhanced_saliency.min()) / \
                              (enhanced_saliency.max() - enhanced_saliency.min())
            
            return enhanced_saliency
            
        except Exception as e:
            logger.error(f"Clinical weighting failed: {e}")
            return saliency_map
    
    def _calculate_region_weight(self, region_name: str, predicted_class: str, 
                               atrophy_score: float, region_info: Dict) -> float:
        """Calculate region-specific weighting factor"""
        base_weight = 1.0
        
        # Increase weight for clinically significant regions
        if region_info['clinical_significance'] == 'high':
            base_weight *= 1.5
        elif region_info['clinical_significance'] == 'moderate':
            base_weight *= 1.2
        
        # Class-specific weighting
        if predicted_class == 'AD':
            if region_info.get('mta_relevance', False):
                base_weight *= 1.8  # Emphasize hippocampus/temporal for AD
        elif predicted_class == 'MCI':
            if region_info.get('mta_relevance', False):
                base_weight *= 1.4  # Moderate emphasis for MCI
            if region_info.get('koedam_relevance', False):
                base_weight *= 1.3  # Parietal regions important in MCI
        
        # Severity-based weighting
        severity_factor = 1.0 + (atrophy_score * 0.5)
        base_weight *= severity_factor
        
        return base_weight
    
    def _analyze_regional_atrophy(self, saliency_map: np.ndarray, 
                                mri_data: np.ndarray, predictions: Dict) -> Dict:
        """Analyze atrophy patterns in clinical regions"""
        regional_analysis = {}
        
        for region_name, region_info in self.brain_atlas.clinical_regions.items():
            region_mask = self.brain_atlas.get_region_mask(region_name, saliency_map.shape)
            
            if np.sum(region_mask) == 0:
                continue
            
            # Calculate regional statistics
            region_saliency = saliency_map[region_mask]
            region_mri = mri_data[region_mask]
            
            regional_analysis[region_name] = {
                'mean_saliency': float(np.mean(region_saliency)),
                'max_saliency': float(np.max(region_saliency)),
                'volume_affected': float(np.sum(region_saliency > self.significance_threshold) / np.sum(region_mask)),
                'mean_intensity': float(np.mean(region_mri)),
                'clinical_significance': region_info['clinical_significance'],
                'description': region_info['description']
            }
        
        return regional_analysis
    
    def _calculate_clinical_scores(self, saliency_map: np.ndarray, 
                                 regional_analysis: Dict) -> Dict:
        """Calculate automated clinical scores (MTA, GCA, Koedam)"""
        clinical_scores = {}
        
        # MTA Score (0-4) - based on hippocampus and temporal regions
        hippocampus_score = regional_analysis.get('hippocampus', {}).get('mean_saliency', 0)
        temporal_score = regional_analysis.get('temporal_cortex', {}).get('mean_saliency', 0)
        mta_raw = (hippocampus_score + temporal_score) / 2
        clinical_scores['automated_MTA'] = min(4.0, mta_raw * 4.0)
        
        # GCA Score (0-3) - based on overall cortical atrophy
        cortical_regions = ['frontal_cortex', 'parietal_cortex', 'temporal_cortex', 'occipital_cortex']
        cortical_scores = [regional_analysis.get(region, {}).get('mean_saliency', 0) 
                          for region in cortical_regions]
        gca_raw = np.mean(cortical_scores)
        clinical_scores['automated_GCA'] = min(3.0, gca_raw * 3.0)
        
        # Koedam Score (0-3) - based on parietal atrophy
        parietal_score = regional_analysis.get('parietal_cortex', {}).get('mean_saliency', 0)
        clinical_scores['automated_Koedam'] = min(3.0, parietal_score * 3.0)
        
        return clinical_scores

    def _generate_interpretation_report(self, predictions: Dict,
                                      regional_analysis: Dict,
                                      clinical_scores: Dict) -> Dict:
        """Generate comprehensive interpretation report"""
        report = {
            'summary': '',
            'key_findings': [],
            'clinical_correlation': {},
            'recommendations': []
        }

        # Generate summary based on predictions
        predicted_class = predictions.get('predicted_class', 'Unknown')
        atrophy_score = predictions.get('atrophy_score', 0.0)
        confidence = predictions.get('confidence', 0.0)

        report['summary'] = f"AI Analysis: {predicted_class} (Confidence: {confidence:.1%}, Atrophy Score: {atrophy_score:.2f})"

        # Key findings based on regional analysis
        for region, analysis in regional_analysis.items():
            if analysis['mean_saliency'] > 0.5:  # Significant atrophy
                severity = 'severe' if analysis['mean_saliency'] > 0.7 else 'moderate'
                report['key_findings'].append(
                    f"{severity.title()} atrophy detected in {region.replace('_', ' ')} "
                    f"(Saliency: {analysis['mean_saliency']:.2f})"
                )

        # Clinical correlation
        report['clinical_correlation'] = {
            'MTA_equivalent': f"{clinical_scores.get('automated_MTA', 0):.1f}/4.0",
            'GCA_equivalent': f"{clinical_scores.get('automated_GCA', 0):.1f}/3.0",
            'Koedam_equivalent': f"{clinical_scores.get('automated_Koedam', 0):.1f}/3.0"
        }

        # Generate recommendations
        if predicted_class == 'CN':
            report['recommendations'].append("No significant atrophy detected. Continue routine monitoring.")
        elif predicted_class == 'MCI':
            report['recommendations'].append("Mild cognitive impairment pattern detected. Consider follow-up in 6-12 months.")
            report['recommendations'].append("Monitor hippocampal and parietal regions for progression.")
        elif predicted_class == 'AD':
            report['recommendations'].append("Alzheimer's disease pattern detected. Clinical correlation recommended.")
            report['recommendations'].append("Significant medial temporal lobe atrophy present.")

        return report

def display_comprehensive_heatmap(interpretability_result: Dict, slice_indices: Dict = None):
    """
    Display comprehensive heatmap with clinical annotations

    Args:
        interpretability_result: Result from comprehensive interpretability engine
        slice_indices: Optional slice indices for display
    """
    if not interpretability_result:
        st.error("❌ No interpretability data available")
        return

    heatmap_data = interpretability_result['heatmap_data']
    original_mri = interpretability_result['original_mri']
    regional_analysis = interpretability_result['regional_analysis']
    clinical_scores = interpretability_result['clinical_scores']
    interpretation_report = interpretability_result['interpretation_report']

    # Display interpretation summary
    st.markdown("### 🧠 Comprehensive Atrophy Analysis")
    st.info(interpretation_report['summary'])

    # Display clinical scores
    col1, col2, col3 = st.columns(3)
    with col1:
        mta_score = clinical_scores.get('automated_MTA', 0)
        st.metric("Automated MTA Score", f"{mta_score:.1f}/4.0",
                 help="Medial Temporal Atrophy - Higher scores indicate more atrophy")
    with col2:
        gca_score = clinical_scores.get('automated_GCA', 0)
        st.metric("Automated GCA Score", f"{gca_score:.1f}/3.0",
                 help="Global Cortical Atrophy - Overall brain atrophy assessment")
    with col3:
        koedam_score = clinical_scores.get('automated_Koedam', 0)
        st.metric("Automated Koedam Score", f"{koedam_score:.1f}/3.0",
                 help="Parietal Atrophy - Important for early-onset AD")

    # Create comprehensive heatmap visualization
    if slice_indices is None:
        slice_indices = {
            'axial': heatmap_data.shape[2] // 2,
            'coronal': heatmap_data.shape[1] // 2,
            'sagittal': heatmap_data.shape[0] // 2
        }

    # Create subplot figure
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=[
            "Axial - Original MRI", "Coronal - Original MRI", "Sagittal - Original MRI",
            "Axial - Atrophy Heatmap", "Coronal - Atrophy Heatmap", "Sagittal - Atrophy Heatmap"
        ],
        vertical_spacing=0.1,
        horizontal_spacing=0.05
    )

    # Original MRI slices (top row)
    axial_orig = original_mri[:, :, slice_indices['axial']]
    coronal_orig = original_mri[:, slice_indices['coronal'], :]
    sagittal_orig = original_mri[slice_indices['sagittal'], :, :]

    fig.add_trace(go.Heatmap(z=axial_orig, colorscale='gray', showscale=False), row=1, col=1)
    fig.add_trace(go.Heatmap(z=coronal_orig, colorscale='gray', showscale=False), row=1, col=2)
    fig.add_trace(go.Heatmap(z=sagittal_orig, colorscale='gray', showscale=False), row=1, col=3)

    # Atrophy heatmaps (bottom row)
    axial_heatmap = heatmap_data[:, :, slice_indices['axial']]
    coronal_heatmap = heatmap_data[:, slice_indices['coronal'], :]
    sagittal_heatmap = heatmap_data[slice_indices['sagittal'], :, :]

    fig.add_trace(go.Heatmap(z=axial_heatmap, colorscale='hot', showscale=True,
                            colorbar=dict(title="Atrophy Severity", x=1.02, len=0.4)), row=2, col=1)
    fig.add_trace(go.Heatmap(z=coronal_heatmap, colorscale='hot', showscale=False), row=2, col=2)
    fig.add_trace(go.Heatmap(z=sagittal_heatmap, colorscale='hot', showscale=False), row=2, col=3)

    fig.update_layout(
        height=800,
        showlegend=False,
        title_text="Comprehensive Atrophy Heatmap - All Patterns Unified"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Display regional analysis
    st.markdown("### 📊 Regional Atrophy Analysis")

    # Create regional analysis table
    regional_data = []
    for region, analysis in regional_analysis.items():
        regional_data.append({
            'Region': region.replace('_', ' ').title(),
            'Mean Saliency': f"{analysis['mean_saliency']:.3f}",
            'Volume Affected': f"{analysis['volume_affected']:.1%}",
            'Clinical Significance': analysis['clinical_significance'].title(),
            'Description': analysis['description']
        })

    if regional_data:
        import pandas as pd
        df = pd.DataFrame(regional_data)
        st.dataframe(df, use_container_width=True)

    # Display key findings
    if interpretation_report['key_findings']:
        st.markdown("### 🔍 Key Findings")
        for finding in interpretation_report['key_findings']:
            st.write(f"• {finding}")

    # Display recommendations
    if interpretation_report['recommendations']:
        st.markdown("### 💡 Clinical Recommendations")
        for recommendation in interpretation_report['recommendations']:
            st.write(f"• {recommendation}")

    # Clinical interpretation note
    st.markdown("---")
    st.info("""
    **Clinical Note**: This unified heatmap shows all detected atrophy patterns in a single visualization.
    Bright/hot colors indicate regions where the AI detected significant atrophy. The automated clinical
    scores provide radiologist-equivalent assessments for MTA, GCA, and Koedam scales.
    """)

# Export functions for use in main application
__all__ = [
    'ComprehensiveInterpretabilityEngine',
    'ClinicalBrainAtlas',
    'display_comprehensive_heatmap'
]
