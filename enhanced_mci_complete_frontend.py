#!/usr/bin/env python3
"""
🧠 Enhanced MCI Complete Frontend - Final Application
Complete implementation with segmentation, clinical features, and fast preprocessing
"""

import streamlit as st
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import nibabel as nib
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.ndimage import zoom, gaussian_filter, label
from pathlib import Path
import time
import json
import logging
from datetime import datetime
import io
import base64
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="🧠 Enhanced MCI Classification System",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for medical interface
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        margin: 0.5rem 0;
    }
    .clinical-score {
        background: #e8f4fd;
        padding: 0.8rem;
        border-radius: 6px;
        margin: 0.3rem 0;
        border-left: 3px solid #0066cc;
    }
    .warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 1rem;
        border-radius: 6px;
        margin: 1rem 0;
    }
    .success-box {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        padding: 1rem;
        border-radius: 6px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Gated CNN Model Architecture
class GatedConv3D(nn.Module):
    """Gated 3D Convolution for enhanced feature learning"""

    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1):
        super(GatedConv3D, self).__init__()
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        self.gate_conv = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        self.bn = nn.BatchNorm3d(out_channels)

    def forward(self, x):
        main = self.conv(x)
        gate = torch.sigmoid(self.gate_conv(x))
        output = main * gate
        return self.bn(output)

class SegmentationEnhancedMCIModel(nn.Module):
    """Enhanced MCI model with segmentation and specific detection capabilities"""

    def __init__(self, num_classes=3, num_regions=15):
        super(SegmentationEnhancedMCIModel, self).__init__()

        # Gated CNN backbone
        self.features = nn.Sequential(
            GatedConv3D(1, 32),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),

            GatedConv3D(32, 64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),

            GatedConv3D(64, 128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.2),

            GatedConv3D(128, 256),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )

        self.feature_size = 256 * 4 * 4 * 4

        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_size, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )

        # Atrophy head
        self.atrophy_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )

        # Clinical scores head
        self.clinical_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 3)  # MTA, GCA, Koedam
        )

        self.region_names = [
            'background', 'hippocampus_L', 'hippocampus_R', 'temporal_L', 'temporal_R',
            'frontal_L', 'frontal_R', 'parietal_L', 'parietal_R', 'occipital_L', 'occipital_R',
            'thalamus_L', 'thalamus_R', 'basal_ganglia_L', 'basal_ganglia_R'
        ]

    def forward(self, x):
        features = []
        for i, layer in enumerate(self.features):
            x = layer(x)
            if i == 6:  # After block 3
                feature_maps = x

        global_features = x.view(x.size(0), -1)

        classification = self.classifier(global_features)
        atrophy = self.atrophy_head(global_features)
        clinical = self.clinical_head(global_features)

        return {
            'classification': classification,
            'atrophy': atrophy,
            'clinical': clinical,
            'features': global_features
        }

# Clinical Radiological Assessment
class ClinicalRadiologicalAssessment:
    """Comprehensive clinical radiological assessment"""

    def __init__(self):
        self.anatomical_regions = {
            'hippocampus': {'coords': [(35, 55), (45, 65), (35, 55)]},
            'choroid_fissure': {'coords': [(38, 52), (48, 62), (38, 52)]},
            'temporal_horn': {'coords': [(40, 50), (50, 70), (40, 50)]},
            'frontal_cortex': {'coords': [(10, 40), (40, 80), (30, 70)]},
            'parietal_cortex': {'coords': [(20, 50), (30, 60), (40, 70)]},
            'temporal_cortex': {'coords': [(30, 60), (40, 70), (30, 60)]},
            'occipital_cortex': {'coords': [(60, 80), (30, 70), (30, 70)]},
            'precuneus': {'coords': [(25, 45), (25, 45), (45, 65)]},
            'posterior_cingulate': {'coords': [(30, 50), (30, 50), (40, 60)]},
            'thalamus_medial': {'coords': [(42, 48), (52, 58), (42, 48)]},
            'angular_gyrus': {'coords': [(35, 55), (35, 55), (55, 75)]},
            'basal_ganglia': {'coords': [(38, 52), (48, 62), (38, 52)]}
        }

    def calculate_mta_score(self, mri_data, saliency_map, age=70):
        """Calculate MTA Score (0-4)"""

        # Extract regions
        hippo_coords = self.anatomical_regions['hippocampus']['coords']
        z_range, y_range, x_range = hippo_coords
        z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
        y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
        x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])

        hippocampus_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]

        choroid_coords = self.anatomical_regions['choroid_fissure']['coords']
        z_range, y_range, x_range = choroid_coords
        z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
        y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
        x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])

        choroid_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]

        temporal_coords = self.anatomical_regions['temporal_horn']['coords']
        z_range, y_range, x_range = temporal_coords
        z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
        y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
        x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])

        temporal_horn_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]

        # Calculate components
        choroid_widening = np.mean(choroid_atrophy)
        temporal_horn_widening = np.mean(temporal_horn_atrophy)
        hippocampal_volume_loss = np.mean(hippocampus_atrophy)

        # MTA scoring
        if choroid_widening < 0.2 and temporal_horn_widening < 0.2 and hippocampal_volume_loss < 0.2:
            mta_score = 0
        elif choroid_widening >= 0.2 and temporal_horn_widening < 0.3 and hippocampal_volume_loss < 0.3:
            mta_score = 1
        elif choroid_widening >= 0.3 and temporal_horn_widening >= 0.3 and hippocampal_volume_loss < 0.4:
            mta_score = 2
        elif hippocampal_volume_loss >= 0.4 and hippocampal_volume_loss < 0.7:
            mta_score = 3
        else:
            mta_score = 4

        # Age-adjusted interpretation
        abnormal_threshold = 3 if age >= 75 else 2
        interpretation = "Normal" if mta_score < abnormal_threshold else "Abnormal"

        return {
            'mta_score': mta_score,
            'interpretation': interpretation,
            'age_threshold': abnormal_threshold,
            'components': {
                'choroid_widening': choroid_widening,
                'temporal_horn_widening': temporal_horn_widening,
                'hippocampal_volume_loss': hippocampal_volume_loss
            }
        }

    def calculate_gca_score(self, mri_data, saliency_map):
        """Calculate GCA Score (0-3)"""

        cortical_regions = ['frontal_cortex', 'parietal_cortex', 'temporal_cortex', 'occipital_cortex']
        regional_atrophy_scores = []

        for region_name in cortical_regions:
            coords = self.anatomical_regions[region_name]['coords']
            z_range, y_range, x_range = coords
            z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
            y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
            x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])

            region_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]
            regional_atrophy_scores.append(np.mean(region_atrophy))

        mean_cortical_atrophy = np.mean(regional_atrophy_scores)

        if mean_cortical_atrophy < 0.2:
            gca_score = 0
        elif mean_cortical_atrophy < 0.4:
            gca_score = 1
        elif mean_cortical_atrophy < 0.7:
            gca_score = 2
        else:
            gca_score = 3

        interpretation_map = {
            0: "No cortical atrophy",
            1: "Mild atrophy: opening of sulci",
            2: "Moderate atrophy: volume loss of gyri",
            3: "Severe end-stage atrophy: 'knife blade'"
        }

        return {
            'gca_score': gca_score,
            'interpretation': interpretation_map[gca_score],
            'mean_cortical_atrophy': mean_cortical_atrophy,
            'regional_scores': {
                region: score for region, score in zip(cortical_regions, regional_atrophy_scores)
            }
        }

    def calculate_koedam_score(self, mri_data, saliency_map):
        """Calculate Koedam Score (0-3) for Posterior Atrophy"""

        posterior_regions = ['precuneus', 'posterior_cingulate']
        posterior_atrophy_scores = []

        for region_name in posterior_regions:
            coords = self.anatomical_regions[region_name]['coords']
            z_range, y_range, x_range = coords
            z_start, z_end = max(0, z_range[0]), min(mri_data.shape[0], z_range[1])
            y_start, y_end = max(0, y_range[0]), min(mri_data.shape[1], y_range[1])
            x_start, x_end = max(0, x_range[0]), min(mri_data.shape[2], x_range[1])

            region_atrophy = saliency_map[z_start:z_end, y_start:y_end, x_start:x_end]
            posterior_atrophy_scores.append(np.mean(region_atrophy))

        max_posterior_atrophy = np.max(posterior_atrophy_scores)

        if max_posterior_atrophy < 0.2:
            koedam_score = 0
        elif max_posterior_atrophy < 0.4:
            koedam_score = 1
        elif max_posterior_atrophy < 0.7:
            koedam_score = 2
        else:
            koedam_score = 3

        interpretation_map = {
            0: "No posterior atrophy",
            1: "Mild posterior atrophy",
            2: "Moderate posterior atrophy",
            3: "Severe posterior atrophy"
        }

        return {
            'koedam_score': koedam_score,
            'interpretation': interpretation_map[koedam_score],
            'max_posterior_atrophy': max_posterior_atrophy,
            'clinical_significance': "High predictive value for AD, especially presenile AD"
        }

# Enhanced MCI Inference Engine
class EnhancedMCIInferenceEngine:
    """Complete inference engine with fast preprocessing and clinical assessment"""

    def __init__(self, model_path=None, device='cpu'):
        self.device = torch.device(device)
        self.class_names = ['CN (Normal)', 'MCI (Mild Cognitive Impairment)', 'AD (Alzheimer\'s Disease)']
        self.clinical_assessment = ClinicalRadiologicalAssessment()

        # Initialize model (use dummy if no model path provided)
        if model_path and Path(model_path).exists():
            self.load_model(model_path)
        else:
            self.model = SegmentationEnhancedMCIModel().to(self.device)
            self.model.eval()
            logger.warning("Using dummy model - no trained weights loaded")

    def load_model(self, model_path):
        """Load trained model"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model = SegmentationEnhancedMCIModel().to(self.device)

            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)

            self.model.eval()
            logger.info(f"Model loaded successfully from {model_path}")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            self.model = SegmentationEnhancedMCIModel().to(self.device)
            self.model.eval()

    def fast_preprocess_mri(self, mri_path):
        """Fast MRI preprocessing pipeline"""

        start_time = time.time()

        # Load MRI data
        if isinstance(mri_path, str):
            mri_path = Path(mri_path)

        if mri_path.suffix == '.npy':
            mri_data = np.load(mri_path)
        elif mri_path.suffix in ['.nii', '.nii.gz']:
            img = nib.load(mri_path)
            mri_data = img.get_fdata()
        else:
            raise ValueError(f"Unsupported file format: {mri_path.suffix}")

        # Fast resizing to target shape
        target_shape = (91, 109, 91)
        if mri_data.shape != target_shape:
            zoom_factors = [t/s for t, s in zip(target_shape, mri_data.shape)]
            mri_data = zoom(mri_data, zoom_factors, order=1)

        # Fast normalization
        if mri_data.max() > mri_data.min():
            mri_data = (mri_data - mri_data.min()) / (mri_data.max() - mri_data.min())

        # Convert to tensor
        mri_tensor = torch.FloatTensor(mri_data).unsqueeze(0).unsqueeze(0)

        preprocessing_time = time.time() - start_time

        return mri_tensor, mri_data, preprocessing_time

    def generate_enhanced_heatmap(self, mri_tensor, target_class=None):
        """Generate enhanced gradient-based heatmap"""

        input_tensor = mri_tensor.to(self.device)
        input_tensor.requires_grad_(True)

        # Forward pass
        outputs = self.model(input_tensor)

        # Use specified class or highest probability class
        if target_class is None:
            target_class = torch.argmax(outputs['classification'], dim=1)

        # Get target score
        target_score = outputs['classification'][0, target_class]

        # Backward pass
        target_score.backward()

        # Get gradients
        gradients = input_tensor.grad.data
        saliency_map = torch.abs(gradients[0, 0]).cpu().numpy()

        # Enhanced smoothing
        saliency_map = gaussian_filter(saliency_map, sigma=1.5)

        # Normalize
        if saliency_map.max() > saliency_map.min():
            saliency_map = (saliency_map - saliency_map.min()) / (saliency_map.max() - saliency_map.min())

        return saliency_map

    def detect_specific_atrophy(self, mri_data, saliency_map):
        """Detect specific types of atrophy"""

        atrophy_types = {
            'hippocampal_atrophy': self._detect_hippocampal_atrophy(mri_data, saliency_map),
            'cortical_atrophy': self._detect_cortical_atrophy(mri_data, saliency_map),
            'subcortical_atrophy': self._detect_subcortical_atrophy(mri_data, saliency_map),
            'white_matter_changes': self._detect_white_matter_changes(mri_data, saliency_map)
        }

        return atrophy_types

    def _detect_hippocampal_atrophy(self, mri_data, saliency_map):
        """Detect hippocampal atrophy"""
        hippo_region = saliency_map[35:55, 45:65, 35:55]
        severity = np.mean(hippo_region)

        if severity < 0.3:
            return {'severity': 'Normal', 'score': severity, 'description': 'No significant hippocampal atrophy'}
        elif severity < 0.5:
            return {'severity': 'Mild', 'score': severity, 'description': 'Mild hippocampal volume loss'}
        elif severity < 0.7:
            return {'severity': 'Moderate', 'score': severity, 'description': 'Moderate hippocampal atrophy'}
        else:
            return {'severity': 'Severe', 'score': severity, 'description': 'Severe hippocampal volume loss'}

    def _detect_cortical_atrophy(self, mri_data, saliency_map):
        """Detect cortical atrophy"""
        cortical_regions = [
            saliency_map[10:40, 40:80, 30:70],  # Frontal
            saliency_map[20:50, 30:60, 40:70],  # Parietal
            saliency_map[30:60, 40:70, 30:60],  # Temporal
        ]

        severity = np.mean([np.mean(region) for region in cortical_regions])

        if severity < 0.3:
            return {'severity': 'Normal', 'score': severity, 'description': 'No significant cortical atrophy'}
        elif severity < 0.5:
            return {'severity': 'Mild', 'score': severity, 'description': 'Mild cortical volume loss'}
        elif severity < 0.7:
            return {'severity': 'Moderate', 'score': severity, 'description': 'Moderate cortical atrophy'}
        else:
            return {'severity': 'Severe', 'score': severity, 'description': 'Severe cortical atrophy'}

    def _detect_subcortical_atrophy(self, mri_data, saliency_map):
        """Detect subcortical atrophy"""
        subcortical_region = saliency_map[38:52, 48:62, 38:52]
        severity = np.mean(subcortical_region)

        if severity < 0.4:
            return {'severity': 'Normal', 'score': severity, 'description': 'No significant subcortical changes'}
        elif severity < 0.6:
            return {'severity': 'Mild', 'score': severity, 'description': 'Mild subcortical changes'}
        else:
            return {'severity': 'Moderate', 'score': severity, 'description': 'Moderate subcortical atrophy'}

    def _detect_white_matter_changes(self, mri_data, saliency_map):
        """Detect white matter changes"""
        wm_regions = [
            saliency_map[25:65, 25:85, 25:65],  # Central white matter
        ]

        severity = np.mean([np.mean(region) for region in wm_regions])

        if severity < 0.3:
            return {'severity': 'Normal', 'score': severity, 'description': 'No significant white matter changes'}
        elif severity < 0.5:
            return {'severity': 'Mild', 'score': severity, 'description': 'Mild white matter hyperintensities'}
        else:
            return {'severity': 'Moderate', 'score': severity, 'description': 'Moderate white matter changes'}

    def detect_lesions(self, mri_data, saliency_map):
        """Detect strategic infarcts and lesions"""

        lesion_locations = {
            'thalamic_lesions': self._check_thalamic_lesions(mri_data, saliency_map),
            'basal_ganglia_lesions': self._check_basal_ganglia_lesions(mri_data, saliency_map),
            'white_matter_lesions': self._check_white_matter_lesions(mri_data, saliency_map),
            'cortical_infarcts': self._check_cortical_infarcts(mri_data, saliency_map)
        }

        return lesion_locations

    def _check_thalamic_lesions(self, mri_data, saliency_map):
        """Check for thalamic lesions"""
        thalamic_region = saliency_map[42:48, 52:58, 42:48]
        lesion_probability = np.mean(thalamic_region > 0.7)

        return {
            'probability': lesion_probability,
            'risk_level': 'High' if lesion_probability > 0.3 else 'Low',
            'clinical_significance': 'Memory and learning deficits'
        }

    def _check_basal_ganglia_lesions(self, mri_data, saliency_map):
        """Check for basal ganglia lesions"""
        bg_region = saliency_map[38:52, 48:62, 38:52]
        lesion_probability = np.mean(bg_region > 0.6)

        return {
            'probability': lesion_probability,
            'risk_level': 'High' if lesion_probability > 0.25 else 'Low',
            'clinical_significance': 'Movement and executive function'
        }

    def _check_white_matter_lesions(self, mri_data, saliency_map):
        """Check for white matter lesions"""
        wm_region = saliency_map[20:70, 20:90, 20:70]
        lesion_probability = np.mean(wm_region > 0.5)

        return {
            'probability': lesion_probability,
            'risk_level': 'High' if lesion_probability > 0.4 else 'Low',
            'clinical_significance': 'Processing speed and connectivity'
        }

    def _check_cortical_infarcts(self, mri_data, saliency_map):
        """Check for cortical infarcts"""
        cortical_region = saliency_map[15:75, 15:95, 15:75]
        lesion_probability = np.mean(cortical_region > 0.8)

        return {
            'probability': lesion_probability,
            'risk_level': 'High' if lesion_probability > 0.2 else 'Low',
            'clinical_significance': 'Cognitive domain-specific deficits'
        }