#!/usr/bin/env python3
"""
Model Inference Module for Local Frontend
Contains the trained model architecture and inference functions
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import nibabel as nib
from pathlib import Path
from scipy.ndimage import zoom, gaussian_filter
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealMCIModel(nn.Module):
    """Real MCI model architecture - MUST match training exactly"""
    
    def __init__(self, num_classes=3):
        super(RealMCIModel, self).__init__()
        
        # Efficient 3D CNN for real data
        self.features = nn.Sequential(
            # Block 1
            nn.Conv3d(1, 32, kernel_size=3, padding=1),
            nn.BatchNorm3d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),
            
            # Block 2
            nn.Conv3d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm3d(64),
            nn.ReL<PERSON>(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),
            
            # Block 3
            nn.Conv3d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.2),
            
            # Global pooling
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )
        
        # Feature size
        self.feature_size = 128 * 4 * 4 * 4
        
        # Multi-task heads
        # 1. Classification head
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_size, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
        # 2. Atrophy regression head
        self.atrophy_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
        
        # 3. Clinical scores head (MTA, GCA, Koedam)
        self.clinical_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 3)  # MTA, GCA, Koedam scores
        )
    
    def forward(self, x):
        features = self.features(x)
        features = features.view(features.size(0), -1)
        
        classification = self.classifier(features)
        atrophy = self.atrophy_head(features)
        clinical = self.clinical_head(features)
        
        return {
            'classification': classification,
            'atrophy': atrophy,
            'clinical': clinical,
            'features': features
        }

class MCIInferenceEngine:
    """Inference engine for local frontend"""
    
    def __init__(self, model_path, device='cpu'):
        self.device = torch.device(device)
        
        # Load model
        logger.info(f"Loading model from {model_path}")
        checkpoint = torch.load(model_path, map_location=self.device)
        
        self.model = RealMCIModel()
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.to(self.device)
        self.model.eval()
        
        # Class names
        self.class_names = ['CN (Normal)', 'MCI (Mild Cognitive Impairment)', 'AD (Alzheimer\'s Disease)']
        
        logger.info("Model loaded successfully")
    
    def preprocess_mri(self, mri_path):
        """Preprocess MRI for inference"""
        
        # Load MRI data
        if isinstance(mri_path, str):
            mri_path = Path(mri_path)
        
        if mri_path.suffix == '.npy':
            mri_data = np.load(mri_path)
        elif mri_path.suffix in ['.nii', '.nii.gz']:
            img = nib.load(mri_path)
            mri_data = img.get_fdata()
        else:
            raise ValueError(f"Unsupported file format: {mri_path.suffix}")
        
        # Ensure correct shape (91, 109, 91)
        target_shape = (91, 109, 91)
        if mri_data.shape != target_shape:
            zoom_factors = [t/s for t, s in zip(target_shape, mri_data.shape)]
            mri_data = zoom(mri_data, zoom_factors, order=1)
        
        # Normalize to [0, 1]
        if mri_data.max() > mri_data.min():
            mri_data = (mri_data - mri_data.min()) / (mri_data.max() - mri_data.min())
        
        # Convert to tensor
        mri_tensor = torch.FloatTensor(mri_data).unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
        
        return mri_tensor, mri_data
    
    def generate_heatmap(self, mri_tensor, target_class=None):
        """Generate gradient-based heatmap"""
        
        input_tensor = mri_tensor.to(self.device)
        input_tensor.requires_grad_(True)
        
        # Forward pass
        outputs = self.model(input_tensor)
        
        # Use specified class or highest probability class
        if target_class is None:
            target_class = torch.argmax(outputs['classification'], dim=1)
        
        # Get target score
        target_score = outputs['classification'][0, target_class]
        
        # Backward pass
        target_score.backward()
        
        # Get gradients
        gradients = input_tensor.grad.data
        saliency_map = torch.abs(gradients[0, 0]).cpu().numpy()
        
        # Apply smoothing
        saliency_map = gaussian_filter(saliency_map, sigma=1.5)
        
        # Normalize
        if saliency_map.max() > saliency_map.min():
            saliency_map = (saliency_map - saliency_map.min()) / (saliency_map.max() - saliency_map.min())
        
        return saliency_map
    
    def calculate_clinical_metrics(self, saliency_map):
        """Calculate clinical metrics from heatmap"""
        
        # Define clinical regions (simplified for demo)
        regions = {
            'hippocampus': saliency_map[35:55, 45:65, 35:55],
            'temporal': saliency_map[30:60, 40:70, 30:60],
            'parietal': saliency_map[20:50, 30:60, 40:70],
            'frontal': saliency_map[10:40, 40:80, 30:70]
        }
        
        # Calculate regional scores
        regional_scores = {}
        for region_name, region_data in regions.items():
            regional_scores[region_name] = {
                'mean_atrophy': float(np.mean(region_data)),
                'max_atrophy': float(np.max(region_data)),
                'volume_affected': float(np.sum(region_data > 0.5) / region_data.size)
            }
        
        # Calculate clinical scores
        hippocampus_score = regional_scores['hippocampus']['mean_atrophy']
        temporal_score = regional_scores['temporal']['mean_atrophy']
        mta_score = min(4.0, (hippocampus_score + temporal_score) / 2 * 4.5)
        
        cortical_scores = [regional_scores[r]['mean_atrophy'] for r in ['frontal', 'parietal', 'temporal']]
        gca_score = min(3.0, np.mean(cortical_scores) * 3.5)
        
        parietal_score = regional_scores['parietal']['mean_atrophy']
        koedam_score = min(3.0, parietal_score * 3.5)
        
        return {
            'MTA_score': round(mta_score, 1),
            'GCA_score': round(gca_score, 1),
            'Koedam_score': round(koedam_score, 1),
            'regional_analysis': regional_scores
        }
    
    def predict(self, mri_path):
        """Complete prediction pipeline"""
        
        logger.info(f"Processing MRI: {mri_path}")
        
        # Preprocess
        mri_tensor, mri_data = self.preprocess_mri(mri_path)
        
        # Get predictions
        with torch.no_grad():
            outputs = self.model(mri_tensor.to(self.device))
            
            # Classification
            class_probs = F.softmax(outputs['classification'], dim=1).cpu().numpy()[0]
            predicted_class = np.argmax(class_probs)
            confidence = np.max(class_probs)
            
            # Atrophy score
            atrophy_score = outputs['atrophy'].cpu().numpy()[0, 0]
            
            # Clinical scores
            clinical_scores = outputs['clinical'].cpu().numpy()[0]
        
        # Generate heatmap
        heatmap = self.generate_heatmap(mri_tensor, predicted_class)
        
        # Calculate clinical metrics
        clinical_metrics = self.calculate_clinical_metrics(heatmap)
        
        # Compile results
        results = {
            'prediction': {
                'class': self.class_names[predicted_class],
                'class_id': int(predicted_class),
                'confidence': float(confidence),
                'probabilities': {
                    'CN': float(class_probs[0]),
                    'MCI': float(class_probs[1]),
                    'AD': float(class_probs[2])
                }
            },
            'atrophy_score': float(atrophy_score),
            'clinical_scores': {
                'MTA': float(clinical_scores[0]),
                'GCA': float(clinical_scores[1]),
                'Koedam': float(clinical_scores[2])
            },
            'clinical_metrics': clinical_metrics,
            'heatmap': heatmap,
            'original_mri': mri_data
        }
        
        logger.info(f"Prediction: {results['prediction']['class']} (confidence: {confidence:.1%})")
        
        return results

# Example usage
if __name__ == "__main__":
    # Initialize inference engine
    engine = MCIInferenceEngine('best_real_mci_model.pth')
    
    # Example prediction (replace with actual MRI path)
    # results = engine.predict('path/to/mri.nii')
    # print(f"Prediction: {results['prediction']['class']}")
    # print(f"Confidence: {results['prediction']['confidence']:.1%}")
    # print(f"Clinical scores: MTA={results['clinical_metrics']['MTA_score']}")
