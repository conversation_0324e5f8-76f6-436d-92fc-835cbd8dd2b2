"""
Test script for the hierarchical dementia classification system
"""

import sys
import os
import numpy as np
from pathlib import Path

def test_model_loading():
    """Test if models can be loaded successfully"""
    print("🧪 Testing model loading...")
    
    try:
        import sys
        sys.path.append('src')
        from models.hierarchical_classifier import HierarchicalDementiaClassifier
        
        # Initialize classifier
        classifier = HierarchicalDementiaClassifier()
        
        # Check if models are loaded
        model_info = classifier.get_model_info()
        print(f"✅ Main model loaded: {model_info['main_model_loaded']}")
        print(f"✅ Specialized model loaded: {model_info['specialized_model_loaded']}")
        print(f"✅ Device: {model_info['device']}")
        print(f"✅ Main classes: {model_info['main_classes']}")
        print(f"✅ Specialized classes: {model_info['specialized_classes']}")
        
        return classifier
        
    except Exception as e:
        print(f"❌ Error loading models: {e}")
        return None

def test_sample_predictions(classifier):
    """Test predictions on sample data"""
    print("\n🧪 Testing sample predictions...")
    
    sample_dir = Path("src/data/sample_data")
    if not sample_dir.exists():
        print("❌ Sample data directory not found. Run create_sample_data.py first.")
        return False
    
    sample_files = list(sample_dir.glob("*.npy"))
    if not sample_files:
        print("❌ No sample .npy files found.")
        return False
    
    print(f"Found {len(sample_files)} sample files:")
    
    results = []
    for sample_file in sample_files:
        try:
            print(f"\n📁 Testing: {sample_file.name}")
            
            # Run prediction
            result = classifier.predict(str(sample_file))
            
            print(f"  ✅ Final diagnosis: {result['final_diagnosis']}")
            print(f"  ✅ Main prediction: {result['main_prediction']}")
            print(f"  ✅ Confidence: {result['main_confidence']:.3f}")
            print(f"  ✅ Model used: {result['model_used']}")
            
            if result['specialized_prediction']:
                print(f"  ✅ Specialized prediction: {result['specialized_prediction']}")
                print(f"  ✅ Specialized confidence: {result['specialized_confidence']:.3f}")
            
            results.append(result)
            
        except Exception as e:
            print(f"  ❌ Error processing {sample_file.name}: {e}")
    
    return results

def test_reporting_system(results):
    """Test the clinical reporting system"""
    print("\n🧪 Testing reporting system...")
    
    if not results:
        print("❌ No results to test reporting with.")
        return False
    
    try:
        from reports.clinical_reports import ClinicalReportGenerator
        
        report_gen = ClinicalReportGenerator()
        
        # Test PDF generation
        print("📄 Testing PDF report generation...")
        result = results[0]  # Use first result
        result['filename'] = 'test_sample.npy'
        
        try:
            pdf_path = report_gen.generate_pdf_report(result)
            print(f"  ✅ PDF report generated: {pdf_path}")
            
            # Check if file exists
            if os.path.exists(pdf_path):
                file_size = os.path.getsize(pdf_path)
                print(f"  ✅ PDF file size: {file_size:,} bytes")
            else:
                print(f"  ⚠️ PDF file not found at {pdf_path}")
                
        except Exception as e:
            print(f"  ❌ PDF generation error: {e}")
        
        # Test CSV export
        print("\n📊 Testing CSV export...")
        try:
            csv_path = report_gen.export_to_csv(results)
            print(f"  ✅ CSV exported: {csv_path}")
            
            # Check if file exists
            if os.path.exists(csv_path):
                file_size = os.path.getsize(csv_path)
                print(f"  ✅ CSV file size: {file_size:,} bytes")
            else:
                print(f"  ⚠️ CSV file not found at {csv_path}")
                
        except Exception as e:
            print(f"  ❌ CSV export error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing reporting system: {e}")
        return False

def test_streamlit_imports():
    """Test if Streamlit app imports work"""
    print("\n🧪 Testing Streamlit app imports...")
    
    try:
        # Test core imports
        import torch
        print("  ✅ PyTorch imported successfully")
        
        import numpy as np
        print("  ✅ NumPy imported successfully")
        
        import pandas as pd
        print("  ✅ Pandas imported successfully")
        
        try:
            import streamlit as st
            print("  ✅ Streamlit imported successfully")
        except ImportError:
            print("  ⚠️ Streamlit not available (install with: pip install streamlit)")
        
        try:
            import plotly.express as px
            import plotly.graph_objects as go
            print("  ✅ Plotly imported successfully")
        except ImportError:
            print("  ⚠️ Plotly not available (install with: pip install plotly)")
        
        try:
            import matplotlib.pyplot as plt
            print("  ✅ Matplotlib imported successfully")
        except ImportError:
            print("  ⚠️ Matplotlib not available (install with: pip install matplotlib)")
        
        # Test our custom modules
        from models.hierarchical_classifier import HierarchicalDementiaClassifier
        print("  ✅ HierarchicalDementiaClassifier imported successfully")

        from reports.clinical_reports import ClinicalReportGenerator
        print("  ✅ ClinicalReportGenerator imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting system tests for Professional Dementia Classifier")
    print("=" * 60)
    
    # Test 1: Model loading
    classifier = test_model_loading()
    if classifier is None:
        print("\n❌ Model loading failed. Cannot continue with other tests.")
        return False
    
    # Test 2: Sample predictions
    results = test_sample_predictions(classifier)
    if not results:
        print("\n❌ Sample predictions failed.")
        return False
    
    # Test 3: Reporting system
    reporting_success = test_reporting_system(results)
    
    # Test 4: Streamlit imports
    imports_success = test_streamlit_imports()
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Model Loading: {'PASS' if classifier else 'FAIL'}")
    print(f"✅ Sample Predictions: {'PASS' if results else 'FAIL'}")
    print(f"✅ Reporting System: {'PASS' if reporting_success else 'FAIL'}")
    print(f"✅ Streamlit Imports: {'PASS' if imports_success else 'FAIL'}")
    
    all_passed = classifier and results and reporting_success and imports_success
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED! System is ready for deployment.")
        print("\n🚀 To launch the Streamlit app, run:")
        print("   streamlit run streamlit_app.py")
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
