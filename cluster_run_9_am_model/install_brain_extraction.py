#!/usr/bin/env python3
"""
Installation script for brain extraction tools
Installs the best available brain extraction tools for MRI preprocessing
"""

import subprocess
import sys
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_package(package_name, pip_name=None):
    """Install a package using pip"""
    if pip_name is None:
        pip_name = package_name
    
    try:
        logger.info(f"Installing {package_name}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', pip_name])
        logger.info(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install {package_name}: {e}")
        return False

def test_import(module_name, package_name):
    """Test if a module can be imported"""
    try:
        __import__(module_name)
        logger.info(f"✅ {package_name} is available")
        return True
    except ImportError:
        logger.warning(f"⚠️ {package_name} is not available")
        return False

def main():
    """Main installation function"""
    logger.info("🧠 Installing Brain Extraction Tools for Enhanced MRI Preprocessing")
    logger.info("=" * 70)
    
    # Core dependencies
    core_packages = [
        ('numpy', 'numpy>=1.21.0'),
        ('scipy', 'scipy>=1.7.0'),
        ('scikit-learn', 'scikit-learn>=1.0.0'),
        ('nibabel', 'nibabel>=3.2.0'),
        ('nilearn', 'nilearn>=0.12.0'),
    ]
    
    logger.info("📦 Installing core dependencies...")
    for package, pip_name in core_packages:
        install_package(package, pip_name)
    
    # Brain extraction tools (in order of preference)
    brain_tools = []
    
    # Try to install HD-BET (best quality)
    logger.info("\n🎯 Installing HD-BET (state-of-the-art brain extraction)...")
    if install_package("HD-BET", "HD-BET"):
        brain_tools.append("HD-BET")
    
    # Try to install deepbrain (good alternative)
    logger.info("\n🚀 Installing deepbrain (fast brain extraction)...")
    if install_package("deepbrain", "deepbrain"):
        brain_tools.append("deepbrain")
    
    # Additional image processing tools
    logger.info("\n🖼️ Installing image processing tools...")
    additional_packages = [
        ('opencv-python', 'opencv-python>=4.5.0'),
        ('Pillow', 'Pillow>=8.0.0'),
        ('SimpleITK', 'SimpleITK>=2.1.0'),
    ]
    
    for package, pip_name in additional_packages:
        install_package(package, pip_name)
    
    # Test installations
    logger.info("\n🔍 Testing installations...")
    
    # Test core packages
    test_results = []
    test_results.append(test_import('numpy', 'NumPy'))
    test_results.append(test_import('scipy', 'SciPy'))
    test_results.append(test_import('sklearn', 'scikit-learn'))
    test_results.append(test_import('nibabel', 'NiBabel'))
    test_results.append(test_import('nilearn', 'NIlearn'))
    
    # Test brain extraction tools
    hd_bet_available = test_import('HD_BET', 'HD-BET')
    deepbrain_available = test_import('deepbrain', 'deepbrain')
    
    # Summary
    logger.info("\n📊 Installation Summary:")
    logger.info("=" * 40)
    
    if hd_bet_available:
        logger.info("✅ HD-BET: State-of-the-art brain extraction available")
    elif deepbrain_available:
        logger.info("✅ deepbrain: Fast brain extraction available")
    else:
        logger.warning("⚠️ No advanced brain extraction tools available")
        logger.info("💡 Will use morphological brain extraction methods")
    
    if all(test_results):
        logger.info("✅ All core dependencies installed successfully")
    else:
        logger.warning("⚠️ Some core dependencies may be missing")
    
    logger.info("\n🎉 Installation complete!")
    logger.info("🏥 Enhanced brain extraction is now available for MRI preprocessing")
    
    # Recommendations
    logger.info("\n💡 Recommendations:")
    if not hd_bet_available and not deepbrain_available:
        logger.info("- For best results, manually install HD-BET:")
        logger.info("  pip install torch torchvision")
        logger.info("  pip install HD-BET")
        logger.info("- Or install deepbrain for faster processing:")
        logger.info("  pip install deepbrain")
    
    logger.info("- Restart your application to use the new brain extraction tools")

if __name__ == "__main__":
    main()
