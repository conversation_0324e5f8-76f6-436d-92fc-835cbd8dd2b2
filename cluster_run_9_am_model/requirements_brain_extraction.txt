# Enhanced Brain Extraction Requirements
# Professional brain extraction and preprocessing tools

# Core dependencies
numpy>=1.21.0
scipy>=1.7.0
scikit-learn>=1.0.0
nibabel>=3.2.0
nilearn>=0.12.0

# Brain extraction tools (install in order of preference)

# HD-BET - State-of-the-art deep learning brain extraction
# Note: Requires PyTorch and may need manual installation
# pip install HD-BET

# deepbrain - Fast and reliable brain extraction
# pip install deepbrain

# Alternative brain extraction tools
# antspyx  # Advanced Normalization Tools (requires compilation)
# fsl-bet  # FSL Brain Extraction Tool (requires FSL installation)

# Image processing
opencv-python>=4.5.0
Pillow>=8.0.0

# Medical imaging
SimpleITK>=2.1.0

# Optional: Advanced preprocessing
# ants  # Advanced Normalization Tools Python wrapper
# freesurfer-python  # FreeSurfer Python tools

# Installation commands for brain extraction tools:
# 
# For HD-BET (recommended):
# pip install torch torchvision
# pip install HD-BET
#
# For deepbrain (lightweight alternative):
# pip install deepbrain
#
# For ANTs (advanced):
# pip install antspyx
#
# Note: Some tools may require additional system dependencies
