#!/usr/bin/env python3
"""
Enhanced Demetify Setup Script
Automated setup for NIlearn-enhanced frontend
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ required. Current version:", sys.version)
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_conda_environment():
    """Check if we're in a conda environment"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✅ Running in conda environment: {conda_env}")
        return True
    else:
        print("⚠️  Not in a conda environment. Consider using 'conda activate abstract'")
        return False

def install_requirements():
    """Install required packages"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    # Install basic requirements
    if not run_command(f"pip install -r {requirements_file}", "Installing basic requirements"):
        return False
    
    # Install additional NIlearn dependencies
    additional_packages = [
        "nilearn>=0.10.0",
        "nibabel>=3.2.0", 
        "scikit-learn>=1.0.0"
    ]
    
    for package in additional_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            print(f"⚠️  Failed to install {package}, but continuing...")
    
    return True

def verify_installation():
    """Verify that all packages are properly installed"""
    print("\n🔍 Verifying installation...")
    
    packages_to_check = [
        ("torch", "PyTorch"),
        ("streamlit", "Streamlit"),
        ("numpy", "NumPy"),
        ("pandas", "Pandas"),
        ("plotly", "Plotly"),
        ("scipy", "SciPy"),
        ("matplotlib", "Matplotlib"),
        ("PIL", "Pillow"),
        ("nibabel", "NiBabel"),
        ("nilearn", "NIlearn"),
        ("sklearn", "Scikit-learn")
    ]
    
    failed_imports = []
    
    for package, name in packages_to_check:
        try:
            __import__(package)
            print(f"✅ {name} imported successfully")
        except ImportError:
            print(f"❌ {name} import failed")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n⚠️  Some packages failed to import: {', '.join(failed_imports)}")
        print("The application may still work with reduced functionality.")
        return False
    else:
        print("\n✅ All packages verified successfully!")
        return True

def create_launch_script():
    """Create a convenient launch script"""
    launch_script = Path(__file__).parent / "launch_enhanced.sh"
    
    script_content = """#!/bin/bash
# Enhanced Demetify Launch Script

echo "🚀 Launching Enhanced Demetify Frontend..."
echo "📊 Advanced Features with NIlearn Integration"
echo ""

# Check if in conda environment
if [[ -n "$CONDA_DEFAULT_ENV" ]]; then
    echo "✅ Conda environment: $CONDA_DEFAULT_ENV"
else
    echo "⚠️  Consider activating conda environment: conda activate abstract"
fi

echo ""
echo "🌐 Starting Streamlit application..."
echo "📱 Access at: http://localhost:9999"
echo ""

streamlit run streamlit_app.py --server.port 9999 --server.maxUploadSize 500
"""
    
    try:
        with open(launch_script, 'w') as f:
            f.write(script_content)
        
        # Make executable on Unix systems
        if os.name != 'nt':
            os.chmod(launch_script, 0o755)
        
        print(f"✅ Launch script created: {launch_script}")
        return True
    except Exception as e:
        print(f"❌ Failed to create launch script: {e}")
        return False

def main():
    """Main setup function"""
    print("🎯 Enhanced Demetify Setup")
    print("=" * 50)
    print("Setting up NIlearn-enhanced medical imaging frontend")
    print("")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check conda environment
    check_conda_environment()
    
    # Install requirements
    if not install_requirements():
        print("\n❌ Installation failed. Please check errors above.")
        sys.exit(1)
    
    # Verify installation
    verify_installation()
    
    # Create launch script
    create_launch_script()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("")
    print("🚀 To launch the application:")
    print("   ./launch_enhanced.sh")
    print("   OR")
    print("   streamlit run streamlit_app.py --server.port 9999")
    print("")
    print("🌐 Access at: http://localhost:9999")
    print("📚 Documentation: README_ENHANCED.md")
    print("")
    print("🏥 Features:")
    print("   • NIlearn professional visualization")
    print("   • High-resolution MRI display")
    print("   • Transform tracking system")
    print("   • Enhanced SHAP interpretability")
    print("   • Clinical-grade reporting")

if __name__ == "__main__":
    main()
