"""
Create sample MRI data for testing the dementia classifier
"""

import numpy as np
import os
from pathlib import Path

def create_sample_mri_data():
    """Create realistic sample MRI data for testing"""
    
    # Create sample_data directory
    sample_dir = Path("sample_data")
    sample_dir.mkdir(exist_ok=True)
    
    # Sample 1: Normal Cognition (should predict class 0)
    print("Creating sample_normal_cognition.npy...")
    normal_data = np.random.normal(0.5, 0.2, (182, 218, 182))
    # Add some brain-like structure
    normal_data[50:130, 60:160, 50:130] += 0.3  # Central brain region
    normal_data[70:110, 80:140, 70:110] += 0.2  # Inner structures
    # Z-score normalize
    normal_data = (normal_data - np.mean(normal_data)) / np.std(normal_data)
    np.save(sample_dir / "sample_normal_cognition.npy", normal_data.astype(np.float32))
    
    # Sample 2: Alzheimer's Disease (should predict class 1)
    print("Creating sample_alzheimers_disease.npy...")
    ad_data = np.random.normal(0.3, 0.25, (182, 218, 182))
    # Simulate atrophy patterns
    ad_data[50:130, 60:160, 50:130] += 0.2  # Reduced central activity
    ad_data[80:100, 90:130, 80:100] -= 0.1  # Hippocampal atrophy simulation
    # Z-score normalize
    ad_data = (ad_data - np.mean(ad_data)) / np.std(ad_data)
    np.save(sample_dir / "sample_alzheimers_disease.npy", ad_data.astype(np.float32))
    
    # Sample 3: Other Dementia (should predict class 2 and trigger specialized)
    print("Creating sample_other_dementia.npy...")
    other_data = np.random.normal(0.4, 0.3, (182, 218, 182))
    # Different pattern from AD
    other_data[60:120, 70:150, 60:120] += 0.25
    other_data[90:110, 100:120, 90:110] += 0.15  # Different affected regions
    # Z-score normalize
    other_data = (other_data - np.mean(other_data)) / np.std(other_data)
    np.save(sample_dir / "sample_other_dementia.npy", other_data.astype(np.float32))
    
    # Sample 4: Smaller test file (different dimensions to test resizing)
    print("Creating sample_small_scan.npy...")
    small_data = np.random.normal(0.4, 0.2, (100, 120, 100))
    small_data = (small_data - np.mean(small_data)) / np.std(small_data)
    np.save(sample_dir / "sample_small_scan.npy", small_data.astype(np.float32))
    
    # Create metadata file
    metadata = {
        "sample_normal_cognition.npy": {
            "description": "Simulated normal cognition MRI scan",
            "expected_prediction": "Normal Cognition",
            "shape": "(182, 218, 182)"
        },
        "sample_alzheimers_disease.npy": {
            "description": "Simulated Alzheimer's disease MRI scan",
            "expected_prediction": "Alzheimer's Disease", 
            "shape": "(182, 218, 182)"
        },
        "sample_other_dementia.npy": {
            "description": "Simulated other dementia MRI scan",
            "expected_prediction": "Other Dementia (triggers specialized classifier)",
            "shape": "(182, 218, 182)"
        },
        "sample_small_scan.npy": {
            "description": "Smaller MRI scan to test resizing functionality",
            "expected_prediction": "Variable (tests preprocessing)",
            "shape": "(100, 120, 100)"
        }
    }
    
    import json
    with open(sample_dir / "metadata.json", "w") as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\n✅ Sample data created successfully in {sample_dir}/")
    print("Files created:")
    for file in sample_dir.glob("*.npy"):
        size_mb = file.stat().st_size / (1024 * 1024)
        print(f"  - {file.name} ({size_mb:.1f} MB)")
    print(f"  - metadata.json")

if __name__ == "__main__":
    create_sample_mri_data()
