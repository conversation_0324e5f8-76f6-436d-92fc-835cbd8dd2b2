"""
Radiological Brain Region Analysis for Dementia Assessment
Based on clinical standards from radiologyassistant.nl and medical literature
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
import nibabel as nib

logger = logging.getLogger(__name__)

class RadiologicalBrainAtlas:
    """
    Brain atlas focused on radiologically relevant regions for dementia assessment
    Based on clinical standards and radiological practice
    """
    
    def __init__(self):
        """Initialize radiological brain regions of interest"""
        
        # Primary regions radiologists examine for dementia
        self.dementia_regions = {
            # Hippocampal complex (most important for AD)
            'hippocampus_left': {
                'name': 'Left Hippocampus',
                'clinical_significance': 'Primary site of early AD pathology',
                'expected_changes': 'Atrophy in AD, preserved in FTD',
                'coordinates': self._get_hippocampus_coordinates('left')
            },
            'hippocampus_right': {
                'name': 'Right Hippocampus', 
                'clinical_significance': 'Primary site of early AD pathology',
                'expected_changes': 'Atrophy in AD, preserved in FTD',
                'coordinates': self._get_hippocampus_coordinates('right')
            },
            
            # Temporal lobe regions
            'temporal_cortex_left': {
                'name': 'Left Temporal Cortex',
                'clinical_significance': 'Language and memory processing',
                'expected_changes': 'Atrophy in AD and semantic dementia',
                'coordinates': self._get_temporal_coordinates('left')
            },
            'temporal_cortex_right': {
                'name': 'Right Temporal Cortex',
                'clinical_significance': 'Memory and visuospatial processing', 
                'expected_changes': 'Atrophy in AD',
                'coordinates': self._get_temporal_coordinates('right')
            },
            
            # Frontal regions (important for FTD)
            'frontal_cortex_left': {
                'name': 'Left Frontal Cortex',
                'clinical_significance': 'Executive function and behavior',
                'expected_changes': 'Primary site of FTD pathology',
                'coordinates': self._get_frontal_coordinates('left')
            },
            'frontal_cortex_right': {
                'name': 'Right Frontal Cortex',
                'clinical_significance': 'Executive function and behavior',
                'expected_changes': 'Primary site of FTD pathology', 
                'coordinates': self._get_frontal_coordinates('right')
            },
            
            # Parietal regions (important for AD)
            'parietal_cortex_left': {
                'name': 'Left Parietal Cortex',
                'clinical_significance': 'Visuospatial and language processing',
                'expected_changes': 'Atrophy in AD, especially posterior regions',
                'coordinates': self._get_parietal_coordinates('left')
            },
            'parietal_cortex_right': {
                'name': 'Right Parietal Cortex',
                'clinical_significance': 'Visuospatial processing and attention',
                'expected_changes': 'Atrophy in AD, especially posterior regions',
                'coordinates': self._get_parietal_coordinates('right')
            },
            
            # Ventricular system
            'lateral_ventricles': {
                'name': 'Lateral Ventricles',
                'clinical_significance': 'Enlargement indicates brain atrophy',
                'expected_changes': 'Enlargement in all dementias',
                'coordinates': self._get_ventricular_coordinates()
            },
            
            # Posterior cingulate (important for AD)
            'posterior_cingulate': {
                'name': 'Posterior Cingulate Cortex',
                'clinical_significance': 'Default mode network hub',
                'expected_changes': 'Early changes in AD',
                'coordinates': self._get_posterior_cingulate_coordinates()
            }
        }
        
        # Disease-specific patterns
        self.disease_patterns = {
            'alzheimers': {
                'primary_regions': ['hippocampus_left', 'hippocampus_right', 'temporal_cortex_left', 'temporal_cortex_right'],
                'secondary_regions': ['parietal_cortex_left', 'parietal_cortex_right', 'posterior_cingulate'],
                'pattern': 'medial_temporal_parietal'
            },
            'frontotemporal': {
                'primary_regions': ['frontal_cortex_left', 'frontal_cortex_right', 'temporal_cortex_left'],
                'secondary_regions': ['temporal_cortex_right'],
                'pattern': 'frontotemporal'
            },
            'vascular': {
                'primary_regions': ['frontal_cortex_left', 'frontal_cortex_right'],
                'secondary_regions': ['parietal_cortex_left', 'parietal_cortex_right'],
                'pattern': 'subcortical_frontal'
            }
        }
    
    def _get_hippocampus_coordinates(self, side: str) -> Dict:
        """Get hippocampus coordinates in MNI space"""
        if side == 'left':
            return {
                'center': [-25, -15, -15],  # MNI coordinates
                'radius': 8,
                'shape': 'ellipsoid',
                'dimensions': [12, 8, 6]  # AP, ML, SI
            }
        else:
            return {
                'center': [25, -15, -15],
                'radius': 8, 
                'shape': 'ellipsoid',
                'dimensions': [12, 8, 6]
            }
    
    def _get_temporal_coordinates(self, side: str) -> Dict:
        """Get temporal cortex coordinates"""
        if side == 'left':
            return {
                'center': [-50, -20, -10],
                'radius': 15,
                'shape': 'ellipsoid',
                'dimensions': [25, 20, 15]
            }
        else:
            return {
                'center': [50, -20, -10],
                'radius': 15,
                'shape': 'ellipsoid', 
                'dimensions': [25, 20, 15]
            }
    
    def _get_frontal_coordinates(self, side: str) -> Dict:
        """Get frontal cortex coordinates"""
        if side == 'left':
            return {
                'center': [-35, 25, 25],
                'radius': 20,
                'shape': 'ellipsoid',
                'dimensions': [30, 25, 20]
            }
        else:
            return {
                'center': [35, 25, 25],
                'radius': 20,
                'shape': 'ellipsoid',
                'dimensions': [30, 25, 20]
            }
    
    def _get_parietal_coordinates(self, side: str) -> Dict:
        """Get parietal cortex coordinates"""
        if side == 'left':
            return {
                'center': [-40, -50, 45],
                'radius': 18,
                'shape': 'ellipsoid',
                'dimensions': [25, 20, 18]
            }
        else:
            return {
                'center': [40, -50, 45],
                'radius': 18,
                'shape': 'ellipsoid',
                'dimensions': [25, 20, 18]
            }
    
    def _get_ventricular_coordinates(self) -> Dict:
        """Get lateral ventricle coordinates"""
        return {
            'center': [0, 0, 10],
            'radius': 12,
            'shape': 'bilateral',
            'left_center': [-15, -10, 10],
            'right_center': [15, -10, 10],
            'dimensions': [8, 15, 20]
        }
    
    def _get_posterior_cingulate_coordinates(self) -> Dict:
        """Get posterior cingulate coordinates"""
        return {
            'center': [0, -50, 25],
            'radius': 10,
            'shape': 'midline',
            'dimensions': [8, 12, 8]
        }
    
    def create_region_mask(self, region_name: str, image_shape: Tuple[int, int, int], 
                          affine: np.ndarray) -> np.ndarray:
        """
        Create a binary mask for a specific brain region
        
        Args:
            region_name: Name of the region from dementia_regions
            image_shape: Shape of the target image
            affine: Affine transformation matrix
            
        Returns:
            Binary mask array
        """
        if region_name not in self.dementia_regions:
            logger.warning(f"Region {region_name} not found in atlas")
            return np.zeros(image_shape, dtype=bool)
        
        region_info = self.dementia_regions[region_name]
        coords = region_info['coordinates']
        
        # Create coordinate grids
        x, y, z = np.meshgrid(
            np.arange(image_shape[0]),
            np.arange(image_shape[1]), 
            np.arange(image_shape[2]),
            indexing='ij'
        )
        
        # Convert voxel coordinates to world coordinates
        voxel_coords = np.stack([x.ravel(), y.ravel(), z.ravel(), np.ones(x.size)])
        world_coords = affine @ voxel_coords
        world_coords = world_coords[:3].T.reshape(image_shape + (3,))
        
        # Create mask based on region shape
        if coords['shape'] == 'ellipsoid':
            center = np.array(coords['center'])
            dimensions = np.array(coords['dimensions'])
            
            # Calculate ellipsoidal distance
            diff = world_coords - center
            ellipsoid_dist = np.sum((diff / dimensions) ** 2, axis=-1)
            mask = ellipsoid_dist <= 1.0
            
        elif coords['shape'] == 'bilateral':
            # Handle bilateral structures like ventricles
            left_center = np.array(coords['left_center'])
            right_center = np.array(coords['right_center'])
            dimensions = np.array(coords['dimensions'])
            
            # Left side
            diff_left = world_coords - left_center
            left_dist = np.sum((diff_left / dimensions) ** 2, axis=-1)
            left_mask = left_dist <= 1.0
            
            # Right side
            diff_right = world_coords - right_center
            right_dist = np.sum((diff_right / dimensions) ** 2, axis=-1)
            right_mask = right_dist <= 1.0
            
            mask = left_mask | right_mask
            
        else:
            # Default spherical region
            center = np.array(coords['center'])
            radius = coords['radius']
            
            diff = world_coords - center
            distance = np.sqrt(np.sum(diff ** 2, axis=-1))
            mask = distance <= radius
        
        return mask.astype(bool)
    
    def get_disease_specific_regions(self, predicted_disease: str) -> List[str]:
        """
        Get the most relevant regions for a specific disease
        
        Args:
            predicted_disease: Predicted disease type
            
        Returns:
            List of region names ordered by clinical relevance
        """
        disease_key = predicted_disease.lower().replace(' ', '_').replace("'", "")
        
        if 'alzheimer' in disease_key:
            disease_key = 'alzheimers'
        elif 'frontotemporal' in disease_key:
            disease_key = 'frontotemporal'
        elif 'vascular' in disease_key:
            disease_key = 'vascular'
        
        if disease_key in self.disease_patterns:
            pattern = self.disease_patterns[disease_key]
            return pattern['primary_regions'] + pattern['secondary_regions']
        else:
            # Default to most common dementia regions
            return ['hippocampus_left', 'hippocampus_right', 'temporal_cortex_left', 'temporal_cortex_right']
