"""
Hierarchical Dementia Classification System
Two-stage classification with 85.3% test accuracy
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
import os
from pathlib import Path

from .models import EnhancedMRIClassifier, SpecializedDementiaClassifier

logger = logging.getLogger(__name__)

class HierarchicalDementiaClassifier:
    """
    Hierarchical dementia classification system with two-stage approach:
    1. Main classifier: Normal Cognition vs Alzheimer's Disease vs Other Dementia
    2. Specialized classifier: Fine-grained dementia subtype classification
    """
    
    def __init__(self, main_model_path: Optional[str] = None, specialized_model_path: Optional[str] = None):
        """
        Initialize the hierarchical classifier
        
        Args:
            main_model_path: Path to main classifier weights
            specialized_model_path: Path to specialized classifier weights
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        # Class definitions
        self.main_classes = [
            "Normal Cognition",
            "Alzheimer's Disease", 
            "Other Dementia"
        ]
        
        self.specialized_classes = [
            "Lewy Body Disease",
            "Frontotemporal Dementia",
            "Vascular Dementia",
            "Other Neurologic"
        ]
        
        # Performance metrics
        self.main_performance = {
            "accuracy": 85.29,
            "f1_score": 0.856,
            "class_performance": {
                "Alzheimer's Disease": {"precision": 92, "recall": 83},
                "Normal Cognition": {"precision": 89, "recall": 92},
                "Other Dementia": {"precision": 54, "recall": 64}
            }
        }
        
        # Initialize brain extraction tools
        self.brain_extraction_method = self._setup_brain_extraction()

        # Initialize models
        self.main_model = None
        self.specialized_model = None

        # Load models if paths provided
        if main_model_path and os.path.exists(main_model_path):
            self.load_main_model(main_model_path)
        else:
            self._create_demo_main_model()

        if specialized_model_path and os.path.exists(specialized_model_path):
            self.load_specialized_model(specialized_model_path)
        else:
            self._create_demo_specialized_model()

    def _setup_brain_extraction(self):
        """Setup brain extraction tools"""
        logger.info("Setting up brain extraction tools...")

        # Check for HD-BET (best quality)
        try:
            # Try different import methods for HD-BET
            try:
                import HD_BET
                logger.info("HD-BET is available - using state-of-the-art brain extraction")
                return 'hd-bet'
            except ImportError:
                try:
                    import hd_bet
                    logger.info("HD-BET is available - using state-of-the-art brain extraction")
                    return 'hd-bet'
                except ImportError:
                    # Check if HD-BET command line tool is available
                    import subprocess
                    result = subprocess.run(['which', 'hd-bet'], capture_output=True, text=True)
                    if result.returncode == 0:
                        logger.info("HD-BET command line tool is available - using state-of-the-art brain extraction")
                        return 'hd-bet'
        except Exception:
            pass

        # Check for deepbrain (good alternative)
        try:
            import deepbrain
            logger.info("deepbrain is available - using fast brain extraction")
            return 'deepbrain'
        except ImportError:
            pass

        # Check for scipy (for advanced morphological operations)
        try:
            from scipy import ndimage
            from sklearn.cluster import KMeans
            logger.info("Using advanced morphological brain extraction")
            return 'advanced'
        except ImportError:
            pass

        logger.warning("No advanced brain extraction tools available - using basic method")
        logger.info("For better results, install: pip install hd-bet deepbrain scipy scikit-learn")
        return 'basic'
    
    def _create_demo_main_model(self):
        """Create demo main model with random weights for demonstration"""
        logger.info("Creating demo main model with random weights")
        try:
            self.main_model = EnhancedMRIClassifier(num_classes=3)
            self.main_model.to(self.device)
            self.main_model.eval()
        except Exception as e:
            logger.error(f"Error creating main model: {e}")
            self.main_model = None
    
    def _create_demo_specialized_model(self):
        """Create demo specialized model with random weights for demonstration"""
        logger.info("Creating demo specialized model with random weights")
        try:
            self.specialized_model = SpecializedDementiaClassifier(num_classes=4)
            self.specialized_model.to(self.device)
            self.specialized_model.eval()
        except Exception as e:
            logger.error(f"Error creating specialized model: {e}")
            self.specialized_model = None
    
    def load_main_model(self, model_path: str):
        """Load the main classifier model"""
        try:
            self.main_model = EnhancedMRIClassifier(num_classes=3)
            state_dict = torch.load(model_path, map_location=self.device)
            self.main_model.load_state_dict(state_dict)
            self.main_model.to(self.device)
            self.main_model.eval()
            logger.info(f"Main model loaded from {model_path}")
        except Exception as e:
            logger.error(f"Error loading main model: {e}")
            self._create_demo_main_model()
    
    def load_specialized_model(self, model_path: str):
        """Load the specialized classifier model"""
        try:
            self.specialized_model = SpecializedDementiaClassifier(num_classes=4)
            state_dict = torch.load(model_path, map_location=self.device)
            self.specialized_model.load_state_dict(state_dict)
            self.specialized_model.to(self.device)
            self.specialized_model.eval()
            logger.info(f"Specialized model loaded from {model_path}")
        except Exception as e:
            logger.error(f"Error loading specialized model: {e}")
            self._create_demo_specialized_model()
    
    def preprocess_mri(self, mri_data: np.ndarray) -> torch.Tensor:
        """
        Preprocess MRI data for model input
        
        Args:
            mri_data: NumPy array with shape (182, 218, 182)
            
        Returns:
            Preprocessed tensor ready for model input
        """
        # Ensure correct shape
        if mri_data.shape != (182, 218, 182):
            logger.warning(f"Expected shape (182, 218, 182), got {mri_data.shape}")
            # Resize if needed (simplified approach)
            try:
                from scipy.ndimage import zoom
                target_shape = (182, 218, 182)
                zoom_factors = [t/s for t, s in zip(target_shape, mri_data.shape)]
                mri_data = zoom(mri_data, zoom_factors, order=1)
            except ImportError:
                # Fallback: simple interpolation
                import torch.nn.functional as F
                mri_tensor = torch.FloatTensor(mri_data).unsqueeze(0).unsqueeze(0)
                mri_tensor = F.interpolate(mri_tensor, size=(182, 218, 182), mode='trilinear', align_corners=False)
                mri_data = mri_tensor.squeeze().numpy()
        
        # Z-score normalization
        mean = np.mean(mri_data)
        std = np.std(mri_data)
        if std > 0:
            mri_data = (mri_data - mean) / std
        
        # Convert to tensor and add batch and channel dimensions
        mri_tensor = torch.FloatTensor(mri_data[np.newaxis, np.newaxis, ...])
        return mri_tensor.to(self.device)

    def load_and_preprocess_mri(self, mri_input) -> Dict:
        """
        Load and preprocess MRI data from various input formats

        Args:
            mri_input: Can be:
                - String path to .npy or .nii/.nii.gz file
                - NumPy array
                - NIfTI image object

        Returns:
            Dictionary containing original and preprocessed data with spatial info
        """
        try:
            original_data = None
            affine = None
            voxel_sizes = None
            is_nifti = False

            # Handle different input types
            if isinstance(mri_input, str):
                # File path input
                if mri_input.endswith('.npy'):
                    # NumPy file
                    original_data = np.load(mri_input)
                    affine = np.eye(4)  # Default identity affine
                    voxel_sizes = np.array([1.0, 1.0, 1.0])  # Default 1mm voxels
                    logger.info(f"Loaded NumPy MRI data with shape: {original_data.shape}")

                elif mri_input.endswith(('.nii', '.nii.gz')):
                    # NIfTI file
                    import nibabel as nib
                    nifti_img = nib.load(mri_input)
                    original_data = nifti_img.get_fdata()
                    affine = nifti_img.affine
                    voxel_sizes = np.abs(np.diag(affine)[:3])
                    is_nifti = True
                    logger.info(f"Loaded NIfTI MRI data with shape: {original_data.shape}")
                    logger.info(f"Voxel sizes: {voxel_sizes}")

                else:
                    raise ValueError(f"Unsupported file format: {mri_input}")

            elif isinstance(mri_input, np.ndarray):
                # NumPy array input
                original_data = mri_input.copy()
                affine = np.eye(4)
                voxel_sizes = np.array([1.0, 1.0, 1.0])
                logger.info(f"Using NumPy array with shape: {original_data.shape}")

            else:
                # Try to handle as NIfTI image object
                try:
                    original_data = mri_input.get_fdata()
                    affine = mri_input.affine
                    voxel_sizes = np.abs(np.diag(affine)[:3])
                    is_nifti = True
                    logger.info(f"Using NIfTI image object with shape: {original_data.shape}")
                except:
                    raise ValueError(f"Unsupported input type: {type(mri_input)}")

            # Validate data
            if original_data is None:
                raise ValueError("Failed to load MRI data")

            if len(original_data.shape) != 3:
                raise ValueError(f"Expected 3D MRI data, got shape: {original_data.shape}")

            # Apply comprehensive preprocessing pipeline
            preprocessed_data = self.apply_preprocessing_pipeline(original_data)

            # Create spatial information dictionary
            spatial_info = {
                'original_data': original_data,
                'data': preprocessed_data,
                'affine': affine,
                'voxel_sizes': voxel_sizes,
                'is_nifti': is_nifti,
                'shape': original_data.shape
            }

            return spatial_info

        except Exception as e:
            logger.error(f"Error loading/preprocessing MRI: {e}")
            raise

    def _install_brain_extraction_tools(self):
        """Install brain extraction tools if not available"""
        try:
            # Try to install HD-BET (state-of-the-art)
            try:
                import subprocess
                import sys
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'hd-bet'])
                logger.info("HD-BET installed successfully")
                return 'hd-bet'
            except:
                pass

            # Try to install deepbrain (lightweight alternative)
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'deepbrain'])
                logger.info("deepbrain installed successfully")
                return 'deepbrain'
            except:
                pass

            logger.warning("Could not install brain extraction tools, using basic method")
            return 'basic'

        except Exception as e:
            logger.error(f"Error installing brain extraction tools: {e}")
            return 'basic'

    def _extract_brain_hd_bet(self, data: np.ndarray, affine: np.ndarray) -> np.ndarray:
        """Extract brain using HD-BET (state-of-the-art deep learning method)"""
        try:
            import tempfile
            import os
            import nibabel as nib

            # Try different import paths for HD-BET
            try:
                from HD_BET.run import run_hd_bet
            except ImportError:
                try:
                    from hd_bet.run import run_hd_bet
                except ImportError:
                    # Try command line approach
                    import subprocess

                    # Create temporary files
                    with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as tmp_input:
                        with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as tmp_output:

                            # Save input image
                            input_img = nib.Nifti1Image(data, affine)
                            nib.save(input_img, tmp_input.name)

                            # Run HD-BET via command line (correct arguments)
                            cmd = ['hd-bet', '-i', tmp_input.name, '-o', tmp_output.name, '-device', 'cpu']
                            result = subprocess.run(cmd, capture_output=True, text=True)

                            if result.returncode == 0:
                                # Load result
                                result_img = nib.load(tmp_output.name)
                                extracted_data = result_img.get_fdata()

                                # Cleanup
                                os.unlink(tmp_input.name)
                                os.unlink(tmp_output.name)

                                logger.info("Brain extraction completed using HD-BET (command line)")
                                return extracted_data
                            else:
                                logger.warning(f"HD-BET command line failed: {result.stderr}")
                                return None

            # Create temporary files
            with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as tmp_input:
                with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as tmp_output:

                    # Save input image
                    input_img = nib.Nifti1Image(data, affine)
                    nib.save(input_img, tmp_input.name)

                    # Run HD-BET
                    run_hd_bet(tmp_input.name, tmp_output.name, mode='fast', device='cpu')

                    # Load result
                    result_img = nib.load(tmp_output.name)
                    extracted_data = result_img.get_fdata()

                    # Cleanup
                    os.unlink(tmp_input.name)
                    os.unlink(tmp_output.name)

                    logger.info("Brain extraction completed using HD-BET")
                    return extracted_data

        except Exception as e:
            logger.warning(f"HD-BET extraction failed: {e}")
            return None

    def _extract_brain_deepbrain(self, data: np.ndarray, affine: np.ndarray) -> np.ndarray:
        """Extract brain using deepbrain (fast and reliable method)"""
        try:
            import tempfile
            import os
            import nibabel as nib
            from deepbrain import Extractor

            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as tmp_file:
                # Save input image
                input_img = nib.Nifti1Image(data, affine)
                nib.save(input_img, tmp_file.name)

                # Initialize extractor
                ext = Extractor()

                # Extract brain
                prob = ext.run(tmp_file.name)

                # Apply probability mask (threshold at 0.5)
                brain_mask = prob > 0.5
                extracted_data = data * brain_mask

                # Cleanup
                os.unlink(tmp_file.name)

                logger.info("Brain extraction completed using deepbrain")
                return extracted_data

        except Exception as e:
            logger.warning(f"deepbrain extraction failed: {e}")
            return None

    def _extract_brain_advanced(self, data: np.ndarray) -> np.ndarray:
        """Advanced brain extraction using morphological operations and clustering"""
        try:
            from scipy import ndimage
            from sklearn.cluster import KMeans

            # Step 1: Remove obvious background (very low intensities)
            threshold_low = np.percentile(data[data > 0], 1) if np.any(data > 0) else 0
            mask_initial = data > threshold_low

            # Step 2: Morphological operations to clean up
            # Remove small objects
            mask_cleaned = ndimage.binary_opening(mask_initial, structure=np.ones((3,3,3)))
            mask_cleaned = ndimage.binary_closing(mask_cleaned, structure=np.ones((5,5,5)))

            # Step 3: Find largest connected component (should be brain)
            labeled, num_features = ndimage.label(mask_cleaned)
            if num_features > 0:
                # Find largest component
                component_sizes = ndimage.sum(mask_cleaned, labeled, range(1, num_features + 1))
                largest_component = np.argmax(component_sizes) + 1
                brain_mask = labeled == largest_component
            else:
                brain_mask = mask_cleaned

            # Step 4: Morphological operations to smooth brain boundary
            brain_mask = ndimage.binary_fill_holes(brain_mask)
            brain_mask = ndimage.binary_closing(brain_mask, structure=np.ones((3,3,3)))

            # Step 5: Apply mask
            extracted_data = data * brain_mask

            logger.info("Advanced brain extraction completed")
            return extracted_data

        except Exception as e:
            logger.warning(f"Advanced brain extraction failed: {e}")
            return data

    def apply_preprocessing_pipeline(self, data: np.ndarray) -> np.ndarray:
        """
        Apply comprehensive preprocessing pipeline with professional brain extraction

        Args:
            data: Raw MRI data

        Returns:
            Preprocessed MRI data with proper brain extraction
        """
        try:
            logger.info("Applying comprehensive preprocessing pipeline with brain extraction...")

            # Step 1: Handle NaN and infinite values
            data = np.nan_to_num(data, nan=0.0, posinf=0.0, neginf=0.0)

            # Step 2: Initial intensity normalization
            data_min = np.min(data)
            data_max = np.max(data)
            logger.info(f"Original data range: [{data_min:.3f}, {data_max:.3f}]")

            # Normalize to 0-1 range for brain extraction tools
            if data_max > data_min:
                data_normalized = (data - data_min) / (data_max - data_min)
            else:
                data_normalized = data

            # Step 3: Professional brain extraction
            affine = np.eye(4)  # Default affine for brain extraction
            extracted_data = None

            # Try HD-BET first (best quality)
            try:
                extracted_data = self._extract_brain_hd_bet(data_normalized, affine)
                if extracted_data is not None:
                    logger.info("Using HD-BET brain extraction")
            except:
                pass

            # Try deepbrain if HD-BET failed
            if extracted_data is None:
                try:
                    extracted_data = self._extract_brain_deepbrain(data_normalized, affine)
                    if extracted_data is not None:
                        logger.info("Using deepbrain extraction")
                except:
                    pass

            # Use advanced morphological method if both failed
            if extracted_data is None:
                try:
                    extracted_data = self._extract_brain_advanced(data_normalized)
                    logger.info("Using advanced morphological brain extraction")
                except:
                    # Fallback to basic thresholding
                    threshold = np.percentile(data_normalized[data_normalized > 0], 5) if np.any(data_normalized > 0) else 0
                    brain_mask = data_normalized > threshold
                    extracted_data = data_normalized * brain_mask
                    logger.info("Using basic threshold brain extraction")

            # Step 4: Bias field correction
            try:
                from scipy import ndimage
                # Smooth the image to estimate bias field
                smoothed = ndimage.gaussian_filter(extracted_data, sigma=8)
                # Avoid division by zero
                bias_corrected = np.divide(extracted_data, smoothed + 1e-8,
                                         out=np.zeros_like(extracted_data), where=(smoothed + 1e-8) != 0)
                extracted_data = bias_corrected
                logger.info("Bias field correction applied")
            except ImportError:
                logger.warning("Scipy not available, skipping bias field correction")

            # Step 5: Intensity normalization within brain
            brain_mask = extracted_data > 0
            brain_data = extracted_data[brain_mask]

            if len(brain_data) > 0 and np.std(brain_data) > 0:
                # Robust normalization using percentiles
                p1 = np.percentile(brain_data, 1)
                p99 = np.percentile(brain_data, 99)

                # Clip extreme values
                extracted_data = np.clip(extracted_data, p1, p99)

                # Z-score normalization
                mean_val = np.mean(brain_data)
                std_val = np.std(brain_data)
                extracted_data = (extracted_data - mean_val) / std_val

                # Apply brain mask
                extracted_data = extracted_data * brain_mask

                logger.info("Robust intensity normalization applied")

            # Step 6: Resize to standard size (128x128x128) for model input
            target_shape = (128, 128, 128)
            if extracted_data.shape != target_shape:
                try:
                    from scipy import ndimage
                    # Calculate zoom factors
                    zoom_factors = [target_shape[i] / extracted_data.shape[i] for i in range(3)]
                    extracted_data = ndimage.zoom(extracted_data, zoom_factors, order=1)
                    logger.info(f"Resized from {data.shape} to {extracted_data.shape}")
                except ImportError:
                    logger.warning("Scipy not available, using simple resizing")
                    extracted_data = self._simple_resize(extracted_data, target_shape)

            logger.info(f"Preprocessing completed. Final shape: {extracted_data.shape}")
            logger.info(f"Final data range: [{np.min(extracted_data):.3f}, {np.max(extracted_data):.3f}]")

            # Verify brain extraction quality
            nonzero_voxels = np.count_nonzero(extracted_data)
            total_voxels = extracted_data.size
            brain_ratio = nonzero_voxels / total_voxels
            logger.info(f"Brain extraction quality: {brain_ratio:.1%} of voxels retained")

            if brain_ratio < 0.1:
                logger.warning("Brain extraction may have been too aggressive")
            elif brain_ratio > 0.8:
                logger.warning("Brain extraction may not have removed enough skull/background")

            return extracted_data

        except Exception as e:
            logger.error(f"Error in preprocessing pipeline: {e}")
            # Return basic normalized data as fallback
            data_min = np.min(data)
            data_max = np.max(data)
            if data_max > data_min:
                return (data - data_min) / (data_max - data_min)
            return data

    def _simple_resize(self, data: np.ndarray, target_shape: tuple) -> np.ndarray:
        """Simple resize function when scipy is not available"""
        # This is a very basic implementation - scipy should be preferred
        x_ratio = data.shape[0] / target_shape[0]
        y_ratio = data.shape[1] / target_shape[1]
        z_ratio = data.shape[2] / target_shape[2]

        resized = np.zeros(target_shape)
        for i in range(target_shape[0]):
            for j in range(target_shape[1]):
                for k in range(target_shape[2]):
                    orig_i = min(int(i * x_ratio), data.shape[0] - 1)
                    orig_j = min(int(j * y_ratio), data.shape[1] - 1)
                    orig_k = min(int(k * z_ratio), data.shape[2] - 1)
                    resized[i, j, k] = data[orig_i, orig_j, orig_k]

        return resized

    def predict(self, mri_input) -> Dict:
        """
        Perform hierarchical prediction on MRI scan

        Args:
            mri_input: Can be path to .npy/.nii file, NumPy array, or NIfTI image

        Returns:
            Dictionary containing prediction results and spatial information
        """
        try:
            # Load and preprocess MRI data
            spatial_info = self.load_and_preprocess_mri(mri_input)

            # Get preprocessed data for model input
            preprocessed_data = spatial_info['data']

            # Preprocess for tensor
            mri_tensor = self.preprocess_mri(preprocessed_data)
            
            # Main classification
            with torch.no_grad():
                main_output = self.main_model(mri_tensor)
                main_probs = F.softmax(main_output, dim=1)
                main_pred = torch.argmax(main_output, dim=1).item()
                main_confidence = main_probs[0, main_pred].item()
            
            main_diagnosis = self.main_classes[main_pred]
            
            # Initialize specialized prediction variables
            specialized_prediction = None
            specialized_confidence = None
            specialized_probabilities = None
            model_used = "Main Classifier Only"
            
            # Specialized classification if "Other Dementia"
            if main_diagnosis == "Other Dementia":
                with torch.no_grad():
                    spec_output = self.specialized_model(mri_tensor)
                    spec_probs = F.softmax(spec_output, dim=1)
                    spec_pred = torch.argmax(spec_output, dim=1).item()
                    spec_confidence = spec_probs[0, spec_pred].item()
                
                specialized_prediction = self.specialized_classes[spec_pred]
                specialized_confidence = spec_confidence
                specialized_probabilities = spec_probs[0].cpu().numpy()
                model_used = "Hierarchical (Main + Specialized)"
            
            # Compile comprehensive results
            results = {
                'final_diagnosis': specialized_prediction if specialized_prediction else main_diagnosis,
                'main_prediction': main_diagnosis,
                'main_confidence': main_confidence,
                'main_probabilities': main_probs[0].cpu().numpy(),
                'main_class_names': self.main_classes,
                'specialized_prediction': specialized_prediction,
                'specialized_confidence': specialized_confidence,
                'specialized_probabilities': specialized_probabilities,
                'specialized_class_names': self.specialized_classes,
                'model_used': model_used,
                'input_shape': spatial_info['shape'],
                'preprocessed_shape': preprocessed_data.shape,
                'device_used': str(self.device),
                'performance_metrics': self.main_performance,
                'spatial_info': spatial_info  # Include all spatial information
            }

            logger.info(f"Prediction completed: {results['final_diagnosis']} (confidence: {main_confidence:.3f})")
            return results
            
        except Exception as e:
            logger.error(f"Error during prediction: {e}")
            raise
    
    def get_model_info(self) -> Dict:
        """Get information about the loaded models"""
        return {
            'main_model_loaded': self.main_model is not None,
            'specialized_model_loaded': self.specialized_model is not None,
            'device': str(self.device),
            'main_classes': self.main_classes,
            'specialized_classes': self.specialized_classes,
            'performance_metrics': self.main_performance
        }
    
    def batch_predict(self, mri_paths: List[str]) -> List[Dict]:
        """
        Perform batch prediction on multiple MRI scans
        
        Args:
            mri_paths: List of paths to .npy MRI files
            
        Returns:
            List of prediction results
        """
        results = []
        for path in mri_paths:
            try:
                result = self.predict(path)
                result['file_path'] = path
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing {path}: {e}")
                results.append({
                    'file_path': path,
                    'error': str(e),
                    'final_diagnosis': 'Error',
                    'main_confidence': 0.0
                })
        return results
