{"sample_normal_cognition.npy": {"description": "Simulated normal cognition MRI scan", "expected_prediction": "Normal Cognition", "shape": "(182, 218, 182)"}, "sample_alzheimers_disease.npy": {"description": "Simulated Alzheimer's disease MRI scan", "expected_prediction": "Alzheimer's Disease", "shape": "(182, 218, 182)"}, "sample_other_dementia.npy": {"description": "Simulated other dementia MRI scan", "expected_prediction": "Other Dementia (triggers specialized classifier)", "shape": "(182, 218, 182)"}, "sample_small_scan.npy": {"description": "Smaller MRI scan to test resizing functionality", "expected_prediction": "Variable (tests preprocessing)", "shape": "(100, 120, 100)"}}