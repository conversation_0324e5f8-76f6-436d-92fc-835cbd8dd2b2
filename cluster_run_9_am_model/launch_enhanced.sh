#!/bin/bash
# Enhanced Demetify Launch Script

echo "🚀 Launching Enhanced Demetify Frontend..."
echo "📊 Advanced Features with NIlearn Integration"
echo ""

# Check if in conda environment
if [[ -n "$CONDA_DEFAULT_ENV" ]]; then
    echo "✅ Conda environment: $CONDA_DEFAULT_ENV"
else
    echo "⚠️  Consider activating conda environment: conda activate abstract"
fi

echo ""
echo "🌐 Starting Streamlit application..."
echo "📱 Access at: http://localhost:9999"
echo ""

streamlit run streamlit_app.py --server.port 9999 --server.maxUploadSize 500
