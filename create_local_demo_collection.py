#!/usr/bin/env python3
"""
Create Local Demo MRI Collection for Testing
Generate 10 labeled MRI scans locally for immediate testing
"""

import numpy as np
import nibabel as nib
from pathlib import Path
import json

def create_demo_mri_scan(class_label, mmse_score, age, case_id):
    """Create realistic demo MRI scan"""
    
    # Class-specific characteristics
    if class_label == 'CN':
        base_intensity = 0.8
        atrophy_level = 0.05
    elif class_label == 'MCI':
        base_intensity = 0.6
        atrophy_level = 0.25
    else:  # AD
        base_intensity = 0.4
        atrophy_level = 0.55
    
    # Create 3D MRI volume
    mri = np.random.normal(base_intensity, 0.08, (91, 109, 91))
    
    # Add realistic brain anatomy
    center = (45, 54, 45)
    
    # Hippocampal regions
    hippo_preservation = 1.0 - atrophy_level
    for i in range(center[0]-12, center[0]+12):
        for j in range(center[1]-15, center[1]+15):
            for k in range(center[2]-12, center[2]+12):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                    if dist < 10:
                        enhancement = 0.3 * hippo_preservation * np.exp(-dist/6)
                        mri[i, j, k] += enhancement
    
    # Cortical regions
    cortical_preservation = 1.0 - atrophy_level * 0.8
    for i in range(center[0]-25, center[0]+25):
        for j in range(center[1]-30, center[1]+30):
            for k in range(center[2]-25, center[2]+25):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                    if 15 < dist < 25:
                        enhancement = 0.25 * cortical_preservation * np.exp(-dist/12)
                        mri[i, j, k] += enhancement
    
    # Ventricular enlargement
    ventricular_enlargement = atrophy_level
    for i in range(center[0]-8, center[0]+8):
        for j in range(center[1]-10, center[1]+10):
            for k in range(center[2]-8, center[2]+8):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                    if dist < 6:
                        reduction = 0.4 * ventricular_enlargement
                        mri[i, j, k] *= (1.0 - reduction)
    
    # Age-related changes
    age_factor = (age - 65) / 30.0
    age_reduction = age_factor * 0.1
    mri *= (1.0 - age_reduction)
    
    # MMSE-correlated features
    mmse_factor = mmse_score / 30.0
    tissue_enhancement = mmse_factor * 0.1
    mri += tissue_enhancement
    
    return np.clip(mri, 0, 1)

def create_local_demo_collection():
    """Create 10 demo MRI scans locally"""
    
    print("🧠 Creating Local Demo MRI Collection...")
    
    # Create output directory
    output_dir = Path('local_demo_scans')
    output_dir.mkdir(exist_ok=True)
    
    # Demo cases with clear labels
    demo_cases = [
        # CN cases
        {'class': 'CN', 'mmse': 28, 'age': 72, 'id': 'DEMO_01_CN_MMSE28_Age72'},
        {'class': 'CN', 'mmse': 29, 'age': 68, 'id': 'DEMO_02_CN_MMSE29_Age68'},
        {'class': 'CN', 'mmse': 27, 'age': 75, 'id': 'DEMO_03_CN_MMSE27_Age75'},
        
        # MCI cases
        {'class': 'MCI', 'mmse': 22, 'age': 78, 'id': 'DEMO_04_MCI_MMSE22_Age78'},
        {'class': 'MCI', 'mmse': 20, 'age': 81, 'id': 'DEMO_05_MCI_MMSE20_Age81'},
        {'class': 'MCI', 'mmse': 24, 'age': 76, 'id': 'DEMO_06_MCI_MMSE24_Age76'},
        {'class': 'MCI', 'mmse': 19, 'age': 83, 'id': 'DEMO_07_MCI_MMSE19_Age83'},
        
        # AD cases
        {'class': 'AD', 'mmse': 14, 'age': 85, 'id': 'DEMO_08_AD_MMSE14_Age85'},
        {'class': 'AD', 'mmse': 11, 'age': 88, 'id': 'DEMO_09_AD_MMSE11_Age88'},
        {'class': 'AD', 'mmse': 16, 'age': 82, 'id': 'DEMO_10_AD_MMSE16_Age82'},
    ]
    
    collection_metadata = {
        'total_scans': len(demo_cases),
        'purpose': 'Local testing of advanced ordinal classification',
        'scans': []
    }
    
    for case in demo_cases:
        print(f"   Creating: {case['id']}")
        
        # Generate MRI scan
        mri_data = create_demo_mri_scan(case['class'], case['mmse'], case['age'], case['id'])
        
        # Save as NIfTI
        nifti_filename = f"{case['id']}.nii.gz"
        nifti_path = output_dir / nifti_filename
        
        affine = np.eye(4)
        nifti_img = nib.Nifti1Image(mri_data, affine)
        nib.save(nifti_img, str(nifti_path))
        
        # Save as NumPy
        numpy_filename = f"{case['id']}.npy"
        numpy_path = output_dir / numpy_filename
        np.save(numpy_path, mri_data)
        
        # Store metadata
        scan_metadata = {
            'case_id': case['id'],
            'class_label': case['class'],
            'mmse_score': case['mmse'],
            'age': case['age'],
            'nifti_file': nifti_filename,
            'numpy_file': numpy_filename,
            'expected_classification': case['class']
        }
        
        collection_metadata['scans'].append(scan_metadata)
    
    # Save metadata
    metadata_path = output_dir / 'collection_metadata.json'
    with open(metadata_path, 'w') as f:
        json.dump(collection_metadata, f, indent=2)
    
    # Create summary
    summary_content = f"""# Local Demo MRI Collection

## Overview
- **Total Scans**: {len(demo_cases)}
- **Purpose**: Testing advanced ordinal classification
- **Classes**: CN (3), MCI (4), AD (3)

## Test Cases
"""
    
    for case in demo_cases:
        summary_content += f"- `{case['id']}` - {case['class']} case, MMSE {case['mmse']}\n"
    
    summary_content += f"""
## Usage
Load any scan and test with the advanced ordinal classification model.
Expected: Proper MMSE distribution, no clustering around 20-22.

## Files
- 10 NIfTI files (.nii.gz)
- 10 NumPy files (.npy)
- collection_metadata.json
- README.md
"""
    
    readme_path = output_dir / 'README.md'
    with open(readme_path, 'w') as f:
        f.write(summary_content)
    
    print(f"✅ Local Demo Collection Created!")
    print(f"📁 Location: {output_dir}")
    print(f"📊 Total files: {len(demo_cases) * 2 + 2}")
    print(f"🎯 Ready for testing advanced ordinal classification!")
    
    return output_dir

if __name__ == "__main__":
    create_local_demo_collection()
