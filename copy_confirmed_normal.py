#!/usr/bin/env python3
"""
Copy the confirmed normal cognition files with NACC IDs to Windows folder
"""

import os
import shutil
from pathlib import Path

def copy_confirmed_normal_files():
    """Copy the 2 confirmed normal cognition files to Windows Downloads."""
    
    print("🎯 Copying Confirmed Normal Cognition Files with NACC IDs")
    print("=" * 60)
    
    # Confirmed normal cognition files found
    confirmed_files = [
        {
            "source": "/mnt/e/processed_data/1.2.840.113654.2.45.6228.13702219515582886242336888186495063498.nii",
            "filename": "T1_NORMAL_NACC195155_confirmed.nii",
            "nacc_id": "NACC195155",
            "naccetpr": 1,
            "description": "Confirmed Normal Cognition - NACCETPR=1"
        },
        {
            "source": "/mnt/e/processed_data/1.2.840.113654.2.45.6228.5848395279234242473829077320229909517.nii",
            "filename": "T1_NORMAL_NACC732022_confirmed.nii", 
            "nacc_id": "NACC732022",
            "naccetpr": 1,
            "description": "Confirmed Normal Cognition - NACCETPR=1"
        }
    ]
    
    # Windows path
    windows_path = "/mnt/c/Users/<USER>/Downloads/test files"
    Path(windows_path).mkdir(parents=True, exist_ok=True)
    
    copied_count = 0
    for file_info in confirmed_files:
        source_path = file_info["source"]
        dest_path = Path(windows_path) / file_info["filename"]
        
        if not os.path.exists(source_path):
            print(f"⚠️ Source file not found: {source_path}")
            continue
        
        try:
            shutil.copy2(source_path, dest_path)
            file_size_mb = round(dest_path.stat().st_size / (1024*1024), 2)
            print(f"✅ Copied: {file_info['filename']} ({file_size_mb}MB)")
            print(f"   NACC ID: {file_info['nacc_id']}")
            print(f"   NACCETPR: {file_info['naccetpr']} (Normal Cognition)")
            copied_count += 1
        except Exception as e:
            print(f"❌ Error copying {file_info['filename']}: {e}")
    
    return copied_count

def main():
    copied_count = copy_confirmed_normal_files()
    
    print(f"\n🎉 Successfully added {copied_count} confirmed normal cognition files!")
    print(f"📁 Location: /mnt/c/Users/<USER>/Downloads/test files")
    
    # Show final collection status
    windows_path = "/mnt/c/Users/<USER>/Downloads/test files"
    if os.path.exists(windows_path):
        all_files = [f for f in os.listdir(windows_path) if f.endswith(('.nii', '.npy'))]
        normal_files = [f for f in all_files if 'NORMAL' in f]
        ad_files = [f for f in all_files if 'ALZHEIMERS' in f]
        
        print(f"\n📊 **Final Collection Status:**")
        print(f"  Normal Cognition files: {len(normal_files)}")
        print(f"  Alzheimer's Disease files: {len(ad_files)}")
        print(f"  Total MRI files: {len(all_files)}")
        
        print(f"\n🏷️ **Normal Cognition Files:**")
        for f in sorted(normal_files):
            print(f"  - {f}")

if __name__ == "__main__":
    main()
