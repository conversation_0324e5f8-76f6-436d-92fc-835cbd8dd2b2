#!/usr/bin/env python3
"""
Simple Working MRI Viewer Demo
Demonstrates the aspect ratio preservation fix
"""

import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import tempfile
import os

# Page configuration
st.set_page_config(
    page_title="🔍 MRI Aspect Ratio Fix Demo",
    page_icon="🧠",
    layout="wide"
)

def load_mri_file(uploaded_file):
    """Load MRI file and extract spatial information"""
    try:
        if uploaded_file.name.endswith('.nii'):
            # Handle NIfTI file
            tmp_file = tempfile.NamedTemporaryFile(suffix='.nii', delete=False)
            uploaded_file.seek(0)
            tmp_file.write(uploaded_file.read())
            tmp_file.flush()
            tmp_file.close()
            
            # Load with nibabel
            img = nib.load(tmp_file.name)
            data = img.get_fdata()
            voxel_sizes = img.header.get_zooms()[:3]
            
            # Clean up
            os.unlink(tmp_file.name)
            
            return data, voxel_sizes
            
        elif uploaded_file.name.endswith('.npy'):
            # Handle NumPy file
            uploaded_file.seek(0)
            data = np.load(uploaded_file)
            voxel_sizes = (1.0, 1.0, 1.0)  # Assume isotropic
            return data, voxel_sizes
            
    except Exception as e:
        st.error(f"Error loading file: {e}")
        return None, None

def calculate_aspect_ratios(voxel_sizes):
    """Calculate proper aspect ratios"""
    min_voxel = min(voxel_sizes)
    return [v / min_voxel for v in voxel_sizes]

def apply_windowing(data):
    """Apply clinical windowing"""
    # Simple percentile-based windowing
    p1, p99 = np.percentile(data[data > 0], [1, 99])
    windowed = np.clip(data, p1, p99)
    windowed = (windowed - p1) / (p99 - p1)
    return windowed

def create_mri_display(data, voxel_sizes, title="MRI Viewer"):
    """Create MRI display with proper aspect ratios"""
    aspect_ratios = calculate_aspect_ratios(voxel_sizes)
    windowed_data = apply_windowing(data)
    
    # Get middle slices
    mid_x, mid_y, mid_z = [dim // 2 for dim in data.shape]
    
    # Create figure with high DPI
    fig, axes = plt.subplots(1, 3, figsize=(15, 5), dpi=200)
    fig.suptitle(f"{title}\nShape: {data.shape} | Voxels: {[f'{v:.2f}mm' for v in voxel_sizes]}", 
                 fontsize=14, fontweight='bold')
    
    # Axial view (Bottom to Top)
    axial_slice = windowed_data[:, :, mid_z]
    axes[0].imshow(axial_slice, cmap='gray', 
                   aspect=aspect_ratios[1]/aspect_ratios[0],  # Y/X ratio
                   origin='lower')
    axes[0].set_title(f'Axial (Bottom→Top)\nSlice {mid_z}', fontweight='bold')
    axes[0].axis('off')
    
    # Coronal view (Anterior to Posterior)
    coronal_slice = windowed_data[:, mid_y, :]
    axes[1].imshow(coronal_slice, cmap='gray',
                   aspect=aspect_ratios[2]/aspect_ratios[0],  # Z/X ratio
                   origin='lower')
    axes[1].set_title(f'Coronal (Ant→Post)\nSlice {mid_y}', fontweight='bold')
    axes[1].axis('off')
    
    # Sagittal view (Left to Right)
    sagittal_slice = windowed_data[mid_x, :, :]
    axes[2].imshow(sagittal_slice, cmap='gray',
                   aspect=aspect_ratios[2]/aspect_ratios[1],  # Z/Y ratio
                   origin='lower')
    axes[2].set_title(f'Sagittal (Left→Right)\nSlice {mid_x}', fontweight='bold')
    axes[2].axis('off')
    
    plt.tight_layout()
    return fig

# Main App
def main():
    st.title("🔍 MRI Aspect Ratio Fix Demo")
    st.markdown("### Testing High-Resolution Display with Proper Aspect Ratios")
    
    st.info("🎯 **This demo shows the fix for MRI image stretching and implements high-resolution display**")
    
    # File upload
    uploaded_file = st.file_uploader(
        "Upload MRI Scan",
        type=['nii', 'npy'],
        help="Upload a .nii (NIfTI) or .npy (NumPy) MRI file"
    )
    
    if uploaded_file is not None:
        st.success(f"✅ File uploaded: {uploaded_file.name}")
        
        # Load the file
        with st.spinner("Loading MRI data..."):
            data, voxel_sizes = load_mri_file(uploaded_file)
            
        if data is not None:
            st.success("✅ MRI data loaded successfully!")
            
            # Display file information
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Shape", f"{data.shape}")
            with col2:
                st.metric("Voxel Sizes", f"{voxel_sizes[0]:.3f}×{voxel_sizes[1]:.3f}×{voxel_sizes[2]:.3f}mm")
            with col3:
                real_dims = [data.shape[i] * voxel_sizes[i] for i in range(3)]
                st.metric("Real Dimensions", f"{real_dims[0]:.0f}×{real_dims[1]:.0f}×{real_dims[2]:.0f}mm")
            
            # Check for anisotropic voxels
            if not all(abs(v - voxel_sizes[0]) < 0.01 for v in voxel_sizes):
                st.warning("⚠️ **Anisotropic voxels detected** - aspect ratio preservation is critical!")
                aspect_ratios = calculate_aspect_ratios(voxel_sizes)
                st.info(f"🎯 **Aspect ratios applied**: {[f'{r:.3f}' for r in aspect_ratios]}")
            else:
                st.success("✅ **Isotropic voxels** - uniform spacing")
            
            # Create and display MRI visualization
            st.markdown("---")
            st.subheader("🔍 High-Resolution MRI Display")
            st.info("🎯 **200 DPI display with proper aspect ratios and anatomical orientations**")
            
            try:
                fig = create_mri_display(data, voxel_sizes, "Aspect Ratio Preserved MRI")
                st.pyplot(fig, use_container_width=True)
                plt.close(fig)
                
                st.success("✅ **Display successful!**")
                
                # Show the fixes applied
                st.markdown("""
                ### 🎯 **Fixes Applied:**
                - ✅ **No stretching** - Original aspect ratios preserved using voxel spacing
                - ✅ **High resolution** - 200 DPI display for clear viewing
                - ✅ **Proper orientations** - Bottom→Top, Left→Right, Anterior→Posterior
                - ✅ **Clinical windowing** - Optimal brain tissue contrast
                - ✅ **Real dimensions** - Displayed in millimeters
                """)
                
            except Exception as e:
                st.error(f"❌ Error creating display: {e}")
                st.error("Please try uploading a different file or check the file format.")
        else:
            st.error("❌ Failed to load MRI data. Please check the file format.")
    
    else:
        # Show sample information
        st.markdown("---")
        st.markdown("### 📋 **Available Test Files:**")
        st.markdown("""
        You can test with these sample files from the repository:
        - `windows_complete_mri_test_collection/real_T1_NO_LABEL_scan_1.nii`
        - `additional_normal_scans/T1_NORMAL_clinical_case1.nii`
        - `demo_data/demo_sample.npy`
        """)
        
        st.markdown("### 🔧 **What This Demo Fixes:**")
        st.markdown("""
        1. **🚨 Stretching Issue**: Original MRI scans were being distorted when resized for AI processing
        2. **📺 Low Resolution**: Images were displayed at low resolution, unsuitable for medical viewing
        3. **🧭 Wrong Orientations**: Images weren't displayed in proper anatomical orientations
        
        **✅ Solution**: Extract original voxel spacing from NIfTI headers and preserve aspect ratios during display
        """)

if __name__ == "__main__":
    main()
