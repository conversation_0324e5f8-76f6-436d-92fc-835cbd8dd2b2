"""
Enhanced MRI Frontend with MCI Support - Integration Demo
Demonstrates the complete pipeline with continuous atrophy scoring and comprehensive interpretability
"""

import streamlit as st
import numpy as np
import torch
from pathlib import Path
import tempfile
import os
from datetime import datetime

# Import our enhanced modules
from enhanced_preprocessing import EnhancedMRIPreprocessor
from enhanced_ncomms2022_model import EnhancedNCOMMs2022Model, EnhancedModelManager
from comprehensive_interpretability import ComprehensiveInterpretabilityEngine, display_comprehensive_heatmap
from clinical_features_reporting import ClinicalFeatureExtractor, EnhancedPDFReportGenerator

# Page configuration
st.set_page_config(
    page_title="Demetify Enhanced - MCI Support",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #2E86AB;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .enhancement-badge {
        background-color: #28a745;
        color: white;
        padding: 0.2rem 0.5rem;
        border-radius: 0.3rem;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .mci-highlight {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header with enhancement badge
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="font-size: 3rem; color: #2E86AB; margin-bottom: 0.5rem; font-weight: bold;">
            🧠 Demetify Enhanced
        </h1>
        <span class="enhancement-badge">MCI SUPPORT ENABLED</span>
        <h2 style="font-size: 1.5rem; color: #A23B72; margin-top: 1rem; font-weight: normal;">
            AI-Powered Radiologist Assistant with Continuous Atrophy Scoring
        </h2>
        <div class="mci-highlight">
            <strong>🎯 New Features:</strong> CN/MCI/AD Classification • Continuous Atrophy Scoring • 
            Enhanced Preprocessing • Unified Heatmaps • Clinical Feature Extraction
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize session state
    if 'enhanced_preprocessor' not in st.session_state:
        st.session_state.enhanced_preprocessor = EnhancedMRIPreprocessor()
    if 'enhanced_model_manager' not in st.session_state:
        st.session_state.enhanced_model_manager = EnhancedModelManager()
    if 'enhanced_model' not in st.session_state:
        st.session_state.enhanced_model = None
    if 'interpretability_engine' not in st.session_state:
        st.session_state.interpretability_engine = ComprehensiveInterpretabilityEngine()
    if 'feature_extractor' not in st.session_state:
        st.session_state.feature_extractor = ClinicalFeatureExtractor()
    if 'pdf_generator' not in st.session_state:
        st.session_state.pdf_generator = EnhancedPDFReportGenerator()
    
    # Sidebar for enhanced model configuration
    with st.sidebar:
        st.markdown('<h2 style="color: #2E86AB;">⚙️ Enhanced Model Configuration</h2>', unsafe_allow_html=True)
        
        # Model selection
        available_models = st.session_state.enhanced_model_manager.get_available_models()
        
        if not available_models:
            st.error("❌ No enhanced models found")
            st.info("💡 Using fallback configuration for demonstration")
            available_models = ["Enhanced_Demo_Model"]
        
        selected_model = st.selectbox(
            "Select Enhanced Model:",
            available_models,
            help="Enhanced models support MCI classification and continuous scoring"
        )
        
        # Device selection
        device_options = ['cpu']
        if torch.cuda.is_available():
            device_options.append('cuda')
        
        selected_device = st.selectbox("Compute Device:", device_options)
        
        # Load enhanced model
        if st.button("🔄 Load Enhanced Model", type="primary"):
            with st.spinner("Loading enhanced model..."):
                st.session_state.enhanced_model = st.session_state.enhanced_model_manager.load_model(
                    selected_model, device=selected_device
                )
        
        # Model status
        if st.session_state.enhanced_model:
            st.success("✅ Enhanced model loaded!")
            
            with st.expander("📊 Enhanced Model Info"):
                model_info = st.session_state.enhanced_model.get_model_info()
                for key, value in model_info.items():
                    if isinstance(value, list):
                        st.write(f"**{key}:**")
                        for item in value:
                            st.write(f"  • {item}")
                    else:
                        st.write(f"**{key}:** {value}")
        else:
            st.warning("⚠️ No enhanced model loaded")
        
        st.markdown("---")
        
        # Enhanced preprocessing options
        st.markdown('<h3 style="color: #A23B72;">🔧 Enhanced Preprocessing</h3>', unsafe_allow_html=True)
        
        apply_skull_stripping = st.checkbox(
            "Advanced Skull Stripping",
            value=True,
            help="Multi-step morphological brain extraction with border detection"
        )
        
        apply_bias_correction = st.checkbox(
            "Bias Field Correction",
            value=True,
            help="N4-like bias field correction for intensity uniformity"
        )
        
        apply_normalization = st.checkbox(
            "Clinical Normalization",
            value=True,
            help="Brain tissue-based intensity normalization"
        )
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown('<h2 style="color: #2E86AB;">📁 Enhanced MRI Processing</h2>', unsafe_allow_html=True)
        
        # File upload
        uploaded_file = st.file_uploader(
            "Upload MRI Scan",
            type=['nii', 'npy'],
            help="Supports .nii (NIfTI) and .npy formats with enhanced preprocessing"
        )
        
        if uploaded_file is not None:
            # Store filename
            st.session_state.uploaded_filename = Path(uploaded_file.name).stem
            
            # Display file info
            file_details = {
                "Filename": uploaded_file.name,
                "Size": f"{uploaded_file.size / 1024 / 1024:.2f} MB",
                "Type": uploaded_file.type or "Unknown"
            }
            
            st.info("📋 **File Information:**")
            for key, value in file_details.items():
                st.write(f"• **{key}:** {value}")
            
            # Enhanced preprocessing
            if st.button("🚀 Enhanced Preprocessing", type="primary"):
                with st.spinner("Running enhanced preprocessing pipeline..."):
                    file_type = 'nii' if uploaded_file.name.endswith('.nii') else 'npy'
                    
                    # Reset file pointer
                    uploaded_file.seek(0)
                    
                    # Enhanced preprocessing
                    preprocessed_data, metadata = st.session_state.enhanced_preprocessor.preprocess_mri_enhanced(
                        uploaded_file,
                        file_type=file_type,
                        apply_skull_stripping=apply_skull_stripping,
                        apply_bias_correction=apply_bias_correction,
                        apply_normalization=apply_normalization
                    )
                    
                    if preprocessed_data is not None:
                        st.session_state.preprocessed_data = preprocessed_data
                        st.session_state.preprocessing_metadata = metadata
                        
                        # Validation
                        if st.session_state.enhanced_preprocessor.validate_preprocessed_data(preprocessed_data):
                            st.success("🎉 Enhanced preprocessing completed successfully!")
                            
                            # Display enhanced preprocessing summary
                            st.info("📊 **Enhanced Preprocessing Summary:**")
                            st.write(f"• **Final shape:** {preprocessed_data.shape}")
                            st.write(f"• **Data type:** {preprocessed_data.dtype}")
                            st.write(f"• **Value range:** [{preprocessed_data.min():.3f}, {preprocessed_data.max():.3f}]")
                            st.write(f"• **Processing steps:** {len(metadata.get('preprocessing_steps', []))}")
                            
                            # Show processing log
                            with st.expander("📝 Processing Log"):
                                for step in metadata.get('preprocessing_steps', []):
                                    st.write(f"• {step}")
                        else:
                            st.error("❌ Enhanced preprocessing validation failed")
                    else:
                        st.error("❌ Enhanced preprocessing failed")
    
    with col2:
        st.markdown('<h2 style="color: #2E86AB;">🔮 Enhanced AI Analysis</h2>', unsafe_allow_html=True)
        
        # Enhanced prediction
        if st.session_state.enhanced_model and hasattr(st.session_state, 'preprocessed_data'):
            if st.button("🧠 Run Enhanced Dementia Assessment", type="primary"):
                with st.spinner("Running enhanced AI analysis..."):
                    # Enhanced prediction
                    predictions = st.session_state.enhanced_model.predict_single(
                        st.session_state.preprocessed_data
                    )
                    
                    if predictions:
                        st.session_state.enhanced_predictions = predictions
                        st.success("🎉 Enhanced analysis completed!")
                        
                        # Display enhanced results
                        display_enhanced_predictions(predictions)
                    else:
                        st.error("❌ Enhanced prediction failed")
        
        elif not st.session_state.enhanced_model:
            st.warning("⚠️ Please load an enhanced model first")
        elif not hasattr(st.session_state, 'preprocessed_data'):
            st.warning("⚠️ Please upload and preprocess an MRI scan first")
    
    # Enhanced interpretability section
    if hasattr(st.session_state, 'enhanced_predictions'):
        st.markdown("---")
        st.markdown('<h2 style="color: #2E86AB;">🔍 Comprehensive Interpretability</h2>', unsafe_allow_html=True)
        
        if st.button("🧠 Generate Comprehensive Heatmap", type="primary"):
            with st.spinner("Generating comprehensive atrophy analysis..."):
                # Generate comprehensive interpretability
                interpretability_result = st.session_state.interpretability_engine.generate_comprehensive_heatmap(
                    st.session_state.enhanced_model.model,
                    st.session_state.preprocessed_data,
                    st.session_state.enhanced_predictions
                )
                
                if interpretability_result:
                    st.session_state.interpretability_result = interpretability_result
                    
                    # Display comprehensive heatmap
                    display_comprehensive_heatmap(interpretability_result)
                    
                    # Extract clinical features
                    with st.spinner("Extracting clinical features..."):
                        brain_mask = st.session_state.preprocessing_metadata.get('brain_mask')
                        clinical_features = st.session_state.feature_extractor.extract_comprehensive_features(
                            st.session_state.preprocessed_data,
                            brain_mask,
                            st.session_state.enhanced_predictions,
                            st.session_state.preprocessing_metadata
                        )
                        st.session_state.clinical_features = clinical_features
                    
                    # Generate enhanced PDF report
                    if st.button("📄 Generate Enhanced Clinical Report", type="primary"):
                        with st.spinner("Generating comprehensive clinical report..."):
                            pdf_bytes = st.session_state.pdf_generator.generate_comprehensive_report(
                                st.session_state.preprocessed_data,
                                st.session_state.enhanced_predictions,
                                st.session_state.clinical_features,
                                interpretability_result,
                                st.session_state.preprocessing_metadata,
                                st.session_state.uploaded_filename
                            )
                            
                            if pdf_bytes:
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                filename = f"Demetify_Enhanced_Report_{st.session_state.uploaded_filename}_{timestamp}.pdf"
                                
                                st.success(f"✅ Enhanced clinical report generated! ({len(pdf_bytes):,} bytes)")
                                
                                st.download_button(
                                    label="📄 Download Enhanced Clinical Report",
                                    data=pdf_bytes,
                                    file_name=filename,
                                    mime="application/pdf",
                                    type="primary",
                                    use_container_width=True
                                )
                                st.info(f"📁 Filename: {filename}")
                            else:
                                st.error("❌ Enhanced report generation failed")
                else:
                    st.error("❌ Comprehensive interpretability generation failed")

def display_enhanced_predictions(predictions: dict):
    """Display enhanced prediction results with MCI support"""
    st.markdown("### 🎯 Enhanced Prediction Results")
    
    # Main classification result
    predicted_class = predictions.get('predicted_class', 'Unknown')
    confidence = predictions.get('confidence', 0.0)
    atrophy_score = predictions.get('atrophy_score', 0.0)
    
    # Color coding for classes
    class_colors = {
        'CN': 'green',
        'MCI': 'orange', 
        'AD': 'red'
    }
    
    color = class_colors.get(predicted_class, 'gray')
    
    st.markdown(f"""
    <div style="background-color: {color}; color: white; padding: 1rem; border-radius: 0.5rem; text-align: center; margin: 1rem 0;">
        <h3>Classification: {predicted_class}</h3>
        <p>Confidence: {confidence:.1%} | Atrophy Score: {atrophy_score:.3f}</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Class probabilities
    if 'class_probabilities' in predictions:
        st.markdown("#### 📊 Class Probabilities")
        probs = predictions['class_probabilities']
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Normal Cognition", f"{probs.get('CN', 0):.1%}")
        with col2:
            st.metric("Mild Cognitive Impairment", f"{probs.get('MCI', 0):.1%}")
        with col3:
            st.metric("Alzheimer's Disease", f"{probs.get('AD', 0):.1%}")
    
    # Continuous atrophy score interpretation
    st.markdown("#### 🧠 Atrophy Severity Analysis")
    
    if atrophy_score < 0.33:
        severity = "Minimal"
        interpretation = "Brain structure within normal limits for age"
        color = "lightgreen"
    elif atrophy_score < 0.67:
        severity = "Moderate"
        interpretation = "Intermediate atrophy consistent with MCI"
        color = "lightyellow"
    else:
        severity = "Significant"
        interpretation = "Substantial atrophy consistent with AD"
        color = "lightcoral"
    
    st.markdown(f"""
    <div style="background-color: {color}; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
        <strong>Severity:</strong> {severity} (Score: {atrophy_score:.3f})<br>
        <strong>Interpretation:</strong> {interpretation}
    </div>
    """, unsafe_allow_html=True)
    
    # Regional atrophy if available
    if 'regional_atrophy' in predictions:
        st.markdown("#### 🗺️ Regional Atrophy Scores")
        regional = predictions['regional_atrophy']
        
        for region, score in regional.items():
            region_name = region.replace('_', ' ').title()
            st.write(f"• **{region_name}:** {score:.3f}")
    
    # Clinical scores if available
    if 'clinical_scores' in predictions:
        st.markdown("#### 🏥 Automated Clinical Scores")
        clinical = predictions['clinical_scores']
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("MTA Score", f"{clinical.get('MTA_score', 0):.1f}/4.0")
        with col2:
            st.metric("GCA Score", f"{clinical.get('GCA_score', 0):.1f}/3.0")
        with col3:
            st.metric("Koedam Score", f"{clinical.get('Koedam_score', 0):.1f}/3.0")

if __name__ == "__main__":
    main()
