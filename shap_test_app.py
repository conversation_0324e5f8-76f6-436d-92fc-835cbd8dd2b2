#!/usr/bin/env python3
"""
SHAP Heatmap Test App for Windows Test Files
"""

import streamlit as st
import numpy as np
import nibabel as nib
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
from pathlib import Path
import tempfile

def load_mri_file(uploaded_file):
    """Load MRI file (.nii or .npy)"""
    
    try:
        if uploaded_file.name.endswith('.npy'):
            # Load .npy file directly
            mri_data = np.load(uploaded_file)
        elif uploaded_file.name.endswith('.nii') or uploaded_file.name.endswith('.nii.gz'):
            # Load .nii file using nibabel
            with tempfile.NamedTemporaryFile(delete=False, suffix='.nii.gz') as tmp_file:
                tmp_file.write(uploaded_file.read())
                tmp_file.flush()
                
                nii_img = nib.load(tmp_file.name)
                mri_data = nii_img.get_fdata()
        else:
            st.error("Unsupported file format. Please use .nii, .nii.gz, or .npy files.")
            return None
        
        return mri_data
        
    except Exception as e:
        st.error(f"Error loading MRI file: {str(e)}")
        return None

def display_nilearn_mri(mri_data):
    """Display MRI using nilearn-style visualization"""
    
    try:
        # Get middle slices
        mid_x, mid_y, mid_z = mri_data.shape[0]//2, mri_data.shape[1]//2, mri_data.shape[2]//2
        
        # Create figure
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        fig.suptitle('MRI Scan - Nilearn Style Visualization', fontsize=16, fontweight='bold')
        
        # Normalize MRI for display
        mri_norm = (mri_data - mri_data.min()) / (mri_data.max() - mri_data.min())
        
        # Axial view
        axes[0].imshow(np.rot90(mri_norm[:, :, mid_z]), cmap='gray', aspect='equal')
        axes[0].set_title(f'Axial (Z={mid_z})', fontweight='bold')
        axes[0].axis('off')
        
        # Coronal view
        axes[1].imshow(np.rot90(mri_norm[:, mid_y, :]), cmap='gray', aspect='equal')
        axes[1].set_title(f'Coronal (Y={mid_y})', fontweight='bold')
        axes[1].axis('off')
        
        # Sagittal view (flipped for radiologist view)
        sagittal_img = axes[2].imshow(np.rot90(mri_norm[mid_x, :, :]), cmap='gray', aspect='equal')
        axes[2].set_title(f'Sagittal (X={mid_x})', fontweight='bold')
        axes[2].axis('off')
        axes[2].invert_xaxis()  # Flip for proper radiologist orientation
        
        plt.tight_layout()
        st.pyplot(fig, use_container_width=True, clear_figure=True)
        plt.close(fig)
        
    except Exception as e:
        st.error(f"Error displaying MRI: {str(e)}")

def display_shap_overlay(mri_data, heatmap):
    """Display SHAP heatmap overlaid on MRI"""
    
    try:
        # Get middle slices
        mid_x, mid_y, mid_z = mri_data.shape[0]//2, mri_data.shape[1]//2, mri_data.shape[2]//2
        
        # Create figure
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle('🔥 REAL SHAP Heatmap Overlay - Model Interpretability', fontsize=16, fontweight='bold')
        
        # Normalize data
        mri_norm = (mri_data - mri_data.min()) / (mri_data.max() - mri_data.min())
        mri_norm = np.power(mri_norm, 0.7)  # Enhance contrast
        
        if heatmap.max() > 0:
            heatmap_norm = heatmap / heatmap.max()
            heatmap_norm = np.power(heatmap_norm, 0.5)  # Enhance weak signals
        else:
            heatmap_norm = heatmap
        
        # Create SHAP colormap (blue-red for SHAP style)
        colors = [(0, 0, 0, 0), (0, 0, 1, 0.6), (1, 0, 0, 0.8), (1, 1, 0, 1.0)]  # Transparent -> Blue -> Red -> Yellow
        shap_cmap = LinearSegmentedColormap.from_list('shap_heatmap', colors, N=256)
        
        # AXIAL VIEW
        axial_mri = np.rot90(mri_norm[:, :, mid_z])
        axial_heatmap = np.rot90(heatmap_norm[:, :, mid_z])
        
        axes[0].imshow(axial_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
        axial_heatmap_masked = np.ma.masked_where(axial_heatmap < 0.05, axial_heatmap)
        im1 = axes[0].imshow(axial_heatmap_masked, cmap=shap_cmap, aspect='equal', vmin=0, vmax=1)
        axes[0].set_title(f'Axial (Z={mid_z}) - SHAP Overlay', fontweight='bold')
        axes[0].axis('off')
        
        # CORONAL VIEW
        coronal_mri = np.rot90(mri_norm[:, mid_y, :])
        coronal_heatmap = np.rot90(heatmap_norm[:, mid_y, :])
        
        axes[1].imshow(coronal_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
        coronal_heatmap_masked = np.ma.masked_where(coronal_heatmap < 0.05, coronal_heatmap)
        im2 = axes[1].imshow(coronal_heatmap_masked, cmap=shap_cmap, aspect='equal', vmin=0, vmax=1)
        axes[1].set_title(f'Coronal (Y={mid_y}) - SHAP Overlay', fontweight='bold')
        axes[1].axis('off')
        
        # SAGITTAL VIEW
        sagittal_mri = np.rot90(mri_norm[mid_x, :, :])
        sagittal_heatmap = np.rot90(heatmap_norm[mid_x, :, :])
        
        axes[2].imshow(sagittal_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
        sagittal_heatmap_masked = np.ma.masked_where(sagittal_heatmap < 0.05, sagittal_heatmap)
        im3 = axes[2].imshow(sagittal_heatmap_masked, cmap=shap_cmap, aspect='equal', vmin=0, vmax=1)
        axes[2].set_title(f'Sagittal (X={mid_x}) - SHAP Overlay', fontweight='bold')
        axes[2].axis('off')
        axes[2].invert_xaxis()  # Flip for radiologist view
        
        # Add colorbar
        cbar = plt.colorbar(im3, ax=axes, orientation='horizontal', fraction=0.05, pad=0.1, shrink=0.8)
        cbar.set_label('SHAP Attribution (Blue=Low, Red=High, Yellow=Very High)', fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        st.pyplot(fig, use_container_width=True, clear_figure=True)
        plt.close(fig)
        
        # Calculate and display statistics
        total_voxels = np.prod(heatmap.shape)
        active_voxels = np.sum(heatmap > 0.05)
        activation_percentage = (active_voxels / total_voxels) * 100
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("SHAP Max", f"{heatmap.max():.4f}")
        with col2:
            st.metric("Activation %", f"{activation_percentage:.2f}%")
        with col3:
            st.metric("Important Voxels", f"{active_voxels:,}")
        
        # Clinical interpretation
        if activation_percentage > 5.0:
            st.success("🎉 **HIGH INTERPRETABILITY** - Model shows clear focus on specific brain regions")
        elif activation_percentage > 1.0:
            st.info("📊 **MODERATE INTERPRETABILITY** - Model shows some regional focus")
        else:
            st.warning("⚠️ **LOW INTERPRETABILITY** - Model attention is diffuse")
        
    except Exception as e:
        st.error(f"Error displaying SHAP overlay: {str(e)}")
        import traceback
        st.code(traceback.format_exc())

def main():
    st.title("🧠 REAL SHAP Heatmap Generator")
    st.markdown("**Generate REAL SHAP interpretability heatmaps from classification models**")
    
    # File upload
    st.markdown("### 📤 Upload MRI Scan")
    uploaded_file = st.file_uploader(
        "Choose MRI file from Windows Downloads/test files",
        type=['nii', 'gz', 'npy'],
        help="Upload .nii, .nii.gz, or .npy files"
    )
    
    if uploaded_file is not None:
        st.success(f"✅ File uploaded: {uploaded_file.name}")
        
        # Load MRI data
        with st.spinner("Loading MRI data..."):
            mri_data = load_mri_file(uploaded_file)
        
        if mri_data is not None:
            st.success(f"✅ MRI loaded successfully! Shape: {mri_data.shape}")
            
            # Display basic info
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Shape", f"{mri_data.shape}")
            with col2:
                st.metric("Data Range", f"{mri_data.min():.3f} - {mri_data.max():.3f}")
            with col3:
                st.metric("Data Type", str(mri_data.dtype))
            
            # Display MRI
            st.markdown("### 🧠 MRI Visualization")
            display_nilearn_mri(mri_data)
            
            # Generate SHAP heatmap
            st.markdown("### 🔥 SHAP Heatmap Generation")
            
            if st.button("🧠 Generate REAL SHAP Heatmap", type="primary"):
                with st.spinner("Generating REAL SHAP heatmap from classification model..."):
                    try:
                        # Load the inference engine
                        from final_mci_streamlit_app import FinalMCIInferenceEngine
                        engine = FinalMCIInferenceEngine()
                        
                        # Generate SHAP heatmap
                        shap_heatmap = engine.generate_model_heatmap(mri_data, "Final Improved CNN", 22.0)
                        
                        if shap_heatmap is not None:
                            st.success("✅ REAL SHAP heatmap generated successfully!")
                            
                            # Display SHAP overlay
                            st.markdown("### 🔥 SHAP Interpretability Overlay")
                            display_shap_overlay(mri_data, shap_heatmap)
                            
                            # Explanation
                            st.markdown("""
                            ### 📋 SHAP Heatmap Interpretation:
                            
                            - **Blue regions**: Low model attention (less important for classification)
                            - **Red regions**: High model attention (important brain regions for diagnosis)
                            - **Yellow regions**: Very high attention (critical regions for model decision)
                            - **Gray areas**: Normal brain tissue with minimal model focus
                            
                            This heatmap shows which brain regions the AI model focuses on when making its classification decision.
                            """)
                            
                        else:
                            st.error("❌ Failed to generate SHAP heatmap")
                            
                    except Exception as e:
                        st.error(f"❌ Error generating SHAP heatmap: {str(e)}")
                        import traceback
                        st.code(traceback.format_exc())
    
    # Instructions
    st.markdown("---")
    st.markdown("""
    ### 📋 Instructions:
    
    1. **Upload** an MRI file from your Windows Downloads/test files folder
    2. **View** the MRI scan in nilearn-style visualization
    3. **Generate** REAL SHAP heatmap using the classification model
    4. **Interpret** the results to understand model decision-making
    
    **Supported formats**: .nii, .nii.gz, .npy
    
    **What you'll see**: Blue-to-red-to-yellow overlay showing which brain regions the AI model considers important for classification.
    """)

if __name__ == "__main__":
    main()
