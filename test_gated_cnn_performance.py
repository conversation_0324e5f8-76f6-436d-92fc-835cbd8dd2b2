#!/usr/bin/env python3
"""
Test Gated CNN Performance - Comprehensive evaluation
"""

import numpy as np
import torch
import time
import json
from pathlib import Path
import matplotlib.pyplot as plt
from gated_cnn_model import GatedCNNModel, mmse_to_class_probs_corrected

def create_test_mri_cases():
    """Create test MRI cases with known characteristics"""
    
    test_cases = []
    
    # Case 1: Healthy brain (high tissue quality)
    healthy_mri = np.random.normal(0.7, 0.1, (91, 109, 91))
    # Add healthy tissue structure
    center = (45, 54, 45)
    for i in range(center[0]-15, center[0]+15):
        for j in range(center[1]-20, center[1]+20):
            for k in range(center[2]-15, center[2]+15):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    healthy_mri[i, j, k] += np.random.normal(0.1, 0.03)
    
    test_cases.append({
        'name': 'Healthy Brain',
        'mri': np.clip(healthy_mri, 0, 1),
        'expected_class': 'CN',
        'expected_mmse_range': (26, 30)
    })
    
    # Case 2: MCI brain (moderate tissue quality)
    mci_mri = np.random.normal(0.5, 0.12, (91, 109, 91))
    # Add mild atrophy
    for i in range(center[0]-10, center[0]+10):
        for j in range(center[1]-15, center[1]+15):
            for k in range(center[2]-10, center[2]+10):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    mci_mri[i, j, k] *= np.random.uniform(0.8, 0.95)
    
    test_cases.append({
        'name': 'MCI Brain',
        'mri': np.clip(mci_mri, 0, 1),
        'expected_class': 'MCI',
        'expected_mmse_range': (20, 25)
    })
    
    # Case 3: AD brain (low tissue quality)
    ad_mri = np.random.normal(0.3, 0.15, (91, 109, 91))
    # Add severe atrophy
    for i in range(center[0]-15, center[0]+15):
        for j in range(center[1]-20, center[1]+20):
            for k in range(center[2]-15, center[2]+15):
                if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                    ad_mri[i, j, k] *= np.random.uniform(0.4, 0.7)
    
    test_cases.append({
        'name': 'AD Brain',
        'mri': np.clip(ad_mri, 0, 1),
        'expected_class': 'AD',
        'expected_mmse_range': (8, 19)
    })
    
    return test_cases

def test_model_predictions(model, test_cases):
    """Test model predictions on various cases"""
    
    print("🧪 Testing Model Predictions...")
    print("=" * 50)
    
    results = []
    
    for case in test_cases:
        print(f"\n📊 Testing: {case['name']}")
        
        # Prepare input
        mri_tensor = torch.FloatTensor(case['mri']).unsqueeze(0).unsqueeze(0)
        
        # Predict
        start_time = time.time()
        with torch.no_grad():
            outputs = model(mri_tensor)
            
        inference_time = time.time() - start_time
        
        # Extract results
        mmse_score = outputs['mmse_score'].item()
        class_probs = mmse_to_class_probs_corrected(mmse_score)
        predicted_class = np.argmax(class_probs)
        class_names = ['CN', 'MCI', 'AD']
        
        # Check if prediction is correct
        expected_class_idx = class_names.index(case['expected_class'])
        is_correct = predicted_class == expected_class_idx
        
        # Check MMSE range
        mmse_in_range = case['expected_mmse_range'][0] <= mmse_score <= case['expected_mmse_range'][1]
        
        result = {
            'case_name': case['name'],
            'expected_class': case['expected_class'],
            'predicted_class': class_names[predicted_class],
            'mmse_score': mmse_score,
            'expected_mmse_range': case['expected_mmse_range'],
            'class_probabilities': {
                'CN': class_probs[0],
                'MCI': class_probs[1], 
                'AD': class_probs[2]
            },
            'inference_time': inference_time,
            'classification_correct': is_correct,
            'mmse_in_range': mmse_in_range,
            'overall_correct': is_correct and mmse_in_range
        }
        
        results.append(result)
        
        # Print results
        print(f"   Expected: {case['expected_class']} (MMSE: {case['expected_mmse_range'][0]}-{case['expected_mmse_range'][1]})")
        print(f"   Predicted: {class_names[predicted_class]} (MMSE: {mmse_score:.1f})")
        print(f"   Probabilities: CN={class_probs[0]:.3f}, MCI={class_probs[1]:.3f}, AD={class_probs[2]:.3f}")
        print(f"   Inference Time: {inference_time:.3f}s")
        print(f"   ✅ Correct: {is_correct}, MMSE in range: {mmse_in_range}")
    
    return results

def test_gradient_quality(model, test_cases):
    """Test gradient quality for heatmap generation"""
    
    print("\n🔥 Testing Gradient Quality...")
    print("=" * 40)
    
    gradient_results = []
    
    for case in test_cases:
        print(f"\n📈 Gradient test: {case['name']}")
        
        # Prepare input with gradients
        mri_tensor = torch.FloatTensor(case['mri']).unsqueeze(0).unsqueeze(0)
        mri_tensor.requires_grad_(True)
        
        # Forward pass
        outputs = model(mri_tensor)
        loss = outputs['cognitive_score'].mean()
        
        # Backward pass
        loss.backward()
        
        # Check gradients
        gradients = mri_tensor.grad
        if gradients is not None:
            grad_magnitude = torch.abs(gradients).cpu().numpy()
            
            gradient_result = {
                'case_name': case['name'],
                'gradient_min': float(grad_magnitude.min()),
                'gradient_max': float(grad_magnitude.max()),
                'gradient_mean': float(grad_magnitude.mean()),
                'non_zero_gradients': int(np.count_nonzero(grad_magnitude)),
                'gradient_quality': 'Good' if grad_magnitude.max() > 1e-6 else 'Poor'
            }
            
            gradient_results.append(gradient_result)
            
            print(f"   Gradient range: {grad_magnitude.min():.8f} - {grad_magnitude.max():.8f}")
            print(f"   Non-zero gradients: {np.count_nonzero(grad_magnitude)}")
            print(f"   Quality: {gradient_result['gradient_quality']}")
        else:
            print("   ❌ No gradients computed")
    
    return gradient_results

def test_consistency(model, num_tests=10):
    """Test model consistency across multiple runs"""
    
    print("\n🔄 Testing Model Consistency...")
    print("=" * 35)
    
    # Create a standard test case
    test_mri = np.random.normal(0.5, 0.1, (91, 109, 91))
    mri_tensor = torch.FloatTensor(test_mri).unsqueeze(0).unsqueeze(0)
    
    predictions = []
    
    for i in range(num_tests):
        with torch.no_grad():
            outputs = model(mri_tensor)
            mmse_score = outputs['mmse_score'].item()
            class_probs = mmse_to_class_probs_corrected(mmse_score)
            predicted_class = np.argmax(class_probs)
            
            predictions.append({
                'run': i+1,
                'mmse_score': mmse_score,
                'predicted_class': predicted_class,
                'class_probs': class_probs.copy()
            })
    
    # Analyze consistency
    mmse_scores = [p['mmse_score'] for p in predictions]
    predicted_classes = [p['predicted_class'] for p in predictions]
    
    mmse_std = np.std(mmse_scores)
    class_consistency = len(set(predicted_classes)) == 1
    
    print(f"   MMSE Score Range: {min(mmse_scores):.2f} - {max(mmse_scores):.2f}")
    print(f"   MMSE Standard Deviation: {mmse_std:.4f}")
    print(f"   Class Consistency: {'✅ Consistent' if class_consistency else '❌ Inconsistent'}")
    print(f"   Classes predicted: {set(predicted_classes)}")
    
    return {
        'mmse_std': mmse_std,
        'class_consistency': class_consistency,
        'predictions': predictions
    }

def generate_performance_report(prediction_results, gradient_results, consistency_results):
    """Generate comprehensive performance report"""
    
    print("\n📋 PERFORMANCE REPORT")
    print("=" * 60)
    
    # Classification accuracy
    correct_classifications = sum(1 for r in prediction_results if r['classification_correct'])
    mmse_accuracy = sum(1 for r in prediction_results if r['mmse_in_range'])
    overall_accuracy = sum(1 for r in prediction_results if r['overall_correct'])
    
    print(f"🎯 Classification Accuracy: {correct_classifications}/{len(prediction_results)} ({correct_classifications/len(prediction_results)*100:.1f}%)")
    print(f"📊 MMSE Range Accuracy: {mmse_accuracy}/{len(prediction_results)} ({mmse_accuracy/len(prediction_results)*100:.1f}%)")
    print(f"🏆 Overall Accuracy: {overall_accuracy}/{len(prediction_results)} ({overall_accuracy/len(prediction_results)*100:.1f}%)")
    
    # Gradient quality
    good_gradients = sum(1 for r in gradient_results if r['gradient_quality'] == 'Good')
    print(f"🔥 Gradient Quality: {good_gradients}/{len(gradient_results)} cases have good gradients")
    
    # Consistency
    print(f"🔄 Model Consistency: MMSE std = {consistency_results['mmse_std']:.4f}")
    print(f"🎲 Class Consistency: {'✅ Stable' if consistency_results['class_consistency'] else '❌ Unstable'}")
    
    # Performance summary
    avg_inference_time = np.mean([r['inference_time'] for r in prediction_results])
    print(f"⚡ Average Inference Time: {avg_inference_time:.3f}s")
    
    # Overall grade
    score = (correct_classifications + mmse_accuracy + good_gradients) / (len(prediction_results) * 3) * 100
    
    if score >= 90:
        grade = "A+ (Excellent)"
    elif score >= 80:
        grade = "A (Very Good)"
    elif score >= 70:
        grade = "B (Good)"
    elif score >= 60:
        grade = "C (Fair)"
    else:
        grade = "D (Needs Improvement)"
    
    print(f"\n🏅 OVERALL PERFORMANCE: {score:.1f}% - {grade}")
    
    return {
        'classification_accuracy': correct_classifications/len(prediction_results),
        'mmse_accuracy': mmse_accuracy/len(prediction_results),
        'overall_accuracy': overall_accuracy/len(prediction_results),
        'gradient_quality': good_gradients/len(gradient_results),
        'consistency_score': 1.0 if consistency_results['class_consistency'] else 0.0,
        'avg_inference_time': avg_inference_time,
        'overall_score': score,
        'grade': grade
    }

def main():
    """Main performance testing function"""
    
    print("🧠 GATED CNN PERFORMANCE TEST")
    print("=" * 70)
    print("Testing the new NACC-trained Gated CNN model architecture")
    print()
    
    # Create model
    print("🔧 Loading Gated CNN model...")
    model = GatedCNNModel()
    model.eval()
    
    # Create test cases
    test_cases = create_test_mri_cases()
    print(f"✅ Created {len(test_cases)} test cases")
    
    # Run tests
    prediction_results = test_model_predictions(model, test_cases)
    gradient_results = test_gradient_quality(model, test_cases)
    consistency_results = test_consistency(model)
    
    # Generate report
    performance_summary = generate_performance_report(prediction_results, gradient_results, consistency_results)
    
    # Save results
    full_results = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'model_architecture': 'GatedCNN',
        'prediction_results': prediction_results,
        'gradient_results': gradient_results,
        'consistency_results': consistency_results,
        'performance_summary': performance_summary
    }
    
    with open('gated_cnn_performance_results.json', 'w') as f:
        json.dump(full_results, f, indent=2)
    
    print(f"\n📁 Results saved to: gated_cnn_performance_results.json")
    print("\n🎉 PERFORMANCE TEST COMPLETE!")

if __name__ == "__main__":
    main()
