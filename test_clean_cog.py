#!/usr/bin/env python3
"""
Test the clean COG score output - just numbers, no interpretation
"""

import sys
sys.path.append('demetify_deployment')
from ncomms2022_model import ModelManager
from ncomms2022_preprocessing import NCOMMs2022Preprocessor

def test_clean_cog_scores():
    """Test the clean COG score system - just normalized numbers."""
    
    print("🔢 Testing Clean COG Score Output")
    print("=" * 50)
    
    try:
        # Load model and preprocessor
        manager = ModelManager()
        model = manager.load_model('CNN_baseline_new_cross0', device='cpu')
        preprocessor = NCOMMs2022Preprocessor()
        
        print("✅ Model and preprocessor loaded")
        
        # Test demo files
        demo_files = [
            ("ncomms2022/demo/mri/demo1.npy", "AD"),
            ("ncomms2022/demo/mri/demo2.npy", "AD"), 
            ("ncomms2022/demo/mri/demo3.npy", "Normal")
        ]
        
        print(f"\n📊 Clean COG Score Results:")
        print(f"{'File':<15} {'COG Score':<12} {'Range':<15} {'Expected Type':<15}")
        print("-" * 60)
        
        for demo_file, expected_type in demo_files:
            try:
                # Preprocess
                processed_data = preprocessor.preprocess_mri(
                    demo_file,
                    file_type='npy',
                    apply_skull_stripping=False,
                    apply_normalization=False
                )
                
                if processed_data is not None:
                    # Get predictions
                    predictions = model.predict_single(processed_data)
                    
                    if predictions and 'COG' in predictions:
                        cog_result = predictions['COG']
                        score = cog_result['score']  # Should be normalized 0-1
                        
                        filename = demo_file.split('/')[-1]
                        
                        # Check if score is in 0-1 range
                        if 0.0 <= score <= 1.0:
                            range_status = "✅ 0-1 range"
                        else:
                            range_status = "❌ Out of range"
                        
                        print(f"{filename:<15} {score:<12.3f} {range_status:<15} {expected_type:<15}")
                        
                        # Show what this means for normal vs AD
                        if expected_type == "Normal":
                            print(f"  📝 Normal case: COG = {score:.3f} (lower is better)")
                        else:
                            print(f"  📝 AD case: COG = {score:.3f} (higher indicates impairment)")
                            
                    else:
                        print(f"{demo_file:<15} ERROR: No COG prediction")
                else:
                    print(f"{demo_file:<15} ERROR: Preprocessing failed")
                    
            except Exception as e:
                print(f"{demo_file:<15} ERROR: {e}")
        
        print(f"\n✅ **Clean COG Score System Summary:**")
        print(f"- COG scores are now normalized to 0-1 range")
        print(f"- No interpretation text, just clean numbers")
        print(f"- Lower scores = better cognitive function")
        print(f"- Higher scores = more cognitive impairment")
        print(f"- Consistent range for all scans")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_expected_ranges():
    """Show what to expect for different scan types."""
    
    print(f"\n📋 **Expected COG Score Ranges:**")
    print("=" * 50)
    
    print(f"""
🧠 **Normal Scans:**
- Expected COG range: 0.0 - 0.3
- Clean number display: "0.123"
- No interpretation text

🔶 **Mild Impairment:**
- Expected COG range: 0.3 - 0.6
- Clean number display: "0.456"
- No interpretation text

🔴 **Significant Impairment:**
- Expected COG range: 0.6 - 1.0
- Clean number display: "0.789"
- No interpretation text

✅ **Benefits of Clean System:**
- Consistent 0-1 range for all scans
- No confusing interpretation text
- Easy to compare across scans
- Professional numerical output
- No threshold calibration issues
""")

def main():
    """Main testing function."""
    
    print("🎯 Clean COG Score Testing")
    print("=" * 60)
    
    # Test the clean system
    success = test_clean_cog_scores()
    
    # Show expected ranges
    show_expected_ranges()
    
    if success:
        print(f"\n🎉 **COG Score System is Now Clean and Simple!**")
        print(f"")
        print(f"✅ Just shows normalized numbers (0-1 range)")
        print(f"✅ No interpretation comments")
        print(f"✅ Consistent range for all scans")
        print(f"✅ Professional output")
        print(f"")
        print(f"🚀 **Ready for your Windows MRI collection!**")
        print(f"Normal scans should show low COG numbers (0.0-0.3)")
        print(f"AD scans should show higher COG numbers (0.6-1.0)")
        
    else:
        print(f"\n❌ **Issues found - check output above**")

if __name__ == "__main__":
    main()
