{"model_type": "FullGatedCNN", "test_accuracy": 0.42857142857142855, "best_val_loss": 1.4924671241215297, "best_val_acc": 0.42857142857142855, "training_time": 574.2680566310883, "epochs_trained": 40, "total_parameters": 9143623, "confusion_matrix": [[120, 0, 0], [40, 0, 0], [120, 0, 0]], "classification_report": {"CN": {"precision": 0.42857142857142855, "recall": 1.0, "f1-score": 0.6, "support": 120.0}, "MCI": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 40.0}, "AD": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 120.0}, "accuracy": 0.42857142857142855, "macro avg": {"precision": 0.14285714285714285, "recall": 0.3333333333333333, "f1-score": 0.19999999999999998, "support": 280.0}, "weighted avg": {"precision": 0.18367346938775508, "recall": 0.42857142857142855, "f1-score": 0.2571428571428571, "support": 280.0}}, "history": {"train_loss": [1.8504936411267234, 1.523211441721235, 1.509959253810701, 1.5147149630955288, 1.516641556648981, 1.510947671390715, 1.4953344776516868, 1.4896393106097268, 1.5098196041016352, 1.475579605783735, 1.4945580334890456, 1.491607996395656, 1.4917115063894362, 1.478663342339652, 1.4805809407007127, 1.453816706793649, 1.4762857153302147, 1.4836031550452822, 1.4617195935476395, 1.450650917916071, 1.462174381528582, 1.4667027586982364, 1.4516458965483165, 1.4810208138965426, 1.4770189001446679, 1.4784626131966, 1.468103701727731, 1.4665886765434628, 1.467890275092352, 1.4996170225597563, 1.4579399381365095, 1.482702626500811, 1.4676403885795957, 1.4630965039843604, 1.46757599512736, 1.4711910815466018, 1.4675488199506488, 1.4614860773086549, 1.458180927094959, 1.462700631504967], "train_acc": [0.3357142857142857, 0.3821428571428571, 0.35595238095238096, 0.3476190476190476, 0.35, 0.37142857142857144, 0.4119047619047619, 0.4035714285714286, 0.39166666666666666, 0.42023809523809524, 0.430952380952381, 0.4297619047619048, 0.444047619047619, 0.42857142857142855, 0.4035714285714286, 0.4261904761904762, 0.4261904761904762, 0.43214285714285716, 0.44642857142857145, 0.42857142857142855, 0.42857142857142855, 0.4095238095238095, 0.41904761904761906, 0.425, 0.44047619047619047, 0.4, 0.4297619047619048, 0.425, 0.4297619047619048, 0.4154761904761905, 0.40714285714285714, 0.40476190476190477, 0.42142857142857143, 0.4297619047619048, 0.41785714285714287, 0.3976190476190476, 0.430952380952381, 0.45357142857142857, 0.4452380952380952, 0.4166666666666667], "val_loss": [1.4924671241215297, 1.5167380946023123, 1.4613829987389702, 1.4479662350245885, 1.5337688684463502, 1.4958585296358382, 1.4630289622715542, 1.4669827938079834, 1.43157046522413, 1.4998087474278041, 1.4279569251196724, 1.4414159638541086, 1.4638179881232125, 1.4772425958088466, 1.4792565720421926, 1.456624926839556, 1.484813438143049, 1.4803445645741053, 1.4473151411328997, 1.5497170414243426, 1.458379098347255, 1.4635624715260096, 1.4711559670312064, 1.4733258077076503, 1.5062465497425623, 1.4779634509767805, 1.5130384411130633, 1.488174898283822, 1.4429357290267943, 1.4859547717230661, 1.4495417288371495, 1.476337262562343, 1.4635285479681832, 1.4563334430967059, 1.464698873247419, 1.4833128758839198, 1.4685010875974382, 1.4842919077192034, 1.4564679282052175, 1.4900476251329695], "val_acc": [0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855, 0.42857142857142855]}}