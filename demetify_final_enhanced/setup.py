"""
Demetify Setup Script
Automated installation and configuration for production deployment
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages from requirements.txt"""
    print("🔧 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def check_system_requirements():
    """Check system requirements and compatibility"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3.8, 0):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Check available memory (basic check)
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 8:
            print(f"⚠️  Warning: Only {memory_gb:.1f}GB RAM available. 8GB+ recommended")
        else:
            print(f"✅ {memory_gb:.1f}GB RAM available")
    except ImportError:
        print("ℹ️  Could not check memory (psutil not available)")
    
    return True

def setup_directories():
    """Create necessary directories"""
    print("📁 Setting up directories...")
    
    directories = [
        "models",
        "uploads", 
        "outputs",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created/verified directory: {directory}")

def create_config():
    """Create configuration file"""
    print("⚙️  Creating configuration...")
    
    config_content = """# Demetify Configuration
# Production settings for clinical deployment

[model]
device = auto  # auto, cpu, cuda
model_path = models/
cache_predictions = true

[interface]
theme = medical
max_file_size = 500MB
enable_fullscreen = true
dpi = 300

[security]
enable_logging = true
log_level = INFO
max_sessions = 10

[performance]
enable_caching = true
memory_limit = 4GB
timeout = 300
"""
    
    with open("config.ini", "w") as f:
        f.write(config_content)
    
    print("✅ Configuration file created")

def main():
    """Main setup function"""
    print("🧠 Demetify Setup - Medical Imaging AI Frontend")
    print("=" * 50)
    
    # Check system requirements
    if not check_system_requirements():
        print("❌ System requirements not met")
        return False
    
    # Setup directories
    setup_directories()
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed during package installation")
        return False
    
    # Create configuration
    create_config()
    
    print("\n" + "=" * 50)
    print("✅ Setup completed successfully!")
    print("\n🚀 To start the application:")
    print("   streamlit run demetify_production.py")
    print("\n📖 For more information, see README.md")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
