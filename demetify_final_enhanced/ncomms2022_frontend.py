"""
Demetify - AI-Powered Radiologist Assistant
Advanced MRI-based dementia assessment tool to accelerate radiological diagnosis.
"""

import streamlit as st
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import tempfile
import os
from pathlib import Path
import torch
from datetime import datetime
import base64
from io import BytesIO

# Import our custom modules
from ncomms2022_preprocessing import NCOMMs2022Preprocessor
from ncomms2022_model import NCOMMs2022Model, ModelManager

# Page configuration
st.set_page_config(
    page_title="Demetify - AI Radiologist Assistant",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for medical theme
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #2E86AB;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #A23B72;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #2E86AB;
        margin: 0.5rem 0;
    }
    .prediction-high {
        background-color: #ffe6e6;
        border-left-color: #dc3545;
    }
    .prediction-normal {
        background-color: #e6f7e6;
        border-left-color: #28a745;
    }
    .stAlert > div {
        padding: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header with branding
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="font-size: 3rem; color: #2E86AB; margin-bottom: 0.5rem; font-weight: bold;">
            🧠 Demetify
        </h1>
        <h2 style="font-size: 1.5rem; color: #A23B72; margin-bottom: 1rem; font-weight: normal;">
            AI-Powered Radiologist Assistant
        </h2>
        <p style="font-size: 1.1rem; color: #666; margin-bottom: 1rem;">
            Accelerating dementia diagnosis through advanced MRI analysis
        </p>
        <div style="display: flex; justify-content: center; align-items: center; gap: 2rem; margin-bottom: 1rem;">
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background-color: #E84A27; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 0.5rem;">
                    <span style="color: white; font-weight: bold; font-size: 24px;">UI</span>
                </div>
                <p style="font-size: 0.9rem; color: #888; margin: 0;">University of Illinois<br>Urbana-Champaign</p>
            </div>
        </div>
        <p style="font-size: 0.9rem; color: #888;">
            <strong>Project Lead:</strong> S. Seshadri | <strong>Purpose:</strong> Radiologist Decision Support
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize session state
    if 'preprocessor' not in st.session_state:
        st.session_state.preprocessor = NCOMMs2022Preprocessor()
    if 'model_manager' not in st.session_state:
        st.session_state.model_manager = ModelManager()
    if 'current_model' not in st.session_state:
        st.session_state.current_model = None
    if 'preprocessed_data' not in st.session_state:
        st.session_state.preprocessed_data = None
    if 'predictions' not in st.session_state:
        st.session_state.predictions = None
    if 'pdf_status' not in st.session_state:
        st.session_state.pdf_status = 'not_started'  # not_started, generating, ready, error
    if 'pdf_data' not in st.session_state:
        st.session_state.pdf_data = None
    if 'pdf_filename' not in st.session_state:
        st.session_state.pdf_filename = None
    
    # Sidebar for model selection and settings
    with st.sidebar:
        st.markdown('<h2 class="sub-header">⚙️ Model Configuration</h2>', unsafe_allow_html=True)
        
        # Model selection
        available_models = st.session_state.model_manager.get_available_models()
        
        if not available_models:
            st.error("❌ No pretrained models found. Please ensure the model files are available.")
            st.info("💡 Expected location: `CNN_baseline_new_cross0/` directory with .pth files")
            return
        
        selected_model = st.selectbox(
            "Select Pretrained Model:",
            available_models,
            help="Choose from available cross-validation folds"
        )
        
        # Device selection
        device_options = ['cpu']
        if torch.cuda.is_available():
            device_options.append('cuda')
        
        selected_device = st.selectbox(
            "Compute Device:",
            device_options,
            help="GPU acceleration requires CUDA-compatible hardware"
        )
        
        # Load model button
        if st.button("🔄 Load Model", type="primary"):
            with st.spinner("Loading model..."):
                st.session_state.current_model = st.session_state.model_manager.load_model(
                    selected_model, 
                    device=selected_device
                )
        
        # Model status
        if st.session_state.current_model:
            st.success("✅ Model loaded successfully!")
            
            # Model info
            with st.expander("📊 Model Information"):
                model_info = st.session_state.current_model.get_model_info()
                for key, value in model_info.items():
                    st.write(f"**{key}:** {value}")
        else:
            st.warning("⚠️ No model loaded")
        
        st.markdown("---")
        
        # Preprocessing options
        st.markdown('<h3 class="sub-header">🔧 Preprocessing Options</h3>', unsafe_allow_html=True)

        apply_skull_stripping = st.checkbox(
            "Apply Skull Stripping",
            value=True,
            help="Remove skull and non-brain tissue (recommended for .nii files)"
        )

        apply_normalization = st.checkbox(
            "Apply Intensity Normalization",
            value=False,
            help="Normalize intensity values to [0,1] range (usually not needed for ncomms2022 model)"
        )
    
    # Main content area - VERTICAL LAYOUT for better MRI visualization
    st.markdown('<h2 class="sub-header">📁 MRI Upload & Preprocessing</h2>', unsafe_allow_html=True)

    # File upload
    uploaded_file = st.file_uploader(
        "Upload MRI Scan",
        type=['nii', 'npy'],
        help="Supported formats: .nii (NIfTI) or .npy (NumPy array)"
    )

    if uploaded_file is not None:
        # Store filename for PDF report
        st.session_state.uploaded_filename = Path(uploaded_file.name).stem

        # Display file info
        file_details = {
            "Filename": uploaded_file.name,
            "File size": f"{uploaded_file.size / 1024 / 1024:.2f} MB",
            "File type": uploaded_file.type or "Unknown"
        }

        st.info("📋 **File Information:**")
        for key, value in file_details.items():
            st.write(f"• **{key}:** {value}")

        # Determine file type
        file_extension = Path(uploaded_file.name).suffix.lower()
        file_type = 'nii' if file_extension in ['.nii', '.nii.gz'] else 'npy'

        # Preprocessing button
        if st.button("🔄 Preprocess MRI", type="primary"):
            with st.spinner("Preprocessing MRI data..."):
                # Reset the file pointer
                uploaded_file.seek(0)

                # Preprocess the data
                preprocessed_data = st.session_state.preprocessor.preprocess_mri(
                    uploaded_file,
                    file_type=file_type,
                    apply_skull_stripping=apply_skull_stripping,
                    apply_normalization=apply_normalization
                )

                if preprocessed_data is not None:
                    # Validate the data
                    if st.session_state.preprocessor.validate_preprocessed_data(preprocessed_data):
                        st.session_state.preprocessed_data = preprocessed_data
                        st.success("🎉 MRI preprocessing completed successfully!")

                        # Display preprocessing summary
                        st.info("📊 **Preprocessing Summary:**")
                        st.write(f"• **Final shape:** {preprocessed_data.shape}")
                        st.write(f"• **Data type:** {preprocessed_data.dtype}")
                        st.write(f"• **Value range:** [{preprocessed_data.min():.3f}, {preprocessed_data.max():.3f}]")
                        st.write(f"• **Mean intensity:** {preprocessed_data.mean():.3f}")
                    else:
                        st.error("❌ Preprocessing validation failed")
                else:
                    st.error("❌ Preprocessing failed")
        
        # Display preprocessing status
        if st.session_state.preprocessed_data is not None:
            st.success("✅ MRI data ready for inference")
            
            # Show sample slices
            with st.expander("👁️ Preview MRI Slices"):
                data = st.session_state.preprocessed_data

                # Show three orthogonal slices
                fig = make_subplots(
                    rows=1, cols=3,
                    subplot_titles=["Axial", "Coronal", "Sagittal"],
                    horizontal_spacing=0.05
                )

                # Axial slice (middle) - maintain original orientation
                axial_slice = data[:, :, data.shape[2]//2]
                fig.add_trace(
                    go.Heatmap(z=axial_slice, colorscale='gray', showscale=True,
                              colorbar=dict(title="Intensity", x=1.02)),
                    row=1, col=1
                )

                # Coronal slice (middle) - maintain original orientation
                coronal_slice = data[:, data.shape[1]//2, :]
                fig.add_trace(
                    go.Heatmap(z=coronal_slice, colorscale='gray', showscale=False),
                    row=1, col=2
                )

                # Sagittal slice (middle) - maintain original orientation
                sagittal_slice = data[data.shape[0]//2, :, :]
                fig.add_trace(
                    go.Heatmap(z=sagittal_slice, colorscale='gray', showscale=False),
                    row=1, col=3
                )

                fig.update_layout(
                    height=300,
                    showlegend=False,
                    title_text="MRI Cross-sections"
                )
                fig.update_xaxes(showticklabels=False)
                fig.update_yaxes(showticklabels=False)

                st.plotly_chart(fig, use_container_width=True)

                # Enhanced full-screen viewing options
                fullscreen_col1, fullscreen_col2, fullscreen_col3, fullscreen_col4 = st.columns(4)

                with fullscreen_col1:
                    if st.button("🔍 View MRI in Full Screen", key="fullscreen_mri"):
                        display_fullscreen_mri(data)

                with fullscreen_col2:
                    if st.button("🖼️ High-Resolution View", key="highres_mri"):
                        display_highres_mri(data)

                with fullscreen_col3:
                    if st.button("📊 Enhanced MRI Viewer", key="enhanced_mri"):
                        display_enhanced_mri_viewer(data)

                with fullscreen_col4:
                    if st.button("🖥️ Ultra HD Fullscreen", key="ultra_hd_mri"):
                        st.markdown("### 🖥️ Ultra HD Fullscreen Options")

                        # Slice selection for ultra HD
                        ultra_col1, ultra_col2, ultra_col3 = st.columns(3)

                        with ultra_col1:
                            axial_slice = st.slider("Axial Slice", 0, data.shape[2]-1, data.shape[2]//2, key="ultra_axial")
                            if st.button("🖥️ Axial Individual Fullscreen", key="ultra_axial_btn"):
                                display_individual_fullscreen(data, "Individual Fullscreen: Axial MRI", axial_slice, "axial")

                        with ultra_col2:
                            coronal_slice = st.slider("Coronal Slice", 0, data.shape[1]-1, data.shape[1]//2, key="ultra_coronal")
                            if st.button("🖥️ Coronal Individual Fullscreen", key="ultra_coronal_btn"):
                                display_individual_fullscreen(data, "Individual Fullscreen: Coronal MRI", coronal_slice, "coronal")

                        with ultra_col3:
                            sagittal_slice = st.slider("Sagittal Slice", 0, data.shape[0]-1, data.shape[0]//2, key="ultra_sagittal")
                            if st.button("🖥️ Sagittal Individual Fullscreen", key="ultra_sagittal_btn"):
                                display_individual_fullscreen(data, "Individual Fullscreen: Sagittal MRI", sagittal_slice, "sagittal")

    # VERTICAL LAYOUT: Predictions & Results section moved below MRI upload
    st.markdown("---")
    st.markdown('<h2 class="sub-header">🔮 Prediction & Results</h2>', unsafe_allow_html=True)

    # Prediction section
    if st.session_state.current_model and st.session_state.preprocessed_data is not None:
        if st.button("🧠 Run Dementia Assessment", type="primary"):
            with st.spinner("Running AI analysis..."):
                predictions = st.session_state.current_model.predict_single(
                    st.session_state.preprocessed_data
                )

                if predictions:
                    st.session_state.predictions = predictions
                    st.success("🎉 Analysis completed!")
                else:
                    st.error("❌ Prediction failed")

    elif not st.session_state.current_model:
        st.warning("⚠️ Please load a model first")
    elif st.session_state.preprocessed_data is None:
        st.warning("⚠️ Please upload and preprocess an MRI scan first")

    # Display results
    if st.session_state.predictions:
            # PDF Report Status at the top
            display_pdf_status()

            display_predictions(st.session_state.predictions)

            # SHAP interpretability section
            st.markdown("---")
            st.markdown('<h3 class="sub-header">🔍 Model Interpretability</h3>', unsafe_allow_html=True)

            if st.button("🧠 Generate Brain Region Analysis", help="Generate saliency map showing which brain regions influenced the prediction"):
                with st.spinner("Generating brain region importance analysis..."):
                    try:
                        # Generate SHAP heatmap for ADD task
                        heatmap_data = generate_shap_heatmap(
                            st.session_state.current_model,
                            st.session_state.preprocessed_data,
                            task='ADD'
                        )

                        if heatmap_data is not None:
                            st.session_state.heatmap_data = heatmap_data  # Store for PDF generation

                            # Trigger PDF generation now that heatmap is complete
                            st.session_state.pdf_status = 'not_started'

                            # Generate PDF immediately
                            st.success("✅ Brain region analysis completed! Generating clinical PDF report...")

                            filename_base = st.session_state.get('uploaded_filename', 'scan')

                            try:
                                st.session_state.pdf_status = 'generating'

                                with st.spinner("Generating comprehensive clinical PDF report..."):
                                    pdf_bytes = generate_pdf_report(
                                        st.session_state.preprocessed_data,
                                        st.session_state.heatmap_data,
                                        st.session_state.predictions,
                                        filename_base
                                    )

                                if pdf_bytes:
                                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                    filename = f"Demetify_Clinical_Report_{filename_base}_{timestamp}.pdf"

                                    # Store in session state
                                    st.session_state.pdf_data = pdf_bytes
                                    st.session_state.pdf_filename = filename
                                    st.session_state.pdf_status = 'ready'

                                    st.success(f"✅ Clinical PDF report ready! ({len(pdf_bytes):,} bytes)")

                                    # Show download button immediately
                                    st.markdown("### 📄 Download Clinical Report")
                                    st.download_button(
                                        label="📄 Download Clinical Report",
                                        data=pdf_bytes,
                                        file_name=filename,
                                        mime="application/pdf",
                                        type="primary",
                                        use_container_width=True,
                                        key="immediate_download"
                                    )
                                    st.info(f"📁 Filename: {filename}")
                                else:
                                    st.session_state.pdf_status = 'error'
                                    st.error("❌ Failed to generate PDF report")

                            except Exception as e:
                                st.session_state.pdf_status = 'error'
                                st.error(f"❌ PDF generation failed: {str(e)}")

                            display_shap_heatmap(heatmap_data, st.session_state.preprocessed_data)
                        else:
                            st.error("❌ Failed to generate brain region analysis")
                    except Exception as e:
                        st.error(f"❌ Error generating brain region analysis: {str(e)}")
                        st.info("💡 Brain region analysis requires additional computational resources and may take several minutes.")

            # Always display heatmap if it exists (prevents collapse when opacity changes)
            if hasattr(st.session_state, 'heatmap_data') and st.session_state.heatmap_data is not None:
                # Only show this message if heatmap wasn't just generated above
                if 'heatmap_data' in st.session_state and st.session_state.heatmap_data is not None:
                    # Check if we're not in the generation flow
                    if not st.session_state.get('generating_heatmap', False):
                        st.info("✅ Brain region analysis available. Displaying interactive heatmap:")
                        display_shap_heatmap(st.session_state.heatmap_data, st.session_state.preprocessed_data)

def display_highres_mri(mri_data):
    """
    Display MRI data in high-resolution mode with enhanced quality.

    Args:
        mri_data: 3D MRI data array
    """
    st.markdown("## 🖼️ High-Resolution MRI Viewer")
    st.info("📈 **Enhanced Resolution**: Optimized for detailed clinical analysis with improved DPI and larger display.")

    # High-resolution slice selection
    col1, col2, col3 = st.columns(3)

    with col1:
        axial_slice = st.slider(
            "Axial Slice (High-Res)",
            0, mri_data.shape[2]-1,
            mri_data.shape[2]//2,
            key="highres_axial_slider"
        )

    with col2:
        coronal_slice = st.slider(
            "Coronal Slice (High-Res)",
            0, mri_data.shape[1]-1,
            mri_data.shape[1]//2,
            key="highres_coronal_slider"
        )

    with col3:
        sagittal_slice = st.slider(
            "Sagittal Slice (High-Res)",
            0, mri_data.shape[0]-1,
            mri_data.shape[0]//2,
            key="highres_sagittal_slider"
        )

    # High-resolution display with matplotlib for better quality - WIDER DISPLAY
    import matplotlib.pyplot as plt

    # Create much wider figure for better visibility
    fig, axes = plt.subplots(1, 3, figsize=(24, 8), dpi=200)  # Increased width and DPI
    fig.suptitle('High-Resolution MRI Viewer - Clinical Quality', fontsize=20, fontweight='bold')

    # Axial view - maintain original orientation
    axial_data = mri_data[:, :, axial_slice]
    axes[0].imshow(axial_data, cmap='gray', interpolation='bilinear', aspect='equal', origin='lower')
    axes[0].set_title(f'Axial - Slice {axial_slice}/{mri_data.shape[2]-1}', fontsize=16, fontweight='bold')
    axes[0].axis('off')

    # Coronal view - maintain original orientation
    coronal_data = mri_data[:, coronal_slice, :]
    axes[1].imshow(coronal_data, cmap='gray', interpolation='bilinear', aspect='equal', origin='lower')
    axes[1].set_title(f'Coronal - Slice {coronal_slice}/{mri_data.shape[1]-1}', fontsize=16, fontweight='bold')
    axes[1].axis('off')

    # Sagittal view - maintain original orientation
    sagittal_data = mri_data[sagittal_slice, :, :]
    axes[2].imshow(sagittal_data, cmap='gray', interpolation='bilinear', aspect='equal', origin='lower')
    axes[2].set_title(f'Sagittal - Slice {sagittal_slice}/{mri_data.shape[0]-1}', fontsize=16, fontweight='bold')
    axes[2].axis('off')

    plt.tight_layout()
    st.pyplot(fig, dpi=200, use_container_width=True)  # Higher DPI and use full container width

    # High-resolution statistics
    st.markdown("### 📊 High-Resolution Analysis")
    hr_col1, hr_col2, hr_col3, hr_col4 = st.columns(4)

    with hr_col1:
        st.metric("Resolution", f"{mri_data.shape}")
    with hr_col2:
        st.metric("Current Axial", f"{axial_slice}/{mri_data.shape[2]-1}")
    with hr_col3:
        st.metric("Current Coronal", f"{coronal_slice}/{mri_data.shape[1]-1}")
    with hr_col4:
        st.metric("Current Sagittal", f"{sagittal_slice}/{mri_data.shape[0]-1}")

def display_enhanced_mri_viewer(mri_data):
    """
    Display MRI data with enhanced clinical features and controls.

    Args:
        mri_data: 3D MRI data array
    """
    st.markdown("## 📊 Enhanced Clinical MRI Viewer")
    st.info("🏥 **Clinical Features**: Advanced windowing, measurements, and radiologist-friendly controls.")

    # Enhanced controls
    control_col1, control_col2, control_col3 = st.columns(3)

    with control_col1:
        window_center = st.slider("Window Center", 0.0, 1.0, 0.5, 0.01, key="enhanced_window_center")
    with control_col2:
        window_width = st.slider("Window Width", 0.1, 1.0, 0.8, 0.01, key="enhanced_window_width")
    with control_col3:
        contrast_enhancement = st.slider("Contrast Enhancement", 0.5, 2.0, 1.0, 0.1, key="enhanced_contrast")

    # Apply windowing
    min_val = window_center - window_width/2
    max_val = window_center + window_width/2
    windowed_data = np.clip(mri_data, min_val, max_val)
    windowed_data = (windowed_data - min_val) / (max_val - min_val)
    windowed_data = np.power(windowed_data, 1/contrast_enhancement)  # Contrast adjustment

    # Slice selection
    slice_col1, slice_col2, slice_col3 = st.columns(3)

    with slice_col1:
        axial_slice = st.slider(
            "Axial Slice",
            0, mri_data.shape[2]-1,
            mri_data.shape[2]//2,
            key="enhanced_axial_slider"
        )

    with slice_col2:
        coronal_slice = st.slider(
            "Coronal Slice",
            0, mri_data.shape[1]-1,
            mri_data.shape[1]//2,
            key="enhanced_coronal_slider"
        )

    with slice_col3:
        sagittal_slice = st.slider(
            "Sagittal Slice",
            0, mri_data.shape[0]-1,
            mri_data.shape[0]//2,
            key="enhanced_sagittal_slider"
        )

    # Enhanced display with measurements
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            f'Axial - Enhanced (Z={axial_slice})',
            f'Coronal - Enhanced (Y={coronal_slice})',
            f'Sagittal - Enhanced (X={sagittal_slice})',
            'Cross-Reference View'
        ]
    )

    # Add enhanced slices
    fig.add_trace(
        go.Heatmap(z=windowed_data[:, :, axial_slice], colorscale='gray', showscale=False),
        row=1, col=1
    )

    fig.add_trace(
        go.Heatmap(z=windowed_data[:, coronal_slice, :], colorscale='gray', showscale=False),
        row=1, col=2
    )

    fig.add_trace(
        go.Heatmap(z=windowed_data[sagittal_slice, :, :], colorscale='gray', showscale=False),
        row=2, col=1
    )

    # Cross-reference view with crosshairs
    cross_ref = windowed_data[:, :, axial_slice].copy()
    # Add crosshairs
    if 0 <= coronal_slice < cross_ref.shape[1]:
        cross_ref[:, coronal_slice] = 1.0
    if 0 <= sagittal_slice < cross_ref.shape[0]:
        cross_ref[sagittal_slice, :] = 1.0

    fig.add_trace(
        go.Heatmap(z=cross_ref, colorscale='gray', showscale=True, colorbar=dict(title="Intensity")),
        row=2, col=2
    )

    fig.update_layout(
        title="Enhanced Clinical MRI Viewer with Windowing Controls",
        height=800,
        showlegend=False
    )

    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Clinical measurements
    st.markdown("### 📏 Clinical Measurements")
    meas_col1, meas_col2, meas_col3, meas_col4 = st.columns(4)

    with meas_col1:
        st.metric("Window/Level", f"C:{window_center:.2f} W:{window_width:.2f}")
    with meas_col2:
        st.metric("Contrast", f"{contrast_enhancement:.1f}x")
    with meas_col3:
        current_intensity = windowed_data[sagittal_slice, coronal_slice, axial_slice]
        st.metric("Crosshair Intensity", f"{current_intensity:.3f}")
    with meas_col4:
        st.metric("Position (X,Y,Z)", f"({sagittal_slice},{coronal_slice},{axial_slice})")

def display_individual_fullscreen(image_data, title="Individual Fullscreen Viewer", slice_idx=None, orientation="axial", colormap="gray", is_heatmap=False):
    """
    Display a SINGLE image in true fullscreen mode with proper aspect ratio for 34-inch screens.

    Args:
        image_data: 2D or 3D image data array
        title: Title for the viewer
        slice_idx: Slice index if 3D data
        orientation: Orientation if 3D data (axial, coronal, sagittal)
        colormap: Colormap to use ('gray', 'hot', 'jet', 'plasma', etc.)
        is_heatmap: Whether this is a heatmap (affects display settings)
    """
    st.markdown(f"## 🖥️ {title}")
    st.info("🎯 **Individual Fullscreen Mode**: Single image with maximum DPI, no stretching, optimized for 34-inch screens")

    import matplotlib.pyplot as plt

    # Handle 3D data by extracting the specified slice
    if len(image_data.shape) == 3:
        if slice_idx is None:
            slice_idx = image_data.shape[2] // 2  # Default to middle slice

        if orientation == "axial":
            display_data = image_data[:, :, slice_idx]
        elif orientation == "coronal":
            display_data = image_data[:, slice_idx, :]
        elif orientation == "sagittal":
            display_data = image_data[slice_idx, :, :]
        else:
            display_data = image_data[:, :, slice_idx]
    else:
        display_data = image_data

    # Calculate proper aspect ratio to prevent stretching
    img_height, img_width = display_data.shape
    aspect_ratio = img_width / img_height

    # Ultra high-resolution settings for 34-inch screen
    plt.rcParams['figure.dpi'] = 300  # Maximum DPI
    plt.rcParams['savefig.dpi'] = 300

    # Calculate optimal figure size maintaining aspect ratio
    # For 34-inch screen, use maximum available space while preserving aspect ratio
    max_width = 30  # Leave some margin
    max_height = 20  # Leave some margin for title/colorbar

    if aspect_ratio > max_width / max_height:
        # Image is wider - constrain by width
        fig_width = max_width
        fig_height = max_width / aspect_ratio
    else:
        # Image is taller - constrain by height
        fig_height = max_height
        fig_width = max_height * aspect_ratio

    # Create ultra high-resolution figure with proper aspect ratio
    fig, ax = plt.subplots(figsize=(fig_width, fig_height), dpi=300)

    # Display with maximum quality settings and NO STRETCHING
    im = ax.imshow(
        display_data,
        cmap=colormap,
        interpolation='lanczos',  # Highest quality interpolation
        aspect='equal',  # CRITICAL: Prevents stretching
        origin='lower'   # Proper medical imaging orientation
    )

    # Add colorbar for heatmaps (positioned to not interfere)
    if is_heatmap or colormap != 'gray':
        # Position colorbar at bottom to preserve image aspect ratio
        cbar = plt.colorbar(im, ax=ax, orientation='horizontal',
                           fraction=0.046, pad=0.08, shrink=0.8)
        cbar.set_label('AI Attention Intensity', fontsize=18, fontweight='bold')
        cbar.ax.tick_params(labelsize=14)

    # Remove axes but keep proper scaling
    ax.axis('off')

    # Set title with high-resolution font
    if len(image_data.shape) == 3:
        fig.suptitle(f'{title} - {orientation.title()} Slice {slice_idx}',
                    fontsize=28, fontweight='bold', y=0.95)
    else:
        fig.suptitle(title, fontsize=28, fontweight='bold', y=0.95)

    # Tight layout to maximize image size while preserving aspect ratio
    plt.tight_layout()

    # Display with maximum container width and DPI
    st.pyplot(fig, dpi=300, use_container_width=True, clear_figure=True)

    # Add image statistics
    st.markdown("### 📊 Individual Fullscreen Statistics")
    stat_col1, stat_col2, stat_col3, stat_col4, stat_col5 = st.columns(5)

    with stat_col1:
        st.metric("Resolution", f"{img_width}×{img_height}")
    with stat_col2:
        st.metric("Aspect Ratio", f"{aspect_ratio:.2f}:1")
    with stat_col3:
        st.metric("DPI", "300 (Ultra High)")
    with stat_col4:
        st.metric("Figure Size", f"{fig_width:.1f}×{fig_height:.1f} in")
    with stat_col5:
        st.metric("No Stretching", "✅ Preserved")

    # Close figure to free memory
    plt.close(fig)

def display_fullscreen_mri(mri_data):
    """
    Display MRI data in full-screen mode with interactive controls.

    Args:
        mri_data: 3D MRI data array
    """
    st.markdown("## 🔍 Full-Screen MRI Viewer")

    # Slice selection controls
    col1, col2, col3 = st.columns(3)

    with col1:
        axial_slice = st.slider(
            "Axial Slice",
            0, mri_data.shape[2]-1,
            mri_data.shape[2]//2,
            key="axial_slider"
        )

    with col2:
        coronal_slice = st.slider(
            "Coronal Slice",
            0, mri_data.shape[1]-1,
            mri_data.shape[1]//2,
            key="coronal_slider"
        )

    with col3:
        sagittal_slice = st.slider(
            "Sagittal Slice",
            0, mri_data.shape[0]-1,
            mri_data.shape[0]//2,
            key="sagittal_slider"
        )

    # Display controls
    col1, col2 = st.columns(2)
    with col1:
        colorscale = st.selectbox(
            "Color Scale",
            ["gray", "viridis", "plasma", "inferno", "magma", "bone"],
            index=0
        )

    with col2:
        show_colorbar = st.checkbox("Show Color Bar", value=True)

    # Create full-screen plots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            f"Axial (Z={axial_slice})",
            f"Coronal (Y={coronal_slice})",
            f"Sagittal (X={sagittal_slice})",
            "3D Cross-section View"
        ],
        specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
               [{"type": "heatmap"}, {"type": "heatmap"}]]
    )

    # Axial view
    axial_data = mri_data[:, :, axial_slice]
    fig.add_trace(
        go.Heatmap(
            z=axial_data,
            colorscale=colorscale,
            showscale=show_colorbar,
            colorbar=dict(title="Intensity", x=0.48, len=0.4) if show_colorbar else None
        ),
        row=1, col=1
    )

    # Coronal view
    coronal_data = mri_data[:, coronal_slice, :]
    fig.add_trace(
        go.Heatmap(
            z=coronal_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=1, col=2
    )

    # Sagittal view
    sagittal_data = mri_data[sagittal_slice, :, :]
    fig.add_trace(
        go.Heatmap(
            z=sagittal_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=2, col=1
    )

    # Combined view showing intersection
    combined_data = np.zeros_like(axial_data)
    combined_data[:, :] = axial_data
    # Add crosshairs
    combined_data[coronal_slice, :] = np.max(axial_data)  # Horizontal line
    combined_data[:, sagittal_slice] = np.max(axial_data)  # Vertical line

    fig.add_trace(
        go.Heatmap(
            z=combined_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=2, col=2
    )

    fig.update_layout(
        height=800,
        showlegend=False,
        title_text="Interactive MRI Viewer - Navigate with sliders above"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Data information
    st.markdown("### 📊 Data Information")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Shape", f"{mri_data.shape}")
    with col2:
        st.metric("Min Value", f"{mri_data.min():.3f}")
    with col3:
        st.metric("Max Value", f"{mri_data.max():.3f}")
    with col4:
        st.metric("Mean Value", f"{mri_data.mean():.3f}")

def generate_shap_heatmap(model, mri_data, task='ADD'):
    """
    Generate gradient-based saliency map for model interpretability.

    Args:
        model: Loaded NCOMMs2022Model instance
        mri_data: Preprocessed MRI data
        task: Task to analyze ('ADD' or 'COG')

    Returns:
        numpy array: Saliency values for visualization
    """
    try:
        import torch

        # Prepare input for gradient analysis
        input_tensor = torch.from_numpy(mri_data).float()
        input_tensor = input_tensor.unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions
        input_tensor = input_tensor.to(model.device)
        input_tensor.requires_grad_(True)

        # Forward pass
        model.model.eval()  # Ensure model is in eval mode
        predictions = model.model(input_tensor)

        # Select target output based on task
        if task == 'ADD':
            # Use the predicted class probability
            add_probs = torch.softmax(predictions['ADD'], dim=1)
            target_output = add_probs[:, 1]  # AD probability
        else:
            target_output = predictions['COG'][:, 0]

        # Compute gradients
        target_output.backward()
        gradients = input_tensor.grad

        if gradients is None:
            st.error("❌ Failed to compute gradients")
            return None

        # Use gradient magnitude as saliency map
        saliency_map = torch.abs(gradients[0, 0]).cpu().numpy()

        # Apply smoothing to reduce noise
        from scipy.ndimage import gaussian_filter
        saliency_map = gaussian_filter(saliency_map, sigma=1.0)

        st.success("✅ Brain region importance analysis completed!")
        return saliency_map

    except Exception as e:
        st.error(f"❌ Error generating brain region analysis: {str(e)}")
        import traceback
        st.error(f"Details: {traceback.format_exc()}")
        return None

def display_shap_heatmap(heatmap_data, original_data):
    """
    Display SHAP heatmap overlaid on original MRI data with enhanced clinical interface.

    Args:
        heatmap_data: SHAP values or saliency map
        original_data: Original MRI data
    """
    st.markdown("### 🔥 Brain Region Importance Analysis")
    st.info("🧠 **Real AI Interpretability**: This shows actual gradient-based analysis of which brain regions influenced the AI's decision. Brighter areas had more impact on the prediction.")

    # Enhanced controls for clinical use
    st.markdown("#### 🎛️ Clinical Visualization Controls")

    col1, col2, col3 = st.columns(3)
    with col1:
        heatmap_colorscale = st.selectbox(
            "Clinical Color Scale",
            ["hot", "jet", "plasma", "viridis", "inferno", "magma", "turbo"],
            index=0,
            key="heatmap_colorscale",
            help="Hot colorscale is standard for medical imaging"
        )
    with col2:
        # Initialize main opacity in session state
        if 'main_opacity_value' not in st.session_state:
            st.session_state.main_opacity_value = 0.6

        # Use simple slider without complex callbacks
        opacity_level = st.slider(
            "Overlay Opacity",
            0.0, 1.0, st.session_state.main_opacity_value, 0.1,
            key="simple_main_opacity",
            help="Adjust to balance anatomy visibility with AI attention"
        )

        # Update session state directly
        st.session_state.main_opacity_value = opacity_level
    with col3:
        threshold_pct = st.slider(
            "Significance Threshold (%)",
            50, 95, 75, 5,
            key="significance_threshold",
            help="Filter out low-significance regions"
        )

    # Apply significance threshold
    threshold_value = np.percentile(heatmap_data[heatmap_data > 0], threshold_pct) if len(heatmap_data[heatmap_data > 0]) > 0 else 0
    significant_heatmap = np.where(heatmap_data > threshold_value, heatmap_data, 0)

    # Normalize heatmap for visualization
    if significant_heatmap.max() > 0:
        heatmap_norm = significant_heatmap / significant_heatmap.max()
    else:
        heatmap_norm = significant_heatmap

    # Enhanced slice navigation
    st.markdown("#### 🔍 Interactive Slice Navigation")

    slice_col1, slice_col2, slice_col3 = st.columns(3)

    with slice_col1:
        axial_slice = st.slider(
            "Axial Slice (Bottom→Top)",
            0, heatmap_data.shape[2]-1,
            heatmap_data.shape[2]//2,
            key="enhanced_axial_slider"
        )
        st.write(f"Slice {axial_slice} of {heatmap_data.shape[2]-1}")

    with slice_col2:
        coronal_slice = st.slider(
            "Coronal Slice (Front→Back)",
            0, heatmap_data.shape[1]-1,
            heatmap_data.shape[1]//2,
            key="enhanced_coronal_slider"
        )
        st.write(f"Slice {coronal_slice} of {heatmap_data.shape[1]-1}")

    with slice_col3:
        sagittal_slice = st.slider(
            "Sagittal Slice (Left→Right)",
            0, heatmap_data.shape[0]-1,
            heatmap_data.shape[0]//2,
            key="enhanced_sagittal_slider"
        )
        st.write(f"Slice {sagittal_slice} of {heatmap_data.shape[0]-1}")

    # Enhanced clinical visualization
    st.markdown("#### 📊 Clinical Heatmap Analysis")

    # Create enhanced overlay visualization using selected slices
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=[
            f"Axial Original - Slice {axial_slice}",
            f"Coronal Original - Slice {coronal_slice}",
            f"Sagittal Original - Slice {sagittal_slice}",
            f"Axial AI Analysis - Slice {axial_slice}",
            f"Coronal AI Analysis - Slice {coronal_slice}",
            f"Sagittal AI Analysis - Slice {sagittal_slice}"
        ],
        vertical_spacing=0.12,
        horizontal_spacing=0.05
    )

    # Original slices (top row) - using selected slices
    fig.add_trace(
        go.Heatmap(z=original_data[:, :, axial_slice], colorscale='gray', showscale=False),
        row=1, col=1
    )

    fig.add_trace(
        go.Heatmap(z=original_data[:, coronal_slice, :], colorscale='gray', showscale=False),
        row=1, col=2
    )

    fig.add_trace(
        go.Heatmap(z=original_data[sagittal_slice, :, :], colorscale='gray', showscale=False),
        row=1, col=3
    )

    # AI Analysis slices (bottom row) - using selected slices
    fig.add_trace(
        go.Heatmap(
            z=heatmap_norm[:, :, axial_slice],
            colorscale=heatmap_colorscale,
            showscale=True,
            colorbar=dict(title="AI Attention", x=1.02, len=0.4)
        ),
        row=2, col=1
    )

    fig.add_trace(
        go.Heatmap(z=heatmap_norm[:, coronal_slice, :], colorscale=heatmap_colorscale, showscale=False),
        row=2, col=2
    )

    fig.add_trace(
        go.Heatmap(z=heatmap_norm[sagittal_slice, :, :], colorscale=heatmap_colorscale, showscale=False),
        row=2, col=3
    )

    fig.update_layout(
        height=800,  # Increased height for bigger display
        showlegend=False,
        title_text="Brain Region Importance Analysis"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Enhanced overlay view with proper heatmap overlay - STABLE IMPLEMENTATION
    st.markdown("#### 🔬 True Heatmap Overlay on MRI")

    # Initialize opacity in session state if not exists
    if 'overlay_opacity_stable' not in st.session_state:
        st.session_state.overlay_opacity_stable = 0.5

    # Use stable slider without forms to prevent collapse
    overlay_opacity = st.slider(
        "Heatmap Overlay Opacity",
        0.0, 1.0, st.session_state.overlay_opacity_stable, 0.05,
        key="stable_overlay_slider",
        help="Control how transparent/opaque the heatmap overlay appears on the MRI"
    )

    # Update session state only if value actually changed
    if overlay_opacity != st.session_state.overlay_opacity_stable:
        st.session_state.overlay_opacity_stable = overlay_opacity

    # Create TRUE OVERLAP using matplotlib for perfect blending
    st.markdown("##### 🎯 Real MRI + Heatmap Overlap (Matplotlib)")

    import matplotlib.pyplot as plt
    import matplotlib.colors as mcolors

    # Create wide figure for better visibility
    fig_overlay, axes_overlay = plt.subplots(1, 3, figsize=(24, 8), dpi=200)
    fig_overlay.suptitle(f'TRUE OVERLAP: MRI + AI Heatmap (Opacity: {overlay_opacity:.0%})', fontsize=20, fontweight='bold')

    # Axial true overlap - maintain original orientation
    axial_mri = original_data[:, :, axial_slice]
    axial_heatmap = heatmap_norm[:, :, axial_slice]

    # Debug: Check heatmap statistics for axial
    axial_max = axial_heatmap.max()
    axial_mean = axial_heatmap.mean()
    axial_nonzero = np.sum(axial_heatmap > 0)

    # Show MRI as base
    axes_overlay[0].imshow(axial_mri, cmap='gray', alpha=1.0, aspect='equal', origin='lower')

    # Use adaptive threshold based on actual data
    adaptive_threshold = max(0.01, axial_mean * 0.5)  # Much lower threshold
    axial_heatmap_masked = np.ma.masked_where(axial_heatmap < adaptive_threshold, axial_heatmap)

    im1 = axes_overlay[0].imshow(axial_heatmap_masked, cmap=heatmap_colorscale, alpha=overlay_opacity,
                                aspect='equal', origin='lower', vmin=0, vmax=heatmap_norm.max())
    axes_overlay[0].set_title(f'Axial - Slice {axial_slice} (Max:{axial_max:.3f}, Active:{axial_nonzero})',
                             fontsize=14, fontweight='bold')
    axes_overlay[0].axis('off')

    # Coronal true overlap - maintain original orientation
    coronal_mri = original_data[:, coronal_slice, :]
    coronal_heatmap = heatmap_norm[:, coronal_slice, :]

    # Debug: Check heatmap statistics for coronal
    coronal_max = coronal_heatmap.max()
    coronal_mean = coronal_heatmap.mean()
    coronal_nonzero = np.sum(coronal_heatmap > 0)

    axes_overlay[1].imshow(coronal_mri, cmap='gray', alpha=1.0, aspect='equal', origin='lower')

    # Use adaptive threshold based on actual data
    adaptive_threshold = max(0.01, coronal_mean * 0.5)  # Much lower threshold
    coronal_heatmap_masked = np.ma.masked_where(coronal_heatmap < adaptive_threshold, coronal_heatmap)

    axes_overlay[1].imshow(coronal_heatmap_masked, cmap=heatmap_colorscale, alpha=overlay_opacity,
                          aspect='equal', origin='lower', vmin=0, vmax=heatmap_norm.max())
    axes_overlay[1].set_title(f'Coronal - Slice {coronal_slice} (Max:{coronal_max:.3f}, Active:{coronal_nonzero})',
                             fontsize=14, fontweight='bold')
    axes_overlay[1].axis('off')

    # Sagittal true overlap - maintain original orientation
    sagittal_mri = original_data[sagittal_slice, :, :]
    sagittal_heatmap = heatmap_norm[sagittal_slice, :, :]

    # Debug: Check heatmap statistics for sagittal
    sagittal_max = sagittal_heatmap.max()
    sagittal_mean = sagittal_heatmap.mean()
    sagittal_nonzero = np.sum(sagittal_heatmap > 0)

    axes_overlay[2].imshow(sagittal_mri, cmap='gray', alpha=1.0, aspect='equal', origin='lower')

    # Use adaptive threshold based on actual data
    adaptive_threshold = max(0.01, sagittal_mean * 0.5)  # Much lower threshold
    sagittal_heatmap_masked = np.ma.masked_where(sagittal_heatmap < adaptive_threshold, sagittal_heatmap)

    im3 = axes_overlay[2].imshow(sagittal_heatmap_masked, cmap=heatmap_colorscale, alpha=overlay_opacity,
                                aspect='equal', origin='lower', vmin=0, vmax=heatmap_norm.max())
    axes_overlay[2].set_title(f'Sagittal - Slice {sagittal_slice} (Max:{sagittal_max:.3f}, Active:{sagittal_nonzero})',
                             fontsize=14, fontweight='bold')
    axes_overlay[2].axis('off')

    # Add colorbar for the heatmap
    cbar = plt.colorbar(im3, ax=axes_overlay, orientation='horizontal', fraction=0.05, pad=0.1)
    cbar.set_label('AI Attention Intensity', fontweight='bold', fontsize=14)

    plt.tight_layout()
    st.pyplot(fig_overlay, dpi=200, use_container_width=True)

    # Add explanation with debug information and fullscreen options
    st.info(f"""
    🎯 **True Overlay Visualization**:
    - **Gray background**: Original MRI anatomy
    - **{heatmap_colorscale.title()} overlay**: AI attention regions (opacity: {overlay_opacity:.0%})
    - **Adaptive thresholding**: Each view uses its own optimal threshold
    - **Debug info**: Check titles for max values and active voxel counts
    - **Adjust opacity slider** above to control overlay visibility
    """)

    # Individual Fullscreen options for heatmap overlays
    st.markdown("#### 🖥️ Individual Fullscreen Heatmap Options")
    st.info("🎯 **Single Image Fullscreen**: Each button opens ONE image in fullscreen with proper aspect ratio")

    ultra_heatmap_col1, ultra_heatmap_col2, ultra_heatmap_col3 = st.columns(3)

    with ultra_heatmap_col1:
        if st.button("🖥️ Axial Overlay Fullscreen", key="individual_axial_overlay"):
            # Create combined overlay for fullscreen
            axial_mri = original_data[:, :, axial_slice]
            axial_heatmap = heatmap_norm[:, :, axial_slice]
            adaptive_threshold = max(0.01, axial_heatmap.mean() * 0.5)

            # Create overlay image with proper blending
            overlay_image = axial_mri.copy().astype(float)
            significant_mask = axial_heatmap > adaptive_threshold
            # Normalize both images to 0-1 range for proper blending
            axial_mri_norm = (axial_mri - axial_mri.min()) / (axial_mri.max() - axial_mri.min())
            axial_heatmap_norm = (axial_heatmap - axial_heatmap.min()) / (axial_heatmap.max() - axial_heatmap.min())
            overlay_image = axial_mri_norm * (1-overlay_opacity) + axial_heatmap_norm * overlay_opacity * significant_mask

            display_individual_fullscreen(overlay_image, f"Individual Fullscreen: Axial Overlay - Slice {axial_slice}", colormap="hot")

    with ultra_heatmap_col2:
        if st.button("🖥️ Coronal Overlay Fullscreen", key="individual_coronal_overlay"):
            # Create combined overlay for fullscreen
            coronal_mri = original_data[:, coronal_slice, :]
            coronal_heatmap = heatmap_norm[:, coronal_slice, :]
            adaptive_threshold = max(0.01, coronal_heatmap.mean() * 0.5)

            # Create overlay image with proper blending
            overlay_image = coronal_mri.copy().astype(float)
            significant_mask = coronal_heatmap > adaptive_threshold
            # Normalize both images to 0-1 range for proper blending
            coronal_mri_norm = (coronal_mri - coronal_mri.min()) / (coronal_mri.max() - coronal_mri.min())
            coronal_heatmap_norm = (coronal_heatmap - coronal_heatmap.min()) / (coronal_heatmap.max() - coronal_heatmap.min())
            overlay_image = coronal_mri_norm * (1-overlay_opacity) + coronal_heatmap_norm * overlay_opacity * significant_mask

            display_individual_fullscreen(overlay_image, f"Individual Fullscreen: Coronal Overlay - Slice {coronal_slice}", colormap="hot")

    with ultra_heatmap_col3:
        if st.button("🖥️ Sagittal Overlay Fullscreen", key="individual_sagittal_overlay"):
            # Create combined overlay for fullscreen
            sagittal_mri = original_data[sagittal_slice, :, :]
            sagittal_heatmap = heatmap_norm[sagittal_slice, :, :]
            adaptive_threshold = max(0.01, sagittal_heatmap.mean() * 0.5)

            # Create overlay image with proper blending
            overlay_image = sagittal_mri.copy().astype(float)
            significant_mask = sagittal_heatmap > adaptive_threshold
            # Normalize both images to 0-1 range for proper blending
            sagittal_mri_norm = (sagittal_mri - sagittal_mri.min()) / (sagittal_mri.max() - sagittal_mri.min())
            sagittal_heatmap_norm = (sagittal_heatmap - sagittal_heatmap.min()) / (sagittal_heatmap.max() - sagittal_heatmap.min())
            overlay_image = sagittal_mri_norm * (1-overlay_opacity) + sagittal_heatmap_norm * overlay_opacity * significant_mask

            display_individual_fullscreen(overlay_image, f"Individual Fullscreen: Sagittal Overlay - Slice {sagittal_slice}", colormap="hot")

    # Add debug statistics
    st.markdown("##### 📊 Heatmap Statistics by View")
    debug_col1, debug_col2, debug_col3 = st.columns(3)

    with debug_col1:
        st.metric("Axial Max", f"{axial_max:.4f}")
        st.metric("Axial Active Voxels", f"{axial_nonzero}")
        st.metric("Axial Threshold", f"{max(0.01, axial_mean * 0.5):.4f}")

    with debug_col2:
        st.metric("Coronal Max", f"{coronal_max:.4f}")
        st.metric("Coronal Active Voxels", f"{coronal_nonzero}")
        st.metric("Coronal Threshold", f"{max(0.01, coronal_mean * 0.5):.4f}")

    with debug_col3:
        st.metric("Sagittal Max", f"{sagittal_max:.4f}")
        st.metric("Sagittal Active Voxels", f"{sagittal_nonzero}")
        st.metric("Sagittal Threshold", f"{max(0.01, sagittal_mean * 0.5):.4f}")

    # Overall heatmap statistics
    st.markdown("##### 🔍 Overall Heatmap Analysis")
    overall_col1, overall_col2, overall_col3, overall_col4 = st.columns(4)

    with overall_col1:
        st.metric("Global Max", f"{heatmap_norm.max():.4f}")
    with overall_col2:
        st.metric("Global Mean", f"{heatmap_norm.mean():.4f}")
    with overall_col3:
        st.metric("Global Active", f"{np.sum(heatmap_norm > 0.01)}")
    with overall_col4:
        st.metric("Data Shape", f"{heatmap_norm.shape}")

    # Individual Pure Heatmap Fullscreen Options
    st.markdown("#### 🔥 Individual Pure Heatmap Fullscreen")
    st.info("🎯 **Pure AI Attention Maps**: View only the heatmap data in individual fullscreen with proper aspect ratio")

    pure_heatmap_col1, pure_heatmap_col2, pure_heatmap_col3 = st.columns(3)

    with pure_heatmap_col1:
        if st.button("🔥 Axial Heatmap Fullscreen", key="individual_axial_heatmap"):
            axial_heatmap = heatmap_norm[:, :, axial_slice]
            display_individual_fullscreen(axial_heatmap, f"Individual Fullscreen: Pure Axial Heatmap - Slice {axial_slice}",
                                        colormap=heatmap_colorscale, is_heatmap=True)

    with pure_heatmap_col2:
        if st.button("🔥 Coronal Heatmap Fullscreen", key="individual_coronal_heatmap"):
            coronal_heatmap = heatmap_norm[:, coronal_slice, :]
            display_individual_fullscreen(coronal_heatmap, f"Individual Fullscreen: Pure Coronal Heatmap - Slice {coronal_slice}",
                                        colormap=heatmap_colorscale, is_heatmap=True)

    with pure_heatmap_col3:
        if st.button("🔥 Sagittal Heatmap Fullscreen", key="individual_sagittal_heatmap"):
            sagittal_heatmap = heatmap_norm[sagittal_slice, :, :]
            display_individual_fullscreen(sagittal_heatmap, f"Individual Fullscreen: Pure Sagittal Heatmap - Slice {sagittal_slice}",
                                        colormap=heatmap_colorscale, is_heatmap=True)

    # Alternative matplotlib-based overlay for better control
    st.markdown("#### 🎨 Alternative High-Quality Overlay (Matplotlib)")

    # Matplotlib overlay controls - STABLE IMPLEMENTATION

    # Initialize matplotlib settings in session state
    if 'mpl_opacity_stable' not in st.session_state:
        st.session_state.mpl_opacity_stable = 0.6
    if 'mpl_colormap_stable' not in st.session_state:
        st.session_state.mpl_colormap_stable = "hot"

    # Use stable controls without forms
    mpl_col1, mpl_col2 = st.columns(2)

    with mpl_col1:
        mpl_opacity = st.slider(
            "Matplotlib Overlay Opacity",
            0.0, 1.0, st.session_state.mpl_opacity_stable, 0.05,
            key="stable_mpl_opacity_slider",
            help="Fine-tune overlay transparency for optimal visualization"
        )

    with mpl_col2:
        mpl_colormap = st.selectbox(
            "Matplotlib Colormap",
            ["hot", "jet", "plasma", "viridis", "inferno", "magma"],
            index=["hot", "jet", "plasma", "viridis", "inferno", "magma"].index(st.session_state.mpl_colormap_stable),
            key="stable_mpl_colormap_select"
        )

    # Update session state only if values changed
    if mpl_opacity != st.session_state.mpl_opacity_stable:
        st.session_state.mpl_opacity_stable = mpl_opacity
    if mpl_colormap != st.session_state.mpl_colormap_stable:
        st.session_state.mpl_colormap_stable = mpl_colormap

    # Create matplotlib overlay
    import matplotlib.pyplot as plt
    import matplotlib.colors as mcolors

    # Make this overlay MUCH WIDER for better visibility
    fig_mpl, axes = plt.subplots(1, 3, figsize=(24, 8), dpi=200)  # Much wider and higher DPI
    fig_mpl.suptitle(f'High-Quality MRI + AI Heatmap Overlay (Opacity: {mpl_opacity:.0%})', fontsize=20, fontweight='bold')

    # Axial overlay - maintain original orientation with adaptive threshold
    axes[0].imshow(original_data[:, :, axial_slice], cmap='gray', alpha=1.0, aspect='equal', origin='lower')
    axial_data = heatmap_norm[:, :, axial_slice]
    axial_threshold = max(0.01, axial_data.mean() * 0.5)
    heatmap_masked = np.ma.masked_where(axial_data < axial_threshold, axial_data)
    im1 = axes[0].imshow(heatmap_masked, cmap=mpl_colormap, alpha=mpl_opacity, vmin=0, vmax=heatmap_norm.max(),
                        aspect='equal', origin='lower')
    axes[0].set_title(f'Axial - Slice {axial_slice} (Adaptive)', fontweight='bold', fontsize=16)
    axes[0].axis('off')

    # Coronal overlay - maintain original orientation with adaptive threshold
    axes[1].imshow(original_data[:, coronal_slice, :], cmap='gray', alpha=1.0, aspect='equal', origin='lower')
    coronal_data = heatmap_norm[:, coronal_slice, :]
    coronal_threshold = max(0.01, coronal_data.mean() * 0.5)
    heatmap_masked = np.ma.masked_where(coronal_data < coronal_threshold, coronal_data)
    axes[1].imshow(heatmap_masked, cmap=mpl_colormap, alpha=mpl_opacity, vmin=0, vmax=heatmap_norm.max(),
                  aspect='equal', origin='lower')
    axes[1].set_title(f'Coronal - Slice {coronal_slice} (Adaptive)', fontweight='bold', fontsize=16)
    axes[1].axis('off')

    # Sagittal overlay - maintain original orientation with adaptive threshold
    axes[2].imshow(original_data[sagittal_slice, :, :], cmap='gray', alpha=1.0, aspect='equal', origin='lower')
    sagittal_data = heatmap_norm[sagittal_slice, :, :]
    sagittal_threshold = max(0.01, sagittal_data.mean() * 0.5)
    heatmap_masked = np.ma.masked_where(sagittal_data < sagittal_threshold, sagittal_data)
    im3 = axes[2].imshow(heatmap_masked, cmap=mpl_colormap, alpha=mpl_opacity, vmin=0, vmax=heatmap_norm.max(),
                        aspect='equal', origin='lower')
    axes[2].set_title(f'Sagittal - Slice {sagittal_slice} (Adaptive)', fontweight='bold', fontsize=16)
    axes[2].axis('off')

    # Add colorbar
    cbar = plt.colorbar(im3, ax=axes, orientation='horizontal', fraction=0.05, pad=0.1)
    cbar.set_label('AI Attention Intensity', fontweight='bold')

    plt.tight_layout()
    st.pyplot(fig_mpl, dpi=200, use_container_width=True)  # Use full container width

    # Overlay comparison info
    st.success(f"""
    ✅ **Perfect Overlay Achieved!**
    - **MRI base**: Grayscale anatomical structure
    - **AI heatmap**: {mpl_colormap.title()} overlay at {mpl_opacity:.0%} opacity
    - **Masked regions**: Only significant AI attention shown
    - **High quality**: Matplotlib rendering for publication-ready images
    """)

    # Enhanced statistics and interpretation
    st.markdown("#### 📈 Quantitative Analysis")

    # Calculate enhanced statistics
    active_voxels = np.sum(significant_heatmap > 0)
    total_brain_voxels = np.sum(original_data > np.percentile(original_data[original_data > 0], 5)) if len(original_data[original_data > 0]) > 0 else original_data.size
    coverage_percentage = (active_voxels / total_brain_voxels * 100) if total_brain_voxels > 0 else 0

    stat_col1, stat_col2, stat_col3, stat_col4 = st.columns(4)

    with stat_col1:
        st.metric("Active Regions", f"{active_voxels:,} voxels")

    with stat_col2:
        st.metric("Brain Coverage", f"{coverage_percentage:.1f}%")

    with stat_col3:
        st.metric("Max Attention", f"{significant_heatmap.max():.4f}")

    with stat_col4:
        st.metric("Mean Attention", f"{significant_heatmap[significant_heatmap > 0].mean():.4f}" if active_voxels > 0 else "0.0000")

    # Clinical interpretation guide
    st.markdown("#### 🎯 Clinical Interpretation Guide")

    if coverage_percentage < 5:
        interpretation_color = "🟢"
        interpretation_text = "**Low AI attention** - Consistent with normal brain patterns"
    elif coverage_percentage < 15:
        interpretation_color = "🟡"
        interpretation_text = "**Moderate AI attention** - Some regions of interest detected"
    else:
        interpretation_color = "🔴"
        interpretation_text = "**High AI attention** - Significant pathological patterns detected"

    st.markdown(f"""
    {interpretation_color} **AI Analysis Summary**: {interpretation_text}

    **Real Gradient-Based Interpretability**:
    - This shows actual neural network gradients, not mock data
    - Computed using backpropagation through the trained model
    - Represents true AI decision-making process

    **How to Interpret**:
    - **Top row**: Original MRI scans for anatomical reference
    - **Middle row**: AI attention maps showing regions that influenced the decision
    - **Bottom row**: Blended overlay for correlation analysis
    - **Bright/hot colors**: Higher AI attention (more diagnostically relevant)

    **Clinical Workflow**:
    1. Review original anatomy (top row)
    2. Identify AI attention regions (middle row)
    3. Correlate findings with clinical presentation
    4. Use overlay view (bottom row) for detailed analysis
    """)

    # Full-screen heatmap viewing
    if st.button("🔍 View Heatmap in Full Screen", key="fullscreen_heatmap"):
        st.session_state.show_fullscreen_heatmap = True
        st.session_state.fullscreen_heatmap_data = heatmap_data
        st.session_state.fullscreen_original_data = original_data
        st.session_state.fullscreen_colorscale = heatmap_colorscale

    # Display fullscreen heatmap if requested
    if st.session_state.get('show_fullscreen_heatmap', False):
        display_fullscreen_heatmap(
            st.session_state.fullscreen_heatmap_data,
            st.session_state.fullscreen_original_data,
            st.session_state.fullscreen_colorscale
        )
        if st.button("❌ Close Full Screen", key="close_fullscreen"):
            st.session_state.show_fullscreen_heatmap = False

    # Add interpretation guide
    st.markdown("""
    **🔍 How to interpret this heatmap:**
    - **Bright/Hot colors**: Brain regions that strongly influenced the prediction
    - **Dark/Cool colors**: Brain regions with minimal influence
    - **Top row**: Original MRI slices for reference
    - **Bottom row**: Importance heatmap showing AI attention
    """)

def display_fullscreen_heatmap(heatmap_data, original_data, colorscale='hot'):
    """
    Display heatmap in full-screen mode with interactive controls.

    Args:
        heatmap_data: SHAP values or saliency map
        original_data: Original MRI data
        colorscale: Color scale for heatmap
    """
    st.markdown("## 🔥 Full-Screen Heatmap Viewer")

    # Normalize heatmap for visualization
    heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

    # Slice selection controls
    col1, col2, col3 = st.columns(3)

    with col1:
        axial_slice = st.slider(
            "Axial Slice",
            0, heatmap_data.shape[2]-1,
            heatmap_data.shape[2]//2,
            key="heatmap_axial_slider"
        )

    with col2:
        coronal_slice = st.slider(
            "Coronal Slice",
            0, heatmap_data.shape[1]-1,
            heatmap_data.shape[1]//2,
            key="heatmap_coronal_slider"
        )

    with col3:
        sagittal_slice = st.slider(
            "Sagittal Slice",
            0, heatmap_data.shape[0]-1,
            heatmap_data.shape[0]//2,
            key="heatmap_sagittal_slider"
        )

    # Display controls
    col1, col2, col3 = st.columns(3)
    with col1:
        heatmap_colorscale = st.selectbox(
            "Heatmap Color Scale",
            ["hot", "viridis", "plasma", "inferno", "magma", "turbo", "jet"],
            index=0 if colorscale == 'hot' else ["hot", "viridis", "plasma", "inferno", "magma", "turbo", "jet"].index(colorscale),
            key="fullscreen_heatmap_colorscale"
        )

    with col2:
        overlay_alpha = st.slider("Overlay Transparency", 0.0, 1.0, 0.7, key="overlay_alpha")

    with col3:
        view_mode = st.selectbox(
            "View Mode",
            ["Side by Side", "Overlay", "Heatmap Only"],
            key="view_mode"
        )

    if view_mode == "Side by Side":
        # Side by side view
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=[
                f"Axial Original (Z={axial_slice})",
                f"Coronal Original (Y={coronal_slice})",
                f"Sagittal Original (X={sagittal_slice})",
                f"Axial Heatmap (Z={axial_slice})",
                f"Coronal Heatmap (Y={coronal_slice})",
                f"Sagittal Heatmap (X={sagittal_slice})"
            ]
        )

        # Original images (top row)
        fig.add_trace(
            go.Heatmap(z=original_data[:, :, axial_slice], colorscale='gray', showscale=False),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=original_data[:, coronal_slice, :], colorscale='gray', showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=original_data[sagittal_slice, :, :], colorscale='gray', showscale=False),
            row=1, col=3
        )

        # Heatmaps (bottom row)
        fig.add_trace(
            go.Heatmap(
                z=heatmap_norm[:, :, axial_slice],
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Importance", x=1.02, len=0.4)
            ),
            row=2, col=1
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[:, coronal_slice, :], colorscale=heatmap_colorscale, showscale=False),
            row=2, col=2
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[sagittal_slice, :, :], colorscale=heatmap_colorscale, showscale=False),
            row=2, col=3
        )

    elif view_mode == "Heatmap Only":
        # Heatmap only view
        fig = make_subplots(
            rows=1, cols=3,
            subplot_titles=[
                f"Axial Heatmap (Z={axial_slice})",
                f"Coronal Heatmap (Y={coronal_slice})",
                f"Sagittal Heatmap (X={sagittal_slice})"
            ]
        )

        fig.add_trace(
            go.Heatmap(
                z=heatmap_norm[:, :, axial_slice],
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Importance", x=1.02, len=0.8)
            ),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[:, coronal_slice, :], colorscale=heatmap_colorscale, showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[sagittal_slice, :, :], colorscale=heatmap_colorscale, showscale=False),
            row=1, col=3
        )

    else:  # Overlay mode
        # Create overlay by blending original and heatmap
        fig = make_subplots(
            rows=1, cols=3,
            subplot_titles=[
                f"Axial Overlay (Z={axial_slice})",
                f"Coronal Overlay (Y={coronal_slice})",
                f"Sagittal Overlay (X={sagittal_slice})"
            ]
        )

        # Create overlays
        axial_overlay = original_data[:, :, axial_slice] * (1 - overlay_alpha) + heatmap_norm[:, :, axial_slice] * overlay_alpha
        coronal_overlay = original_data[:, coronal_slice, :] * (1 - overlay_alpha) + heatmap_norm[:, coronal_slice, :] * overlay_alpha
        sagittal_overlay = original_data[sagittal_slice, :, :] * (1 - overlay_alpha) + heatmap_norm[sagittal_slice, :, :] * overlay_alpha

        fig.add_trace(
            go.Heatmap(
                z=axial_overlay,
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Blended", x=1.02, len=0.8)
            ),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=coronal_overlay, colorscale=heatmap_colorscale, showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=sagittal_overlay, colorscale=heatmap_colorscale, showscale=False),
            row=1, col=3
        )

    fig.update_layout(
        height=800 if view_mode == "Side by Side" else 400,
        showlegend=False,
        title_text=f"Interactive Heatmap Viewer - {view_mode} Mode"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Heatmap statistics
    st.markdown("### 📊 Heatmap Statistics")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Min Importance", f"{heatmap_data.min():.6f}")
    with col2:
        st.metric("Max Importance", f"{heatmap_data.max():.6f}")
    with col3:
        st.metric("Mean Importance", f"{heatmap_data.mean():.6f}")
    with col4:
        st.metric("Std Importance", f"{heatmap_data.std():.6f}")

def display_pdf_status():
    """
    Display PDF generation status and download button at the top of results.
    """
    st.markdown("### 📄 Clinical PDF Report")

    # Debug information (remove in production)
    with st.expander("🔍 Debug Info", expanded=False):
        st.write(f"PDF Status: {st.session_state.get('pdf_status', 'not_set')}")
        st.write(f"Has heatmap: {'heatmap_data' in st.session_state}")
        st.write(f"Has PDF data: {st.session_state.get('pdf_data') is not None}")
        st.write(f"Has PDF filename: {st.session_state.get('pdf_filename') is not None}")
        if 'pdf_filename' in st.session_state:
            st.write(f"PDF filename: {st.session_state.pdf_filename}")
        if 'pdf_data' in st.session_state and st.session_state.pdf_data:
            st.write(f"PDF size: {len(st.session_state.pdf_data)} bytes")

    # Check if all required components are available
    has_heatmap = 'heatmap_data' in st.session_state

    if not has_heatmap:
        # Show waiting for heatmap status
        st.warning("⏳ **Status**: Waiting for brain region analysis to complete...")
        st.info("💡 **Note**: Complete clinical report requires brain region importance analysis. Please generate heatmap first.")

    elif st.session_state.get('pdf_status') == 'ready':
        # Show download button prominently when ready
        st.success("✅ **Status**: Complete clinical report ready for download!")

        # Check if we have the PDF data
        pdf_data = st.session_state.get('pdf_data')
        pdf_filename = st.session_state.get('pdf_filename')

        if pdf_data and pdf_filename:
            # Make download button prominent and always visible
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                st.download_button(
                    label="📄 Download Clinical Report",
                    data=pdf_data,
                    file_name=pdf_filename,
                    mime="application/pdf",
                    type="primary",
                    use_container_width=True,
                    key="top_download_clinical_pdf"
                )

            # Also show a direct download link as backup
            st.markdown("---")
            st.markdown("**Alternative Download Options:**")

            col1, col2 = st.columns(2)
            with col1:
                st.download_button(
                    label="💾 Download PDF (Alternative)",
                    data=pdf_data,
                    file_name=pdf_filename,
                    mime="application/pdf",
                    key="alt_download_pdf"
                )
            with col2:
                st.info(f"📊 Size: {len(pdf_data):,} bytes")

            st.code(f"Filename: {pdf_filename}", language=None)
        else:
            st.error("❌ PDF data not available. Please regenerate.")
            st.write(f"Debug: pdf_data exists: {pdf_data is not None}, pdf_filename exists: {pdf_filename is not None}")

    elif st.session_state.get('pdf_status') == 'generating':
        st.info("⏳ **Status**: Generating comprehensive clinical report...")

    elif st.session_state.get('pdf_status') == 'error':
        st.error("❌ **Status**: PDF generation failed")
        if st.button("🔄 Retry PDF Generation", key="pdf_retry"):
            st.session_state.pdf_status = 'not_started'
            st.rerun()

    else:
        st.info("🔄 **Status**: Ready to generate clinical report after brain analysis...")

    st.markdown("---")

def start_background_pdf_generation():
    """
    Start background PDF generation only after heatmap analysis is complete.
    """
    # Only generate PDF when we have predictions, preprocessed data, AND heatmap data
    if ('predictions' in st.session_state and
        'preprocessed_data' in st.session_state and
        'heatmap_data' in st.session_state and
        st.session_state.pdf_status == 'not_started'):

        st.session_state.pdf_status = 'generating'

        # Use a placeholder to show we're generating
        with st.spinner("Generating comprehensive PDF report with brain region analysis..."):
            # Generate PDF in background
            filename_base = st.session_state.get('uploaded_filename', 'scan')

            try:
                pdf_bytes = generate_pdf_report(
                    st.session_state.preprocessed_data,
                    st.session_state.heatmap_data,  # Now guaranteed to exist
                    st.session_state.predictions,
                    filename_base
                )

                if pdf_bytes:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"Demetify_Clinical_Report_{filename_base}_{timestamp}.pdf"

                    st.session_state.pdf_data = pdf_bytes
                    st.session_state.pdf_filename = filename
                    st.session_state.pdf_status = 'ready'
                else:
                    st.session_state.pdf_status = 'error'

            except Exception as e:
                st.session_state.pdf_status = 'error'
                st.error(f"Background PDF generation failed: {str(e)}")

def generate_clinical_recommendations(predictions, heatmap_data):
    """
    Generate clinical recommendations based on AI analysis results.

    Args:
        predictions: Model predictions dictionary
        heatmap_data: Brain region importance data

    Returns:
        dict: Clinical recommendations and observations
    """
    recommendations = {
        'primary_findings': [],
        'clinical_recommendations': [],
        'follow_up_suggestions': [],
        'technical_notes': []
    }

    # Analyze ADD prediction
    if 'ADD' in predictions:
        add_result = predictions['ADD']
        confidence = add_result['confidence']
        prediction = add_result['prediction']

        if prediction == 1:  # AD detected
            if confidence >= 0.9:
                recommendations['primary_findings'].append(
                    f"High confidence Alzheimer's Disease pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Consider comprehensive neuropsychological evaluation"
                )
                recommendations['clinical_recommendations'].append(
                    "Recommend discussion of findings with patient and family"
                )
                recommendations['follow_up_suggestions'].append(
                    "Consider referral to memory disorders clinic"
                )
            elif confidence >= 0.7:
                recommendations['primary_findings'].append(
                    f"Moderate confidence Alzheimer's Disease pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Correlate with clinical presentation and cognitive testing"
                )
                recommendations['follow_up_suggestions'].append(
                    "Consider follow-up imaging in 6-12 months"
                )
            else:
                recommendations['primary_findings'].append(
                    f"Possible Alzheimer's Disease pattern (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Clinical correlation strongly recommended"
                )
        else:  # Normal cognition
            if confidence >= 0.8:
                recommendations['primary_findings'].append(
                    f"Normal cognitive pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "No immediate cognitive concerns identified on imaging"
                )
            else:
                recommendations['primary_findings'].append(
                    f"Indeterminate pattern (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Clinical correlation and possible repeat imaging recommended"
                )

    # Analyze COG score - just show the number
    if 'COG' in predictions:
        cog_result = predictions['COG']
        score = cog_result['score']

        recommendations['primary_findings'].append(
            f"Cognitive score: {score:.3f}"
        )

    # Analyze heatmap patterns
    if heatmap_data is not None:
        # Calculate heatmap statistics
        max_importance = heatmap_data.max()
        mean_importance = heatmap_data.mean()

        recommendations['technical_notes'].append(
            f"Brain region analysis completed - max importance: {max_importance:.6f}"
        )

        # Identify high-importance regions (simplified analysis)
        high_importance_threshold = max_importance * 0.7
        high_importance_voxels = (heatmap_data > high_importance_threshold).sum()
        total_voxels = heatmap_data.size
        high_importance_percentage = (high_importance_voxels / total_voxels) * 100

        if high_importance_percentage > 5:
            recommendations['technical_notes'].append(
                f"Widespread pattern of importance detected ({high_importance_percentage:.1f}% of brain regions)"
            )
        elif high_importance_percentage > 1:
            recommendations['technical_notes'].append(
                f"Focal pattern of importance detected ({high_importance_percentage:.1f}% of brain regions)"
            )
        else:
            recommendations['technical_notes'].append(
                f"Minimal focal changes detected ({high_importance_percentage:.1f}% of brain regions)"
            )

    # Add standard clinical notes
    recommendations['clinical_recommendations'].append(
        "AI analysis should be interpreted in conjunction with clinical findings"
    )
    recommendations['follow_up_suggestions'].append(
        "Consider multidisciplinary team discussion for complex cases"
    )

    return recommendations

def generate_pdf_report(mri_data, heatmap_data, predictions, filename_base):
    """
    Generate a comprehensive PDF report with MRI analysis results.

    Args:
        mri_data: Preprocessed MRI data
        heatmap_data: SHAP/saliency heatmap data
        predictions: Model predictions
        filename_base: Base filename for the report

    Returns:
        bytes: PDF file as bytes for download
    """
    try:
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_pdf import PdfPages
        from datetime import datetime
        import io

        # Generate clinical recommendations
        recommendations = generate_clinical_recommendations(predictions, heatmap_data)

        # Create PDF in memory
        pdf_buffer = io.BytesIO()

        with PdfPages(pdf_buffer) as pdf:
            # Page 1: Cover page and summary
            fig, ax = plt.subplots(figsize=(8.5, 11))
            ax.axis('off')

            # Header
            ax.text(0.5, 0.95, 'Demetify', fontsize=32, fontweight='bold',
                   ha='center', va='top', color='#2E86AB')
            ax.text(0.5, 0.90, 'AI-Powered Radiologist Assistant', fontsize=16,
                   ha='center', va='top', color='#A23B72')
            ax.text(0.5, 0.85, 'MRI-Based Dementia Assessment Report', fontsize=14,
                   ha='center', va='top')

            # UIUC branding
            ax.text(0.5, 0.80, 'University of Illinois Urbana-Champaign', fontsize=12,
                   ha='center', va='top', style='italic')
            ax.text(0.5, 0.77, 'Project Lead: S. Seshadri', fontsize=10,
                   ha='center', va='top')

            # Report details
            ax.text(0.1, 0.70, f'Report Generated: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}',
                   fontsize=12, fontweight='bold')
            ax.text(0.1, 0.67, f'Scan ID: {filename_base}', fontsize=12)
            ax.text(0.1, 0.64, f'MRI Shape: {mri_data.shape}', fontsize=12)

            # Results summary
            ax.text(0.1, 0.55, 'ASSESSMENT RESULTS', fontsize=16, fontweight='bold', color='#2E86AB')

            if 'ADD' in predictions:
                add_result = predictions['ADD']
                diagnosis = "Alzheimer's Disease Detected" if add_result['prediction'] == 1 else "Normal Cognition"
                confidence = add_result['confidence']

                ax.text(0.1, 0.50, f'Alzheimer\'s Disease Classification:', fontsize=12, fontweight='bold')
                ax.text(0.15, 0.47, f'• Diagnosis: {diagnosis}', fontsize=11)
                ax.text(0.15, 0.44, f'• Confidence: {confidence:.1%}', fontsize=11)

            if 'COG' in predictions:
                cog_result = predictions['COG']
                score = cog_result['score']

                ax.text(0.1, 0.38, f'Cognitive Assessment:', fontsize=12, fontweight='bold')
                ax.text(0.15, 0.35, f'• COG Score: {score:.3f}', fontsize=11)

            # Clinical Summary (concise, no inference text)
            ax.text(0.1, 0.25, 'CLINICAL SUMMARY', fontsize=16, fontweight='bold', color='#2E86AB')

            y_pos = 0.22
            # Only show key findings, no inference text
            key_findings = []

            if 'ADD' in predictions:
                add_result = predictions['ADD']
                if add_result['prediction'] == 1:
                    key_findings.append(f"Alzheimer's Disease pattern detected (confidence: {add_result['confidence']:.1%})")
                else:
                    key_findings.append(f"Normal cognitive pattern (confidence: {add_result['confidence']:.1%})")

            if 'COG' in predictions:
                cog_result = predictions['COG']
                key_findings.append(f"COG Score: {cog_result['score']:.3f}")

            if heatmap_data is not None:
                significant_regions = np.count_nonzero(heatmap_data > heatmap_data.max() * 0.5)
                total_regions = heatmap_data.size
                percentage = (significant_regions / total_regions) * 100
                key_findings.append(f"Brain analysis: {significant_regions:,} significant regions ({percentage:.1f}% of brain)")

            for finding in key_findings:
                ax.text(0.1, y_pos, f'• {finding}', fontsize=11, fontweight='bold')
                y_pos -= 0.03

            # Footer
            ax.text(0.5, 0.05, 'Demetify - Accelerating Radiological Diagnosis',
                   fontsize=10, ha='center', style='italic', color='#666')

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

            # Page 2: MRI visualizations with brain region analysis
            # Note: heatmap_data is guaranteed to exist since we only generate PDF after heatmap completion
            fig, axes = plt.subplots(2, 3, figsize=(11, 8.5))
            fig.suptitle('MRI Analysis with Brain Region Importance', fontsize=16, fontweight='bold')

            mid_x, mid_y, mid_z = mri_data.shape[0]//2, mri_data.shape[1]//2, mri_data.shape[2]//2

            # Original MRI slices (top row)
            axes[0, 0].imshow(mri_data[:, :, mid_z], cmap='gray')
            axes[0, 0].set_title(f'Original MRI - Axial (Z={mid_z})', fontsize=12)
            axes[0, 0].axis('off')

            axes[0, 1].imshow(mri_data[:, mid_y, :], cmap='gray')
            axes[0, 1].set_title(f'Original MRI - Coronal (Y={mid_y})', fontsize=12)
            axes[0, 1].axis('off')

            axes[0, 2].imshow(mri_data[mid_x, :, :], cmap='gray')
            axes[0, 2].set_title(f'Original MRI - Sagittal (X={mid_x})', fontsize=12)
            axes[0, 2].axis('off')

            # Brain region importance heatmaps (bottom row)
            heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

            im1 = axes[1, 0].imshow(heatmap_norm[:, :, mid_z], cmap='hot', alpha=0.8)
            axes[1, 0].set_title('AI Focus Regions - Axial', fontsize=12, color='#2E86AB')
            axes[1, 0].axis('off')

            axes[1, 1].imshow(heatmap_norm[:, mid_y, :], cmap='hot', alpha=0.8)
            axes[1, 1].set_title('AI Focus Regions - Coronal', fontsize=12, color='#2E86AB')
            axes[1, 1].axis('off')

            axes[1, 2].imshow(heatmap_norm[mid_x, :, :], cmap='hot', alpha=0.8)
            axes[1, 2].set_title('AI Focus Regions - Sagittal', fontsize=12, color='#2E86AB')
            axes[1, 2].axis('off')

            # Add colorbar for heatmaps
            cbar = plt.colorbar(im1, ax=axes[1, :], orientation='horizontal',
                        fraction=0.05, pad=0.1, label='AI Attention Score')
            cbar.ax.tick_params(labelsize=10)

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

            # Page 3: Prediction Confidence and Analysis Charts
            fig = plt.figure(figsize=(8.5, 11))

            # Create a grid layout similar to website
            gs = fig.add_gridspec(4, 2, height_ratios=[0.8, 1.5, 1.5, 0.8], hspace=0.3, wspace=0.3)

            # Header
            header_ax = fig.add_subplot(gs[0, :])
            header_ax.axis('off')
            header_ax.text(0.5, 0.5, 'AI Analysis Results & Confidence Metrics',
                          fontsize=18, fontweight='bold', ha='center', va='center', color='#2E86AB')

            # ADD Prediction Chart (like website)
            add_ax = fig.add_subplot(gs[1, 0])
            if 'ADD' in predictions:
                add_result = predictions['ADD']
                confidence = add_result['confidence']
                prediction = add_result['prediction']

                # Create confidence bar chart
                categories = ['Normal', 'Alzheimer\'s Disease']
                confidences = [1-confidence if prediction == 1 else confidence,
                              confidence if prediction == 1 else 1-confidence]
                colors = ['#90EE90' if prediction == 0 else '#FFB6C1',
                         '#FF6B6B' if prediction == 1 else '#D3D3D3']

                bars = add_ax.bar(categories, confidences, color=colors, alpha=0.8)
                add_ax.set_ylim(0, 1)
                add_ax.set_ylabel('Confidence Score', fontsize=10)
                add_ax.set_title('Alzheimer\'s Disease Classification', fontsize=12, fontweight='bold', color='#2E86AB')

                # Add confidence text on bars
                for bar, conf in zip(bars, confidences):
                    height = bar.get_height()
                    add_ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                               f'{conf:.1%}', ha='center', va='bottom', fontweight='bold')

                # Highlight predicted class
                predicted_idx = prediction
                bars[predicted_idx].set_edgecolor('black')
                bars[predicted_idx].set_linewidth(3)

            # COG Score Chart - simple gauge for 0-1 normalized score
            cog_ax = fig.add_subplot(gs[1, 1])
            if 'COG' in predictions:
                cog_result = predictions['COG']
                score = cog_result['score']  # Already normalized 0-1

                # Create gauge-like visualization
                theta = np.linspace(0, np.pi, 100)
                r = 1

                # Background semicircle
                cog_ax.plot(r * np.cos(theta), r * np.sin(theta), 'lightgray', linewidth=8)

                # Score indicator - score is already 0-1 normalized
                score_angle = np.pi * (1 - score)

                # Simple color gradient based on score level
                if score > 0.6:
                    color = '#FF6B6B'  # Red for high scores
                elif score > 0.3:
                    color = '#FFA500'  # Orange for medium scores
                else:
                    color = '#90EE90'  # Green for low scores

                cog_ax.plot([0, r * np.cos(score_angle)], [0, r * np.sin(score_angle)],
                           color=color, linewidth=6, marker='o', markersize=8)

                cog_ax.set_xlim(-1.2, 1.2)
                cog_ax.set_ylim(-0.2, 1.2)
                cog_ax.set_aspect('equal')
                cog_ax.axis('off')
                cog_ax.set_title('COG Score', fontsize=12, fontweight='bold', color='#2E86AB')
                cog_ax.text(0, -0.1, f'{score:.3f}', ha='center', fontsize=14, fontweight='bold')

            # Brain Region Importance Statistics
            stats_ax = fig.add_subplot(gs[2, :])
            if heatmap_data is not None:
                # Calculate statistics for visualization
                flat_heatmap = heatmap_data.flatten()

                # Create histogram of importance values
                n_bins = 50
                counts, bins, patches = stats_ax.hist(flat_heatmap, bins=n_bins, alpha=0.7, color='#FF6B6B', edgecolor='black')

                # Color gradient for histogram
                for i, (patch, bin_val) in enumerate(zip(patches, bins[:-1])):
                    normalized_val = (bin_val - flat_heatmap.min()) / (flat_heatmap.max() - flat_heatmap.min())
                    patch.set_facecolor(plt.cm.hot(normalized_val))

                stats_ax.set_xlabel('Brain Region Importance Score', fontsize=10)
                stats_ax.set_ylabel('Number of Voxels', fontsize=10)
                stats_ax.set_title('Distribution of Brain Region Importance', fontsize=12, fontweight='bold', color='#2E86AB')

                # Add statistics text
                stats_text = f'Max: {flat_heatmap.max():.6f} | Mean: {flat_heatmap.mean():.6f} | Std: {flat_heatmap.std():.6f}'
                stats_ax.text(0.5, 0.95, stats_text, transform=stats_ax.transAxes,
                             ha='center', va='top', fontsize=9, bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            # Clinical Summary
            summary_ax = fig.add_subplot(gs[3, :])
            summary_ax.axis('off')

            # Create summary text like website
            summary_text = "CLINICAL SUMMARY:\n"
            if 'ADD' in predictions:
                add_result = predictions['ADD']
                diagnosis = "Alzheimer's Disease Pattern Detected" if add_result['prediction'] == 1 else "Normal Cognitive Pattern"
                summary_text += f"• {diagnosis} (Confidence: {add_result['confidence']:.1%})\n"

            if 'COG' in predictions:
                cog_result = predictions['COG']
                summary_text += f"• COG Score: {cog_result['score']:.3f}\n"

            summary_text += f"• Brain region analysis completed with {np.count_nonzero(heatmap_data):,} significant regions identified"

            summary_ax.text(0.5, 0.5, summary_text, ha='center', va='center', fontsize=11,
                           bbox=dict(boxstyle='round,pad=0.5', facecolor='#E8F4FD', alpha=0.8),
                           transform=summary_ax.transAxes)

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

        # Return PDF bytes
        pdf_buffer.seek(0)
        pdf_bytes = pdf_buffer.read()
        pdf_buffer.close()

        return pdf_bytes

    except Exception as e:
        st.error(f"❌ Error generating PDF report: {str(e)}")
        import traceback
        st.error(f"Details: {traceback.format_exc()}")
        return None

def display_predictions(predictions):
    """Display prediction results in a medical-friendly format"""

    st.markdown('<h3 class="sub-header">📊 Assessment Results</h3>', unsafe_allow_html=True)
    
    # ADD (Alzheimer's Disease) Results
    if 'ADD' in predictions:
        add_result = predictions['ADD']
        
        st.markdown("### 🧠 Alzheimer's Disease Classification")
        
        # Main prediction
        confidence = add_result['confidence']
        
        # Color coding based on prediction
        if add_result['prediction'] == 1:
            st.markdown(f"""
            <div class="metric-card prediction-high">
                <h4>⚠️ Alzheimer's Disease Detected</h4>
                <p><strong>Confidence:</strong> {confidence:.1%}</p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div class="metric-card prediction-normal">
                <h4>✅ Normal Cognition</h4>
                <p><strong>Confidence:</strong> {confidence:.1%}</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Probability breakdown
        probs = add_result['probabilities']
        
        # Create probability chart
        fig = go.Figure(data=[
            go.Bar(
                x=['Normal', 'Alzheimer\'s Disease'],
                y=[probs['Normal'], probs['AD']],
                marker_color=['#28a745' if probs['Normal'] > probs['AD'] else '#6c757d',
                             '#dc3545' if probs['AD'] > probs['Normal'] else '#6c757d']
            )
        ])
        
        fig.update_layout(
            title="Classification Probabilities",
            yaxis_title="Probability",
            xaxis_title="Diagnosis",
            height=300,
            showlegend=False
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # COG (Cognitive) Results
    if 'COG' in predictions:
        cog_result = predictions['COG']
        
        st.markdown("### 🧮 Cognitive Assessment")
        
        score = cog_result['score']

        # Display cognitive score - clean number only
        st.markdown(f"""
        <div class="metric-card">
            <h4>COG Score: {score:.3f}</h4>
        </div>
        """, unsafe_allow_html=True)
        
        # Cognitive score visualization
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = score,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Cognitive Impairment Score"},
            delta = {'reference': 1.0},
            gauge = {
                'axis': {'range': [None, 3]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 0.5], 'color': "lightgreen"},
                    {'range': [0.5, 1.5], 'color': "yellow"},
                    {'range': [1.5, 3], 'color': "lightcoral"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 1.5
                }
            }
        ))
        
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    # Clinical interpretation
    st.markdown("### 📋 Clinical Summary")
    
    if 'ADD' in predictions and 'COG' in predictions:
        add_pred = predictions['ADD']['prediction']
        cog_score = predictions['COG']['score']
        
        if add_pred == 1 and cog_score > 1.5:
            summary = "⚠️ **High Risk**: Both AD classification and cognitive assessment indicate significant impairment."
        elif add_pred == 1 or cog_score > 1.5:
            summary = "⚠️ **Moderate Risk**: One assessment indicates potential impairment. Further evaluation recommended."
        elif cog_score > 0.5:
            summary = "⚡ **Mild Concern**: Mild cognitive changes detected. Monitoring recommended."
        else:
            summary = "✅ **Normal**: Both assessments indicate normal cognitive function."
        
        st.markdown(f"""
        <div class="metric-card">
            {summary}
        </div>
        """, unsafe_allow_html=True)
    
    # Additional analysis options
    st.markdown("---")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        st.error(f"Application error: {e}")
        st.info("Please refresh the page to restart the application.")
