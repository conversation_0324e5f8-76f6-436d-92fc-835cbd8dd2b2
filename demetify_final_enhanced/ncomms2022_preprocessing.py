"""
MRI Preprocessing Pipeline for ncomms2022 Model
Converts uploaded MRI files to the format expected by the ncomms2022 model.
Expected output: (182, 218, 182) numpy arrays
"""

import numpy as np
import nibabel as nib
from scipy import ndimage
from skimage import transform
import tempfile
import os
from pathlib import Path
import streamlit as st

class NCOMMs2022Preprocessor:
    """
    Preprocessing pipeline to convert MRI files to ncomms2022 model format.
    Based on the MRI processing pipeline from the ncomms2022 repository.
    """
    
    def __init__(self):
        self.target_shape = (182, 218, 182)  # Expected input shape for ncomms2022 model (from test_size method)
        self.target_spacing = (1.5, 1.5, 1.5)  # Target voxel spacing in mm
        
    def load_mri_file(self, file_path_or_bytes, file_type='nii'):
        """
        Load MRI file from either file path or uploaded bytes.
        
        Args:
            file_path_or_bytes: File path string or uploaded file bytes
            file_type: 'nii' or 'npy'
            
        Returns:
            numpy array: Loaded MRI data
        """
        try:
            if file_type == 'npy':
                if isinstance(file_path_or_bytes, str):
                    data = np.load(file_path_or_bytes)
                else:
                    # Handle uploaded bytes
                    with tempfile.NamedTemporaryFile(suffix='.npy', delete=False) as tmp_file:
                        tmp_file.write(file_path_or_bytes.read())
                        tmp_file.flush()
                        data = np.load(tmp_file.name)
                        os.unlink(tmp_file.name)
                return data
                
            elif file_type == 'nii':
                if isinstance(file_path_or_bytes, str):
                    img = nib.load(file_path_or_bytes)
                    data = img.get_fdata()
                    return data, img.affine, img.header
                else:
                    # Handle uploaded bytes - keep file until processing is complete
                    tmp_file = tempfile.NamedTemporaryFile(suffix='.nii', delete=False)
                    try:
                        # Reset file pointer to beginning
                        file_path_or_bytes.seek(0)
                        tmp_file.write(file_path_or_bytes.read())
                        tmp_file.flush()
                        tmp_file.close()  # Close file handle before loading with nibabel

                        # Load with nibabel
                        img = nib.load(tmp_file.name)
                        data = img.get_fdata()

                        return data, img.affine, img.header, tmp_file.name  # Return temp file path for cleanup
                    except Exception as e:
                        # Clean up on error
                        if os.path.exists(tmp_file.name):
                            os.unlink(tmp_file.name)
                        raise e
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
                
        except Exception as e:
            st.error(f"Error loading MRI file: {str(e)}")
            return None
    
    def normalize_intensity(self, data):
        """
        Normalize MRI intensity values.
        
        Args:
            data: Input MRI data
            
        Returns:
            numpy array: Normalized data
        """
        # Remove background (assume background is close to 0)
        mask = data > np.percentile(data, 1)
        
        if np.sum(mask) == 0:
            return data
        
        # Normalize to [0, 1] based on non-background voxels
        data_masked = data[mask]
        p1, p99 = np.percentile(data_masked, [1, 99])
        
        # Clip and normalize
        data_norm = np.clip(data, p1, p99)
        data_norm = (data_norm - p1) / (p99 - p1)
        
        return data_norm.astype(np.float32)
    
    def resample_to_target_shape(self, data):
        """
        Resample MRI data to target shape (121, 145, 121).
        
        Args:
            data: Input MRI data
            
        Returns:
            numpy array: Resampled data
        """
        current_shape = data.shape
        
        # Calculate zoom factors for each dimension
        zoom_factors = [
            self.target_shape[i] / current_shape[i] 
            for i in range(3)
        ]
        
        # Resample using scipy zoom
        resampled_data = ndimage.zoom(data, zoom_factors, order=1)
        
        # Ensure exact target shape (handle rounding errors)
        if resampled_data.shape != self.target_shape:
            # Use skimage resize for exact shape matching
            resampled_data = transform.resize(
                resampled_data, 
                self.target_shape, 
                order=1, 
                preserve_range=True,
                anti_aliasing=True
            )
        
        return resampled_data.astype(np.float32)
    
    def skull_strip_simple(self, data):
        """
        Simple skull stripping using intensity thresholding.
        
        Args:
            data: Input MRI data
            
        Returns:
            numpy array: Skull-stripped data
        """
        # Create a simple brain mask using Otsu-like thresholding
        threshold = np.percentile(data[data > 0], 15)
        mask = data > threshold
        
        # Apply morphological operations to clean up the mask
        from scipy.ndimage import binary_erosion, binary_dilation, binary_fill_holes
        
        # Fill holes and smooth the mask
        mask = binary_fill_holes(mask)
        mask = binary_erosion(mask, iterations=1)
        mask = binary_dilation(mask, iterations=2)
        
        # Apply mask
        data_masked = data * mask
        
        return data_masked
    
    def preprocess_mri(self, file_input, file_type='nii', apply_skull_stripping=True, apply_normalization=False):
        """
        Complete preprocessing pipeline for MRI data.

        Args:
            file_input: File path or uploaded file bytes
            file_type: 'nii' or 'npy'
            apply_skull_stripping: Whether to apply skull stripping
            apply_normalization: Whether to apply intensity normalization (False by default for ncomms2022)

        Returns:
            numpy array: Preprocessed MRI data ready for ncomms2022 model
        """
        # Load the MRI file
        temp_file_path = None
        try:
            if file_type == 'nii':
                result = self.load_mri_file(file_input, file_type)
                if result is None:
                    return None

                if len(result) == 4:  # Uploaded file case
                    data, affine, header, temp_file_path = result
                else:  # File path case
                    data, affine, header = result
            else:
                data = self.load_mri_file(file_input, file_type)
                if data is None:
                    return None

            # Check if data is already in target shape
            if data.shape == self.target_shape:
                st.info(f"✅ MRI data is already in target shape {self.target_shape}")
                # Apply normalization only if requested
                if apply_normalization:
                    st.info("⚡ Applying intensity normalization...")
                    data = self.normalize_intensity(data)
                return data.astype(np.float32)

            st.info(f"📊 Original MRI shape: {data.shape}")

            # Apply skull stripping if requested
            if apply_skull_stripping and file_type == 'nii':
                st.info("🧠 Applying skull stripping...")
                data = self.skull_strip_simple(data)

            # Resample to target shape
            st.info(f"🔄 Resampling to target shape {self.target_shape}...")
            data = self.resample_to_target_shape(data)

            # Normalize intensity only if requested
            if apply_normalization:
                st.info("⚡ Normalizing intensity values...")
                data = self.normalize_intensity(data)

            st.success(f"✅ Preprocessing complete! Final shape: {data.shape}")

            return data

        except Exception as e:
            st.error(f"❌ Error during preprocessing: {str(e)}")
            return None
        finally:
            # Clean up temporary file if it exists
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                    st.info("🗑️ Temporary file cleaned up")
                except:
                    pass  # Ignore cleanup errors
    
    def save_preprocessed_data(self, data, output_path):
        """
        Save preprocessed data as .npy file.
        
        Args:
            data: Preprocessed MRI data
            output_path: Output file path
        """
        np.save(output_path, data)
        st.success(f"💾 Preprocessed data saved to: {output_path}")
    
    def validate_preprocessed_data(self, data):
        """
        Validate that preprocessed data meets ncomms2022 requirements.
        
        Args:
            data: Preprocessed MRI data
            
        Returns:
            bool: True if data is valid
        """
        if data is None:
            st.error("❌ Data is None")
            return False
        
        if data.shape != self.target_shape:
            st.error(f"❌ Invalid shape: {data.shape}, expected: {self.target_shape}")
            return False
        
        if data.dtype != np.float32:
            st.warning(f"⚠️ Data type is {data.dtype}, converting to float32")
            data = data.astype(np.float32)
        
        if np.isnan(data).any():
            st.error("❌ Data contains NaN values")
            return False
        
        if np.isinf(data).any():
            st.error("❌ Data contains infinite values")
            return False
        
        st.success("✅ Preprocessed data validation passed!")
        return True
