#!/usr/bin/env python3
"""
Demetify Deployment Test
Quick test to verify all components are working
"""

import sys
import traceback

def test_python_version():
    """Test Python version compatibility"""
    print("🐍 Testing Python version...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} - Compatible")
        return True
    else:
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} - Requires 3.8+")
        return False

def test_core_imports():
    """Test core package imports"""
    print("📦 Testing core packages...")
    
    packages = [
        ("streamlit", "Streamlit web framework"),
        ("numpy", "NumPy arrays"),
        ("matplotlib", "Matplotlib plotting"),
        ("torch", "PyTorch deep learning"),
        ("scipy", "SciPy scientific computing"),
        ("nibabel", "NiBabel medical imaging")
    ]
    
    failed = []
    
    for package, description in packages:
        try:
            __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} (MISSING)")
            failed.append(package)
    
    return len(failed) == 0, failed

def test_custom_modules():
    """Test custom module imports"""
    print("🔧 Testing custom modules...")
    
    modules = [
        ("ncomms2022_model", "AI model wrapper"),
        ("ncomms2022_preprocessing", "MRI preprocessing"),
        ("models", "Model definitions"),
        ("utils", "Utility functions")
    ]
    
    failed = []
    
    for module, description in modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError as e:
            print(f"❌ {module} - {description} (ERROR: {e})")
            failed.append(module)
        except Exception as e:
            print(f"⚠️  {module} - {description} (WARNING: {e})")
    
    return len(failed) == 0, failed

def test_file_structure():
    """Test required files exist"""
    print("📁 Testing file structure...")
    
    from pathlib import Path
    
    required_files = [
        ("demetify_production.py", "Main production app"),
        ("ncomms2022_model.py", "Model wrapper"),
        ("ncomms2022_preprocessing.py", "Preprocessing"),
        ("requirements.txt", "Dependencies"),
        ("launch_production.py", "Launcher script")
    ]
    
    missing = []
    
    for file, description in required_files:
        if Path(file).exists():
            print(f"✅ {file} - {description}")
        else:
            print(f"❌ {file} - {description} (MISSING)")
            missing.append(file)
    
    return len(missing) == 0, missing

def test_model_files():
    """Test model files exist"""
    print("🤖 Testing model files...")
    
    from pathlib import Path
    
    model_files = [
        "CNN_baseline_new_cross0/ADD_58.pth",
        "CNN_baseline_new_cross0/COG_58.pth", 
        "CNN_baseline_new_cross0/backbone_58.pth",
        "config.json",
        "task_config.json"
    ]
    
    missing = []
    
    for file in model_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (MISSING)")
            missing.append(file)
    
    return len(missing) == 0, missing

def main():
    """Run all tests"""
    print("🧠 Demetify Deployment Test")
    print("=" * 50)
    
    all_passed = True
    
    # Test Python version
    if not test_python_version():
        all_passed = False
    
    print()
    
    # Test core imports
    core_passed, core_failed = test_core_imports()
    if not core_passed:
        all_passed = False
        print(f"⚠️  Missing core packages: {', '.join(core_failed)}")
    
    print()
    
    # Test custom modules
    custom_passed, custom_failed = test_custom_modules()
    if not custom_passed:
        print(f"⚠️  Missing custom modules: {', '.join(custom_failed)}")
    
    print()
    
    # Test file structure
    files_passed, files_missing = test_file_structure()
    if not files_passed:
        all_passed = False
        print(f"⚠️  Missing files: {', '.join(files_missing)}")
    
    print()
    
    # Test model files
    models_passed, models_missing = test_model_files()
    if not models_passed:
        print(f"⚠️  Missing model files: {', '.join(models_missing)}")
    
    print("\n" + "=" * 50)
    
    if all_passed and core_passed and files_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Ready for deployment")
        print("\n🚀 To start the application:")
        print("   python launch_production.py")
        print("   OR")
        print("   streamlit run demetify_production.py")
    else:
        print("⚠️  SOME TESTS FAILED")
        if not core_passed:
            print("❌ Install missing packages: pip install -r requirements.txt")
        if not files_passed:
            print("❌ Ensure you're in the correct directory")
        if not models_passed:
            print("⚠️  Model files missing - AI features may not work")
        
        print("\n🔧 Try running: python launch_production.py")
        print("   (It will attempt to fix missing packages)")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n{'✅ SUCCESS' if success else '⚠️  WARNINGS'}")
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        print("\nFull error:")
        traceback.print_exc()
    
    input("\nPress Enter to exit...")
