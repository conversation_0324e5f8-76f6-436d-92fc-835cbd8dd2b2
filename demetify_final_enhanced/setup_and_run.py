#!/usr/bin/env python3
"""
Demetify Setup and Run Script
Handles installation and launch with proper error handling
"""

import subprocess
import sys
import os
import shutil

def run_command(cmd, description=""):
    """Run a command and return success status."""
    try:
        if description:
            print(f"Running: {description}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return True, result.stdout
        else:
            return False, result.stderr
    except Exception as e:
        return False, str(e)

def check_python():
    """Check Python installation."""
    python_cmd = None
    for cmd in ['python3', 'python']:
        if shutil.which(cmd):
            python_cmd = cmd
            break
    
    if not python_cmd:
        print("ERROR: Python not found. Please install Python 3.8+")
        return None
    
    # Check version
    success, output = run_command(f"{python_cmd} --version")
    if success:
        print(f"Found: {output.strip()}")
        return python_cmd
    else:
        print(f"ERROR: Could not check Python version: {output}")
        return None

def install_pip(python_cmd):
    """Install pip if not available."""
    success, _ = run_command(f"{python_cmd} -m pip --version")
    if success:
        print("pip is available")
        return True
    
    print("pip not found, attempting to install...")
    
    # Try to install pip
    success, output = run_command(f"{python_cmd} -m ensurepip --upgrade")
    if success:
        print("pip installed successfully")
        return True
    
    # Alternative method
    print("Trying alternative pip installation...")
    success, output = run_command("curl https://bootstrap.pypa.io/get-pip.py | python3")
    if success:
        print("pip installed via get-pip.py")
        return True
    
    print("ERROR: Could not install pip. Please install manually:")
    print("  sudo apt update && sudo apt install python3-pip")
    return False

def install_dependencies(python_cmd):
    """Install required dependencies."""
    print("Installing Demetify dependencies...")
    
    # Essential packages
    packages = [
        "streamlit",
        "torch",
        "torchvision",
        "numpy",
        "pandas",
        "plotly",
        "nibabel",
        "scipy",
        "matplotlib",
        "scikit-image",
        "scikit-learn",
        "pillow",
        "opencv-python"
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        success, output = run_command(f"{python_cmd} -m pip install {package}")
        if not success:
            print(f"WARNING: Failed to install {package}: {output}")
    
    print("Dependencies installation completed")

def launch_demetify(python_cmd):
    """Launch the Demetify application."""
    print("\nLaunching Demetify...")
    print("Open your browser to: http://localhost:8501")
    print("Press Ctrl+C to stop the application")
    print("-" * 50)
    
    try:
        subprocess.run([python_cmd, "-m", "streamlit", "run", "ncomms2022_frontend.py"])
    except KeyboardInterrupt:
        print("\nDemetify stopped by user")
    except Exception as e:
        print(f"ERROR launching Demetify: {e}")

def main():
    print("Demetify Setup and Launch Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("ncomms2022_frontend.py"):
        print("ERROR: ncomms2022_frontend.py not found")
        print("Please run this script from the demetify_deployment directory")
        return 1
    
    # Check Python
    python_cmd = check_python()
    if not python_cmd:
        return 1
    
    # Install pip if needed
    if not install_pip(python_cmd):
        return 1
    
    # Install dependencies
    install_dependencies(python_cmd)
    
    # Launch application
    launch_demetify(python_cmd)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
