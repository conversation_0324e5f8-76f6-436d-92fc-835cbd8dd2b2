#!/usr/bin/env python3
"""
Demetify Deployment Script
Automated setup and launch for Demetify AI-Powered Radiologist Assistant
"""

import os
import sys
import subprocess
import platform
import argparse

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("ERROR: Python 3.8 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"OK: Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import streamlit
        import torch
        import numpy
        import plotly
        print("OK: Core dependencies found")
        return True
    except ImportError as e:
        print(f"ERROR: Missing dependencies: {e}")
        return False

def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements_ncomms2022.txt"
        ])
        print("OK: Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("ERROR: Failed to install dependencies")
        return False

def check_model_files():
    """Check if model files exist."""
    model_dir = "CNN_baseline_new_cross0"
    if os.path.exists(model_dir):
        print("OK: Model files found")
        return True
    else:
        print("ERROR: Model files not found")
        return False

def launch_application(port=8501, host="localhost"):
    """Launch the Streamlit application."""
    print(f"Launching Demetify on {host}:{port}")
    print(f"   Open browser to: http://{host}:{port}")

    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "ncomms2022_frontend.py",
            "--server.port", str(port),
            "--server.address", host
        ])
    except KeyboardInterrupt:
        print("\nDemetify stopped by user")
    except Exception as e:
        print(f"ERROR: Error launching application: {e}")

def main():
    parser = argparse.ArgumentParser(description="Deploy Demetify AI-Powered Radiologist Assistant")
    parser.add_argument("--port", type=int, default=8501, help="Port to run on (default: 8501)")
    parser.add_argument("--host", default="localhost", help="Host to bind to (default: localhost)")
    parser.add_argument("--skip-install", action="store_true", help="Skip dependency installation")
    parser.add_argument("--check-only", action="store_true", help="Only check system requirements")

    args = parser.parse_args()

    print("Demetify Deployment Script")
    print("=" * 50)
    
    # Check system requirements
    print("\n1. Checking system requirements...")
    if not check_python_version():
        return 1
    
    print(f"   OS: {platform.system()} {platform.release()}")
    
    # Check model files
    print("\n2. Checking model files...")
    if not check_model_files():
        print("   Please ensure model files are in the correct location")
        return 1
    
    # Check dependencies
    print("\n3. Checking dependencies...")
    deps_ok = check_dependencies()
    
    if args.check_only:
        if deps_ok:
            print("\nOK: All checks passed! Ready to deploy.")
            return 0
        else:
            print("\nERROR: Some checks failed. Install dependencies first.")
            return 1
    
    # Install dependencies if needed
    if not deps_ok and not args.skip_install:
        print("\n4. Installing dependencies...")
        if not install_dependencies():
            return 1
    elif args.skip_install:
        print("\n4. Skipping dependency installation...")
    
    # Launch application
    print("\n5. Launching application...")
    launch_application(args.port, args.host)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
