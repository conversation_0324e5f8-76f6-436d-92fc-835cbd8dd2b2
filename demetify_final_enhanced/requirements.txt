# Demetify - Medical Imaging AI Frontend Requirements
# Production-ready dependencies for clinical deployment

# Core Framework
streamlit>=1.28.0
numpy>=1.24.0
pandas>=2.0.0

# Medical Imaging
nibabel>=5.1.0
scipy>=1.11.0
scikit-image>=0.21.0

# Visualization
matplotlib>=3.7.0
plotly>=5.15.0

# Machine Learning
torch>=2.0.0
torchvision>=0.15.0

# PDF Generation
reportlab>=4.0.0
Pillow>=10.0.0

# File Handling
pathlib2>=2.3.7

# Optional: GPU Support (uncomment if CUDA available)
# torch>=2.0.0+cu118
# torchvision>=0.15.0+cu118

# Development Dependencies (optional)
# pytest>=7.4.0
# black>=23.0.0
# flake8>=6.0.0
