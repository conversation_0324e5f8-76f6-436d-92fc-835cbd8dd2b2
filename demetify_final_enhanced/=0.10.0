Requirement already satisfied: torchvision in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (0.22.1)
Requirement already satisfied: numpy in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torchvision) (2.2.5)
Requirement already satisfied: torch==2.7.1 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torchvision) (2.7.1)
Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torchvision) (11.2.1)
Requirement already satisfied: filelock in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (3.18.0)
Requirement already satisfied: typing-extensions>=4.10.0 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (4.12.2)
Requirement already satisfied: setuptools in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (79.0.1)
Requirement already satisfied: sympy>=1.13.3 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (1.13.3)
Requirement already satisfied: networkx in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (3.4.2)
Requirement already satisfied: jinja2 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (3.1.6)
Requirement already satisfied: fsspec in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (2025.3.0)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (12.6.77)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (12.6.77)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (12.6.80)
Requirement already satisfied: nvidia-cudnn-cu12==9.5.1.17 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (9.5.1.17)
Requirement already satisfied: nvidia-cublas-cu12==12.6.4.1 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (12.6.4.1)
Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (11.3.0.4)
Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (10.3.7.77)
Requirement already satisfied: nvidia-cusolver-cu12==11.7.1.2 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (11.7.1.2)
Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (12.5.4.2)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (0.6.3)
Requirement already satisfied: nvidia-nccl-cu12==2.26.2 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (2.26.2)
Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (12.6.77)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (12.6.85)
Requirement already satisfied: nvidia-cufile-cu12==1.11.1.6 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (1.11.1.6)
Requirement already satisfied: triton==3.3.1 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (3.3.1)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from sympy>=1.13.3->torch==2.7.1->torchvision) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in /mnt/c/common_folder/miniconda3/envs/abstract/lib/python3.13/site-packages (from jinja2->torch==2.7.1->torchvision) (3.0.2)
