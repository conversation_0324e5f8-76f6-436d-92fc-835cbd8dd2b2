"""
Simplified ncomms2022 Model Wrapper for Frontend Inference
Adapted from the original model_wrappers.py for Streamlit frontend use.
"""

import os
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from pathlib import Path
import streamlit as st
import tempfile
import json

# Import the model components
from models import _CNN_Bone, MLP, Model
from backends.ResNet import resnet18
from backends.DenseNet import Dense<PERSON>
from backends.SENet import SENet
from utils import read_json, remove_module

class MultiTaskModel(nn.Module):
    """
    Custom multi-task model that combines backbone CNN with multiple task-specific MLPs.
    """
    def __init__(self, backbone, mlp_dict, tasks):
        super(MultiTaskModel, self).__init__()
        self.backbone = backbone
        self.mlp_dict = nn.ModuleDict(mlp_dict)
        self.tasks = tasks

    def forward(self, x):
        # Extract features using backbone
        features = self.backbone(x)

        # Apply task-specific MLPs
        outputs = {}
        for task in self.tasks:
            if task in self.mlp_dict:
                outputs[task] = self.mlp_dict[task](features)

        return outputs

class NCOMMs2022Model:
    """
    Simplified wrapper for ncomms2022 model inference in Streamlit frontend.
    """
    
    def __init__(self, device='cpu'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.tasks = ['ADD', 'COG']  # Fixed tasks for ncomms2022
        self.model = None
        self.backbone = None
        self.MLPs = {}
        self.task_config = None
        self.model_loaded = False
        
        # Load task configuration
        self.load_task_config()
        
    def load_task_config(self):
        """Load task configuration from task_config.json"""
        try:
            with open('task_config.json', 'r') as f:
                self.task_config = json.load(f)
            st.success("✅ Task configuration loaded successfully")
        except Exception as e:
            st.error(f"❌ Error loading task configuration: {str(e)}")
            # Fallback configuration
            self.task_config = {
                "backbone": {
                    "model": "CNN",
                    "fil_num": 20,
                    "drop_rate": 0,
                    "lr": 0.001,
                    "epochs": 101
                },
                "ADD": {
                    "fil_num": 30,
                    "drop_rate": 0.5,
                    "out_size": 2,
                    "type": "cla"
                },
                "COG": {
                    "fil_num": 100,
                    "drop_rate": 0.5,
                    "out_size": 1,
                    "type": "reg"
                }
            }
    
    def initialize_model(self):
        """Initialize the model architecture"""
        try:
            # Initialize backbone CNN with config dictionary
            backbone_config = self.task_config['backbone']
            self.backbone = _CNN_Bone(backbone_config)

            # Initialize task-specific MLPs
            for task in self.tasks:
                if task in self.task_config:
                    config = self.task_config[task]
                    self.MLPs[task] = MLP(
                        in_size=self.backbone.size,
                        config=config  # Pass the entire config dictionary
                    )
            
            # Create a custom multi-task model
            self.model = MultiTaskModel(
                backbone=self.backbone,
                mlp_dict=self.MLPs,
                tasks=self.tasks
            )
            
            # Move to device
            self.model = self.model.to(self.device)
            self.model.eval()
            
            st.success("✅ Model architecture initialized successfully")
            return True

        except Exception as e:
            error_msg = f"❌ Error initializing model: {str(e)}"
            st.error(error_msg)
            print(error_msg)  # Also print to console for debugging
            import traceback
            traceback.print_exc()
            return False
    
    def load_pretrained_weights(self, checkpoint_dir):
        """
        Load pretrained weights from checkpoint directory.
        
        Args:
            checkpoint_dir: Path to checkpoint directory containing .pth files
        """
        try:
            if not self.model:
                if not self.initialize_model():
                    return False
            
            checkpoint_path = Path(checkpoint_dir)
            if not checkpoint_path.exists():
                st.error(f"❌ Checkpoint directory not found: {checkpoint_dir}")
                return False
            
            # Load backbone weights
            backbone_file = checkpoint_path / "backbone_58.pth"
            if backbone_file.exists():
                backbone_state = torch.load(backbone_file, map_location=self.device)
                backbone_state = remove_module(backbone_state)
                self.backbone.load_state_dict(backbone_state)
                st.success("✅ Backbone weights loaded")
            else:
                st.warning("⚠️ Backbone weights file not found")
            
            # Load task-specific MLP weights
            for task in self.tasks:
                task_file = checkpoint_path / f"{task}_58.pth"
                if task_file.exists():
                    task_state = torch.load(task_file, map_location=self.device)
                    task_state = remove_module(task_state)
                    self.MLPs[task].load_state_dict(task_state)
                    st.success(f"✅ {task} MLP weights loaded")
                else:
                    st.warning(f"⚠️ {task} weights file not found")
            
            self.model_loaded = True
            st.success("🎉 All model weights loaded successfully!")
            return True
            
        except Exception as e:
            st.error(f"❌ Error loading pretrained weights: {str(e)}")
            return False
    
    def predict_single(self, mri_data):
        """
        Make prediction on a single MRI scan.
        
        Args:
            mri_data: Preprocessed MRI data (121, 145, 121)
            
        Returns:
            dict: Predictions for ADD and COG tasks
        """
        if not self.model_loaded:
            st.error("❌ Model not loaded. Please load pretrained weights first.")
            return None
        
        try:
            # Prepare input tensor
            input_tensor = torch.from_numpy(mri_data).float()
            input_tensor = input_tensor.unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions
            input_tensor = input_tensor.to(self.device)
            
            # Make prediction
            with torch.no_grad():
                predictions = self.model(input_tensor)
            
            results = {}
            
            # Process ADD prediction (classification)
            if 'ADD' in predictions:
                add_logits = predictions['ADD'].cpu().numpy()[0]
                add_probs = torch.softmax(predictions['ADD'], dim=1).cpu().numpy()[0]
                add_pred = np.argmax(add_probs)
                
                results['ADD'] = {
                    'prediction': int(add_pred),
                    'probability': float(add_probs[add_pred]),
                    'probabilities': {
                        'Normal': float(add_probs[0]),
                        'AD': float(add_probs[1])
                    },
                    'confidence': float(np.max(add_probs))
                }
            
            # Process COG prediction (regression)
            if 'COG' in predictions:
                cog_score = predictions['COG'].cpu().numpy()[0][0]

                # Clamp score to reasonable range
                cog_score = max(-1.0, min(4.0, cog_score))

                results['COG'] = {
                    'score': float(cog_score),
                    'interpretation': self.interpret_cog_score(cog_score),
                    'confidence': self.get_cog_confidence_level(cog_score),
                    'scale_info': 'Score range: 0-3 (0-0.5: Normal, 0.5-1.5: MCI, >1.5: Impaired)'
                }
            
            return results
            
        except Exception as e:
            error_msg = f"❌ Error during prediction: {str(e)}"
            st.error(error_msg)
            print(error_msg)  # Also print to console for debugging
            import traceback
            traceback.print_exc()
            return None
    
    def interpret_cog_score(self, score):
        """
        Interpret COG score based on model training thresholds.

        The COG model was trained as a regression task with thresholds:
        - Normal Cognition (NC): score < 0.5
        - Mild Cognitive Impairment (MCI): 0.5 <= score <= 1.5
        - Dementia/Cognitive Impairment (DE): score > 1.5

        Args:
            score: COG regression score (typically 0-3 range)

        Returns:
            str: Interpretation of the score
        """
        # Clamp extreme values to reasonable range
        score = max(-1.0, min(4.0, score))

        if score < 0.5:
            return "Normal Cognition"
        elif score <= 1.5:
            return "Mild Cognitive Impairment"
        else:
            return "Cognitive Impairment"

    def get_cog_confidence_level(self, score):
        """
        Get confidence level for COG score interpretation based on distance from thresholds.

        Args:
            score: COG regression score

        Returns:
            float: Confidence level (0.1 to 1.0)
        """
        # Distance from nearest threshold determines confidence
        if score < 0.5:
            # Normal range - confidence based on how far below 0.5
            confidence = min(1.0, abs(score - 0.5) / 0.5 + 0.3)
        elif score <= 1.5:
            # MCI range - lower confidence due to uncertainty
            dist_to_normal = abs(score - 0.5)
            dist_to_dementia = abs(score - 1.5)
            min_distance = min(dist_to_normal, dist_to_dementia)
            confidence = max(0.3, min(0.7, min_distance / 0.5))
        else:
            # Dementia range - confidence based on how far above 1.5
            confidence = min(1.0, (score - 1.5) / 1.0 + 0.5)

        return max(0.3, min(1.0, confidence))  # 30% to 100% confidence

    def normalize_cog_for_visualization(self, score):
        """
        Normalize COG score for gauge visualization (0-1 range).

        Args:
            score: COG regression score

        Returns:
            float: Normalized score for visualization (0-1)
        """
        # Expected range is roughly 0-3, normalize to 0-1
        return max(0, min(1, score / 3.0))
    
    def get_model_info(self):
        """Get information about the loaded model"""
        if not self.model_loaded:
            return "Model not loaded"
        
        info = {
            "Tasks": self.tasks,
            "Device": self.device,
            "Backbone": "CNN with ResNet-like architecture",
            "ADD Task": "Binary classification (Normal vs AD)",
            "COG Task": "Regression (Cognitive score)",
            "Input Shape": "(1, 1, 121, 145, 121)",
            "Model Loaded": self.model_loaded
        }
        
        return info

class ModelManager:
    """
    Manages multiple model checkpoints and provides easy access.
    """
    
    def __init__(self, checkpoint_base_dir=None):
        # Auto-detect checkpoint directory
        if checkpoint_base_dir is None:
            # Try deployment package structure first
            if Path("CNN_baseline_new_cross0").exists():
                checkpoint_base_dir = "."
            # Fall back to original structure
            elif Path("ncomms2022/checkpoint_dir").exists():
                checkpoint_base_dir = "ncomms2022/checkpoint_dir"
            else:
                checkpoint_base_dir = "."

        self.checkpoint_base_dir = Path(checkpoint_base_dir)
        self.available_models = self.scan_available_models()
    
    def scan_available_models(self):
        """Scan for available pretrained models"""
        models = {}
        
        if not self.checkpoint_base_dir.exists():
            st.warning(f"⚠️ Checkpoint directory not found: {self.checkpoint_base_dir}")
            return models
        
        for model_dir in self.checkpoint_base_dir.iterdir():
            if model_dir.is_dir():
                # Check if required files exist
                required_files = ["backbone_58.pth", "ADD_58.pth", "COG_58.pth"]
                if all((model_dir / f).exists() for f in required_files):
                    models[model_dir.name] = str(model_dir)
        
        return models
    
    def get_available_models(self):
        """Get list of available model names"""
        return list(self.available_models.keys())
    
    def get_model_path(self, model_name):
        """Get path to specific model checkpoint"""
        return self.available_models.get(model_name)
    
    def load_model(self, model_name, device='cpu'):
        """
        Load a specific model by name.
        
        Args:
            model_name: Name of the model to load
            device: Device to load model on
            
        Returns:
            NCOMMs2022Model: Loaded model instance
        """
        if model_name not in self.available_models:
            st.error(f"❌ Model '{model_name}' not found")
            return None
        
        model = NCOMMs2022Model(device=device)
        checkpoint_path = self.available_models[model_name]
        
        if model.load_pretrained_weights(checkpoint_path):
            return model
        else:
            return None
