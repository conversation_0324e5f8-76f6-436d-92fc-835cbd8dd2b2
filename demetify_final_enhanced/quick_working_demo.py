"""
Quick Working Demetify Demo - For Immediate Use
All features working with original display + individual fullscreen
"""

import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
import torch
from ncomms2022_model import NCOMMs2022Model
from ncomms2022_preprocessing import NCOMMs2022Preprocessor

# Page config
st.set_page_config(page_title="Demetify - Working Demo", layout="wide")

# Initialize session state
if 'model' not in st.session_state:
    st.session_state.model = None
if 'preprocessed_data' not in st.session_state:
    st.session_state.preprocessed_data = None
if 'predictions' not in st.session_state:
    st.session_state.predictions = None
if 'heatmap_data' not in st.session_state:
    st.session_state.heatmap_data = None

# Header
st.title("🧠 Demetify - Working Demo")
st.markdown("**AI-Powered Medical Imaging for Alzheimer's Detection**")

# Sidebar
with st.sidebar:
    st.header("🔧 Controls")
    
    # Model loading
    if st.session_state.model is None:
        if st.button("🚀 Load AI Model"):
            with st.spinner("Loading model..."):
                try:
                    model = NCOMMs2022Model()
                    if model.initialize_model():
                        if model.load_pretrained_weights("CNN_baseline_new_cross0"):
                            st.session_state.model = model
                            st.success("✅ Model loaded!")
                        else:
                            st.error("❌ Failed to load weights")
                    else:
                        st.error("❌ Failed to initialize model")
                except Exception as e:
                    st.error(f"❌ Error: {e}")
    else:
        st.success("✅ Model Ready")

# Main content
if st.session_state.model is not None:
    
    # File upload
    st.header("📁 Upload MRI")
    uploaded_file = st.file_uploader("Choose MRI file", type=['nii', 'npy'])
    
    if uploaded_file is not None:
        with st.spinner("Processing MRI..."):
            try:
                preprocessor = NCOMMs2022Preprocessor()
                data = preprocessor.preprocess_mri(uploaded_file, 
                                                 file_type='npy' if uploaded_file.name.endswith('.npy') else 'nii')
                st.session_state.preprocessed_data = data
                st.success("✅ MRI processed!")
            except Exception as e:
                st.error(f"❌ Processing failed: {e}")
        
        if st.session_state.preprocessed_data is not None:
            data = st.session_state.preprocessed_data
            
            # Display basic info
            st.header("📊 MRI Information")
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Shape", f"{data.shape}")
            with col2:
                st.metric("Min", f"{data.min():.3f}")
            with col3:
                st.metric("Max", f"{data.max():.3f}")
            with col4:
                st.metric("Mean", f"{data.mean():.3f}")
            
            # Original 3-slice display
            st.header("🧠 MRI Slices")
            
            # Slice controls
            col1, col2, col3 = st.columns(3)
            with col1:
                axial_slice = st.slider("Axial", 0, data.shape[2]-1, data.shape[2]//2)
            with col2:
                coronal_slice = st.slider("Coronal", 0, data.shape[1]-1, data.shape[1]//2)
            with col3:
                sagittal_slice = st.slider("Sagittal", 0, data.shape[0]-1, data.shape[0]//2)
            
            # Display 3 slices
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            # Axial
            axes[0].imshow(data[:, :, axial_slice], cmap='gray', origin='lower')
            axes[0].set_title(f'Axial - Slice {axial_slice}')
            axes[0].axis('off')
            
            # Coronal
            axes[1].imshow(data[:, coronal_slice, :], cmap='gray', origin='lower')
            axes[1].set_title(f'Coronal - Slice {coronal_slice}')
            axes[1].axis('off')
            
            # Sagittal
            axes[2].imshow(data[sagittal_slice, :, :], cmap='gray', origin='lower')
            axes[2].set_title(f'Sagittal - Slice {sagittal_slice}')
            axes[2].axis('off')
            
            plt.tight_layout()
            st.pyplot(fig)
            plt.close()
            
            # Individual fullscreen buttons
            st.header("🖥️ Individual Fullscreen")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("🖥️ Axial Fullscreen"):
                    st.subheader(f"Axial Fullscreen - Slice {axial_slice}")
                    fig_full = plt.figure(figsize=(12, 10), dpi=150)
                    plt.imshow(data[:, :, axial_slice], cmap='gray', origin='lower')
                    plt.title(f'Axial MRI - Slice {axial_slice}', fontsize=16, fontweight='bold')
                    plt.axis('off')
                    st.pyplot(fig_full)
                    plt.close()
            
            with col2:
                if st.button("🖥️ Coronal Fullscreen"):
                    st.subheader(f"Coronal Fullscreen - Slice {coronal_slice}")
                    fig_full = plt.figure(figsize=(12, 10), dpi=150)
                    plt.imshow(data[:, coronal_slice, :], cmap='gray', origin='lower')
                    plt.title(f'Coronal MRI - Slice {coronal_slice}', fontsize=16, fontweight='bold')
                    plt.axis('off')
                    st.pyplot(fig_full)
                    plt.close()
            
            with col3:
                if st.button("🖥️ Sagittal Fullscreen"):
                    st.subheader(f"Sagittal Fullscreen - Slice {sagittal_slice}")
                    fig_full = plt.figure(figsize=(12, 10), dpi=150)
                    plt.imshow(data[sagittal_slice, :, :], cmap='gray', origin='lower')
                    plt.title(f'Sagittal MRI - Slice {sagittal_slice}', fontsize=16, fontweight='bold')
                    plt.axis('off')
                    st.pyplot(fig_full)
                    plt.close()
            
            # AI Analysis
            st.header("🤖 AI Analysis")
            if st.button("🧠 Run AI Analysis"):
                with st.spinner("Running AI analysis..."):
                    try:
                        predictions = st.session_state.model.predict_single(data)
                        st.session_state.predictions = predictions
                        
                        # Display results
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.subheader("🎯 Alzheimer's Detection")
                            add_results = predictions['ADD']
                            if add_results['prediction'] == 1:
                                st.error(f"⚠️ **Alzheimer's Detected**: {add_results['probabilities']['AD']:.1%}")
                            else:
                                st.success(f"✅ **Normal Cognition**: {add_results['probabilities']['Normal']:.1%}")
                        
                        with col2:
                            st.subheader("🧮 Cognitive Score")
                            cog_score = predictions['COG']['score']
                            st.metric("COG Score", f"{cog_score:.3f}")
                        
                    except Exception as e:
                        st.error(f"❌ Analysis failed: {e}")
            
            # Simple heatmap generation
            if st.session_state.predictions is not None:
                st.header("🔥 Brain Heatmap")
                if st.button("🧠 Generate Heatmap"):
                    with st.spinner("Generating heatmap..."):
                        try:
                            # Simple gradient-based heatmap
                            input_tensor = torch.from_numpy(data).float().unsqueeze(0).unsqueeze(0)
                            input_tensor.requires_grad_(True)
                            
                            predictions = st.session_state.model.model(input_tensor)
                            add_probs = torch.softmax(predictions['ADD'], dim=1)
                            target_output = add_probs[:, 1]
                            target_output.backward()
                            
                            gradients = input_tensor.grad
                            heatmap = torch.abs(gradients[0, 0]).numpy()
                            
                            # Normalize
                            heatmap = (heatmap - heatmap.min()) / (heatmap.max() - heatmap.min())
                            st.session_state.heatmap_data = heatmap

                        except Exception as e:
                            st.error(f"❌ Heatmap generation failed: {e}")

            # Always display heatmap if available - PREVENTS COLLAPSE
            if st.session_state.heatmap_data is not None:
                heatmap = st.session_state.heatmap_data

                # Display heatmap overlays
                st.subheader("🔥 Heatmap Overlays")

                opacity = st.slider("Overlay Opacity", 0.0, 1.0, 0.5, 0.1, key="stable_opacity")

                fig, axes = plt.subplots(1, 3, figsize=(15, 5))

                # Axial overlay
                axes[0].imshow(data[:, :, axial_slice], cmap='gray', origin='lower')
                axes[0].imshow(heatmap[:, :, axial_slice], cmap='hot', alpha=opacity, origin='lower')
                axes[0].set_title(f'Axial Overlay - Slice {axial_slice}')
                axes[0].axis('off')

                # Coronal overlay
                axes[1].imshow(data[:, coronal_slice, :], cmap='gray', origin='lower')
                axes[1].imshow(heatmap[:, coronal_slice, :], cmap='hot', alpha=opacity, origin='lower')
                axes[1].set_title(f'Coronal Overlay - Slice {coronal_slice}')
                axes[1].axis('off')

                # Sagittal overlay
                axes[2].imshow(data[sagittal_slice, :, :], cmap='gray', origin='lower')
                axes[2].imshow(heatmap[sagittal_slice, :, :], cmap='hot', alpha=opacity, origin='lower')
                axes[2].set_title(f'Sagittal Overlay - Slice {sagittal_slice}')
                axes[2].axis('off')

                plt.tight_layout()
                st.pyplot(fig)
                plt.close()

                # Individual heatmap fullscreen
                st.subheader("🔥 Individual Heatmap Fullscreen")
                hcol1, hcol2, hcol3 = st.columns(3)

                with hcol1:
                                if st.button("🔥 Axial Heatmap"):
                                    st.subheader(f"Axial Heatmap - Slice {axial_slice}")
                                    fig_heat = plt.figure(figsize=(12, 10), dpi=150)
                                    plt.imshow(data[:, :, axial_slice], cmap='gray', origin='lower')
                                    plt.imshow(heatmap[:, :, axial_slice], cmap='hot', alpha=0.6, origin='lower')
                                    plt.title(f'Axial Heatmap Overlay - Slice {axial_slice}', fontsize=16, fontweight='bold')
                                    plt.colorbar(label='AI Attention')
                                    plt.axis('off')
                                    st.pyplot(fig_heat)
                                    plt.close()
                            
                with hcol2:
                                if st.button("🔥 Coronal Heatmap"):
                                    st.subheader(f"Coronal Heatmap - Slice {coronal_slice}")
                                    fig_heat = plt.figure(figsize=(12, 10), dpi=150)
                                    plt.imshow(data[:, coronal_slice, :], cmap='gray', origin='lower')
                                    plt.imshow(heatmap[:, coronal_slice, :], cmap='hot', alpha=0.6, origin='lower')
                                    plt.title(f'Coronal Heatmap Overlay - Slice {coronal_slice}', fontsize=16, fontweight='bold')
                                    plt.colorbar(label='AI Attention')
                                    plt.axis('off')
                                    st.pyplot(fig_heat)
                                    plt.close()
                            
                with hcol3:
                                if st.button("🔥 Sagittal Heatmap"):
                                    st.subheader(f"Sagittal Heatmap - Slice {sagittal_slice}")
                                    fig_heat = plt.figure(figsize=(12, 10), dpi=150)
                                    plt.imshow(data[sagittal_slice, :, :], cmap='gray', origin='lower')
                                    plt.imshow(heatmap[sagittal_slice, :, :], cmap='hot', alpha=0.6, origin='lower')
                                    plt.title(f'Sagittal Heatmap Overlay - Slice {sagittal_slice}', fontsize=16, fontweight='bold')
                                    plt.colorbar(label='AI Attention')
                                    plt.axis('off')
                                    st.pyplot(fig_heat)
                                    plt.close()

else:
    st.warning("⚠️ Please load the AI model first using the sidebar")

st.markdown("---")
st.markdown("**Demetify** - University of Illinois at Urbana-Champaign | Project Lead: S. Seshadri")
