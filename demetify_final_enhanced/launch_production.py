#!/usr/bin/env python3
"""
Demetify Production Launcher
Clean, production-ready launcher for the medical imaging frontend
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_requirements():
    """Install required packages"""
    print("🔧 Installing dependencies...")
    
    try:
        # Try to install from requirements.txt
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError:
        print("⚠️  Failed to install from requirements.txt, trying alternative...")
        
        # Fallback to essential packages only
        essential_packages = [
            "streamlit>=1.28.0",
            "numpy>=1.24.0", 
            "matplotlib>=3.7.0",
            "torch>=2.0.0",
            "scipy>=1.11.0",
            "nibabel>=5.1.0",
            "reportlab>=4.0.0",
            "Pillow>=10.0.0"
        ]
        
        try:
            for package in essential_packages:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            print("✅ Essential packages installed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install packages: {e}")
            return False

def check_files():
    """Check if required files exist"""
    required_files = [
        "demetify_production.py",
        "ncomms2022_model.py",
        "ncomms2022_preprocessing.py"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        print("   Make sure you're in the correct directory")
        return False

    print("✅ All required files found")
    return True

def test_imports():
    """Test if all imports work correctly"""
    print("🔍 Testing imports...")

    try:
        # Test basic imports
        import streamlit
        import numpy
        import matplotlib
        import torch

        # Test custom imports
        sys.path.append('.')
        from ncomms2022_model import NCOMMs2022Model
        from ncomms2022_preprocessing import NCOMMs2022Preprocessor

        print("✅ All imports working correctly")
        return True

    except ImportError as e:
        print(f"⚠️  Import warning: {e}")
        print("   Continuing anyway - may work at runtime")
        return True
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def launch_application():
    """Launch the Streamlit application"""
    print("🚀 Starting Demetify...")
    print("   Opening in your default browser...")
    print("   Press Ctrl+C to stop the application")
    print("-" * 50)
    
    try:
        # Launch streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "demetify_production.py",
            "--server.headless", "true",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n🛑 Application stopped by user")
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print("🧠 Demetify - Medical Imaging AI Frontend")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return False
    
    # Check required files
    if not check_files():
        input("Press Enter to exit...")
        return False

    # Install dependencies
    if not install_requirements():
        print("⚠️  Continuing with existing packages...")

    # Test imports
    if not test_imports():
        print("⚠️  Import issues detected, but continuing...")

    print("\n" + "=" * 50)
    print("🎯 Ready to launch!")
    print("   Application will open at: http://localhost:8501")
    print("   For best experience, use fullscreen mode")
    print("=" * 50)
    
    # Small delay for user to read
    time.sleep(2)
    
    # Launch application
    return launch_application()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\nPress Enter to exit...")
    except KeyboardInterrupt:
        print("\n🛑 Launcher interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
