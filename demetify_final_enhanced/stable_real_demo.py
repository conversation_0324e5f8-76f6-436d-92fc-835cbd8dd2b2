"""
Stable Real Demetify Demo - NO COLLAPSE, REAL MODEL PREDICTIONS
All state preserved, real model outputs, no resets
"""

import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
import torch
from ncomms2022_model import NCOMMs2022Model
from ncomms2022_preprocessing import NCOMMs2022Preprocessor

# Page config
st.set_page_config(page_title="Demetify - Stable Real Demo", layout="wide")

# CRITICAL: Initialize ALL session state at the top to prevent resets
def initialize_session_state():
    """Initialize all session state variables to prevent any resets"""
    defaults = {
        'model': None,
        'model_loaded': False,
        'preprocessed_data': None,
        'uploaded_filename': None,
        'predictions': None,
        'predictions_made': False,
        'heatmap_data': None,
        'heatmap_generated': False,
        'current_axial_slice': None,
        'current_coronal_slice': None,
        'current_sagittal_slice': None,
        'show_axial_fullscreen': False,
        'show_coronal_fullscreen': False,
        'show_sagittal_fullscreen': False,
        'show_axial_heatmap': False,
        'show_coronal_heatmap': False,
        'show_sagittal_heatmap': False,
        'opacity_value': 0.5
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

# Initialize session state first
initialize_session_state()

# Header
st.title("🧠 Demetify - Stable Real Demo")
st.markdown("**Real AI Model Predictions - No Interface Collapse**")

# Sidebar
with st.sidebar:
    st.header("🔧 System Status")
    
    # Model loading with real verification
    if not st.session_state.model_loaded:
        if st.button("🚀 Load Real AI Model", key="load_model_btn"):
            with st.spinner("Loading real AI model..."):
                try:
                    model = NCOMMs2022Model()
                    st.write("✅ Model instance created")
                    
                    if model.initialize_model():
                        st.write("✅ Model architecture initialized")
                        
                        if model.load_pretrained_weights("CNN_baseline_new_cross0"):
                            st.session_state.model = model
                            st.session_state.model_loaded = True
                            st.success("✅ REAL MODEL LOADED SUCCESSFULLY!")
                            st.write(f"Model device: {model.device}")
                            st.write(f"Model tasks: {model.tasks}")
                            st.write(f"Model loaded: {model.model_loaded}")
                        else:
                            st.error("❌ Failed to load pretrained weights")
                    else:
                        st.error("❌ Failed to initialize model architecture")
                except Exception as e:
                    st.error(f"❌ Model loading error: {e}")
                    import traceback
                    st.error(traceback.format_exc())
    else:
        st.success("✅ Real AI Model Ready")
        st.write(f"Device: {st.session_state.model.device}")
        st.write(f"Tasks: {st.session_state.model.tasks}")

# Main content - only show if model is loaded
if st.session_state.model_loaded:
    
    # File upload section
    st.header("📁 MRI Upload")
    uploaded_file = st.file_uploader("Choose MRI file", type=['nii', 'npy'], key="mri_uploader")
    
    if uploaded_file is not None and st.session_state.preprocessed_data is None:
        with st.spinner("Processing MRI with real preprocessor..."):
            try:
                preprocessor = NCOMMs2022Preprocessor()
                data = preprocessor.preprocess_mri(uploaded_file, 
                                                 file_type='npy' if uploaded_file.name.endswith('.npy') else 'nii')
                st.session_state.preprocessed_data = data
                st.session_state.uploaded_filename = uploaded_file.name
                
                # Set default slice positions
                st.session_state.current_axial_slice = data.shape[2] // 2
                st.session_state.current_coronal_slice = data.shape[1] // 2
                st.session_state.current_sagittal_slice = data.shape[0] // 2
                
                st.success("✅ MRI processed with real preprocessor!")
                st.write(f"Data shape: {data.shape}")
                st.write(f"Data type: {data.dtype}")
                st.write(f"Data range: {data.min():.3f} to {data.max():.3f}")
                
            except Exception as e:
                st.error(f"❌ Real preprocessing failed: {e}")
                import traceback
                st.error(traceback.format_exc())
    
    # Show MRI analysis if data is loaded
    if st.session_state.preprocessed_data is not None:
        data = st.session_state.preprocessed_data
        
        # MRI Information
        st.header("📊 Real MRI Data Information")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Shape", f"{data.shape}")
        with col2:
            st.metric("Min Value", f"{data.min():.4f}")
        with col3:
            st.metric("Max Value", f"{data.max():.4f}")
        with col4:
            st.metric("Mean Value", f"{data.mean():.4f}")
        
        # Slice controls - always visible, values from session state
        st.header("🧠 MRI Slice Viewer")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            axial_slice = st.slider("Axial Slice", 0, data.shape[2]-1, 
                                   st.session_state.current_axial_slice, key="axial_slider")
            st.session_state.current_axial_slice = axial_slice
            
        with col2:
            coronal_slice = st.slider("Coronal Slice", 0, data.shape[1]-1, 
                                    st.session_state.current_coronal_slice, key="coronal_slider")
            st.session_state.current_coronal_slice = coronal_slice
            
        with col3:
            sagittal_slice = st.slider("Sagittal Slice", 0, data.shape[0]-1, 
                                     st.session_state.current_sagittal_slice, key="sagittal_slider")
            st.session_state.current_sagittal_slice = sagittal_slice
        
        # Always display the 3-slice view
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Axial
        axes[0].imshow(data[:, :, axial_slice], cmap='gray', origin='lower')
        axes[0].set_title(f'Axial - Slice {axial_slice}', fontweight='bold')
        axes[0].axis('off')
        
        # Coronal
        axes[1].imshow(data[:, coronal_slice, :], cmap='gray', origin='lower')
        axes[1].set_title(f'Coronal - Slice {coronal_slice}', fontweight='bold')
        axes[1].axis('off')
        
        # Sagittal
        axes[2].imshow(data[sagittal_slice, :, :], cmap='gray', origin='lower')
        axes[2].set_title(f'Sagittal - Slice {sagittal_slice}', fontweight='bold')
        axes[2].axis('off')
        
        plt.tight_layout()
        st.pyplot(fig)
        plt.close()
        
        # Individual fullscreen buttons
        st.header("🖥️ Individual Fullscreen Options")
        fcol1, fcol2, fcol3 = st.columns(3)
        
        with fcol1:
            if st.button("🖥️ Axial Fullscreen", key="axial_fullscreen_btn"):
                st.session_state.show_axial_fullscreen = True
        
        with fcol2:
            if st.button("🖥️ Coronal Fullscreen", key="coronal_fullscreen_btn"):
                st.session_state.show_coronal_fullscreen = True
        
        with fcol3:
            if st.button("🖥️ Sagittal Fullscreen", key="sagittal_fullscreen_btn"):
                st.session_state.show_sagittal_fullscreen = True
        
        # Display fullscreen views if requested
        if st.session_state.show_axial_fullscreen:
            st.subheader(f"🖥️ Axial Fullscreen - Slice {axial_slice}")
            fig_full = plt.figure(figsize=(12, 10), dpi=200)
            plt.imshow(data[:, :, axial_slice], cmap='gray', origin='lower')
            plt.title(f'Axial MRI - Slice {axial_slice}', fontsize=18, fontweight='bold')
            plt.axis('off')
            st.pyplot(fig_full)
            plt.close()
            if st.button("❌ Close Axial Fullscreen", key="close_axial"):
                st.session_state.show_axial_fullscreen = False
                st.rerun()
        
        if st.session_state.show_coronal_fullscreen:
            st.subheader(f"🖥️ Coronal Fullscreen - Slice {coronal_slice}")
            fig_full = plt.figure(figsize=(12, 10), dpi=200)
            plt.imshow(data[:, coronal_slice, :], cmap='gray', origin='lower')
            plt.title(f'Coronal MRI - Slice {coronal_slice}', fontsize=18, fontweight='bold')
            plt.axis('off')
            st.pyplot(fig_full)
            plt.close()
            if st.button("❌ Close Coronal Fullscreen", key="close_coronal"):
                st.session_state.show_coronal_fullscreen = False
                st.rerun()
        
        if st.session_state.show_sagittal_fullscreen:
            st.subheader(f"🖥️ Sagittal Fullscreen - Slice {sagittal_slice}")
            fig_full = plt.figure(figsize=(12, 10), dpi=200)
            plt.imshow(data[sagittal_slice, :, :], cmap='gray', origin='lower')
            plt.title(f'Sagittal MRI - Slice {sagittal_slice}', fontsize=18, fontweight='bold')
            plt.axis('off')
            st.pyplot(fig_full)
            plt.close()
            if st.button("❌ Close Sagittal Fullscreen", key="close_sagittal"):
                st.session_state.show_sagittal_fullscreen = False
                st.rerun()
        
        # AI Analysis section
        st.header("🤖 Real AI Model Analysis")
        
        if not st.session_state.predictions_made:
            if st.button("🧠 Run REAL AI Analysis", key="run_analysis_btn"):
                with st.spinner("Running REAL AI model inference..."):
                    try:
                        # REAL MODEL PREDICTION
                        predictions = st.session_state.model.predict_single(data)
                        st.session_state.predictions = predictions
                        st.session_state.predictions_made = True
                        
                        st.success("✅ REAL AI analysis completed!")
                        st.write("Raw predictions from real model:")
                        st.json(predictions)
                        
                    except Exception as e:
                        st.error(f"❌ REAL AI analysis failed: {e}")
                        import traceback
                        st.error(traceback.format_exc())
        
        # Always display predictions if available
        if st.session_state.predictions_made and st.session_state.predictions is not None:
            st.subheader("🎯 REAL AI Model Results")
            
            predictions = st.session_state.predictions
            
            # Display raw predictions for verification
            with st.expander("🔍 Raw Model Output (Verify Real Predictions)"):
                st.json(predictions)
            
            # Display formatted results
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("#### 🎯 Alzheimer's Detection (REAL)")
                add_results = predictions['ADD']
                
                if add_results['prediction'] == 1:  # AD detected
                    st.error(f"⚠️ **Alzheimer's Detected**: {add_results['probabilities']['AD']:.1%} confidence")
                    st.write(f"Normal probability: {add_results['probabilities']['Normal']:.1%}")
                else:  # Normal cognition
                    st.success(f"✅ **Normal Cognition**: {add_results['probabilities']['Normal']:.1%} confidence")
                    st.write(f"AD probability: {add_results['probabilities']['AD']:.1%}")
            
            with col2:
                st.markdown("#### 🧮 Cognitive Score (REAL)")
                cog_score = predictions['COG']['score']
                st.metric("COG Score", f"{cog_score:.4f}")
                st.write(f"Score interpretation: {st.session_state.model.interpret_cog_score(cog_score * 3.0)}")

        # Heatmap Analysis section
        if st.session_state.predictions_made:
            st.header("🔥 Real Brain Heatmap Analysis")

            if not st.session_state.heatmap_generated:
                if st.button("🧠 Generate REAL Brain Heatmap", key="generate_heatmap_btn"):
                    with st.spinner("Generating REAL brain heatmap from model gradients..."):
                        try:
                            # REAL GRADIENT-BASED HEATMAP
                            input_tensor = torch.from_numpy(data).float().unsqueeze(0).unsqueeze(0)
                            input_tensor = input_tensor.to(st.session_state.model.device)
                            input_tensor.requires_grad_(True)

                            # Forward pass through REAL model
                            st.session_state.model.model.eval()
                            predictions_tensor = st.session_state.model.model(input_tensor)

                            # Use AD probability for gradient computation
                            add_probs = torch.softmax(predictions_tensor['ADD'], dim=1)
                            target_output = add_probs[:, 1]  # AD probability

                            # Compute gradients
                            target_output.backward()
                            gradients = input_tensor.grad

                            if gradients is not None:
                                # Use gradient magnitude as heatmap
                                heatmap = torch.abs(gradients[0, 0]).cpu().numpy()

                                # Apply smoothing
                                from scipy.ndimage import gaussian_filter
                                heatmap = gaussian_filter(heatmap, sigma=1.0)

                                # Normalize
                                heatmap = (heatmap - heatmap.min()) / (heatmap.max() - heatmap.min())

                                st.session_state.heatmap_data = heatmap
                                st.session_state.heatmap_generated = True

                                st.success("✅ REAL brain heatmap generated from model gradients!")
                                st.write(f"Heatmap shape: {heatmap.shape}")
                                st.write(f"Heatmap range: {heatmap.min():.4f} to {heatmap.max():.4f}")
                            else:
                                st.error("❌ Failed to compute gradients")

                        except Exception as e:
                            st.error(f"❌ REAL heatmap generation failed: {e}")
                            import traceback
                            st.error(traceback.format_exc())

            # Always display heatmap if available - PREVENTS COLLAPSE
            if st.session_state.heatmap_generated and st.session_state.heatmap_data is not None:
                heatmap = st.session_state.heatmap_data

                st.subheader("🔥 REAL Brain Heatmap Overlays")

                # Stable opacity control
                opacity = st.slider("Heatmap Overlay Opacity", 0.0, 1.0,
                                  st.session_state.opacity_value, 0.1,
                                  key="stable_opacity_slider")
                st.session_state.opacity_value = opacity

                # Always display heatmap overlays
                fig_heat, axes_heat = plt.subplots(1, 3, figsize=(15, 5))

                # Axial overlay
                axes_heat[0].imshow(data[:, :, axial_slice], cmap='gray', origin='lower')
                axes_heat[0].imshow(heatmap[:, :, axial_slice], cmap='hot', alpha=opacity, origin='lower')
                axes_heat[0].set_title(f'Axial Heatmap - Slice {axial_slice}', fontweight='bold')
                axes_heat[0].axis('off')

                # Coronal overlay
                axes_heat[1].imshow(data[:, coronal_slice, :], cmap='gray', origin='lower')
                axes_heat[1].imshow(heatmap[:, coronal_slice, :], cmap='hot', alpha=opacity, origin='lower')
                axes_heat[1].set_title(f'Coronal Heatmap - Slice {coronal_slice}', fontweight='bold')
                axes_heat[1].axis('off')

                # Sagittal overlay
                axes_heat[2].imshow(data[sagittal_slice, :, :], cmap='gray', origin='lower')
                axes_heat[2].imshow(heatmap[sagittal_slice, :, :], cmap='hot', alpha=opacity, origin='lower')
                axes_heat[2].set_title(f'Sagittal Heatmap - Slice {sagittal_slice}', fontweight='bold')
                axes_heat[2].axis('off')

                plt.tight_layout()
                st.pyplot(fig_heat)
                plt.close()

                # Individual heatmap fullscreen options
                st.subheader("🔥 Individual Heatmap Fullscreen")
                hcol1, hcol2, hcol3 = st.columns(3)

                with hcol1:
                    if st.button("🔥 Axial Heatmap Fullscreen", key="axial_heatmap_btn"):
                        st.session_state.show_axial_heatmap = True

                with hcol2:
                    if st.button("🔥 Coronal Heatmap Fullscreen", key="coronal_heatmap_btn"):
                        st.session_state.show_coronal_heatmap = True

                with hcol3:
                    if st.button("🔥 Sagittal Heatmap Fullscreen", key="sagittal_heatmap_btn"):
                        st.session_state.show_sagittal_heatmap = True

                # Display heatmap fullscreens if requested
                if st.session_state.show_axial_heatmap:
                    st.subheader(f"🔥 Axial Heatmap Fullscreen - Slice {axial_slice}")
                    fig_heat_full = plt.figure(figsize=(12, 10), dpi=200)
                    plt.imshow(data[:, :, axial_slice], cmap='gray', origin='lower')
                    plt.imshow(heatmap[:, :, axial_slice], cmap='hot', alpha=0.6, origin='lower')
                    plt.title(f'Axial Brain Heatmap - Slice {axial_slice}', fontsize=18, fontweight='bold')
                    plt.colorbar(label='AI Attention Intensity')
                    plt.axis('off')
                    st.pyplot(fig_heat_full)
                    plt.close()
                    if st.button("❌ Close Axial Heatmap", key="close_axial_heatmap"):
                        st.session_state.show_axial_heatmap = False
                        st.rerun()

                if st.session_state.show_coronal_heatmap:
                    st.subheader(f"🔥 Coronal Heatmap Fullscreen - Slice {coronal_slice}")
                    fig_heat_full = plt.figure(figsize=(12, 10), dpi=200)
                    plt.imshow(data[:, coronal_slice, :], cmap='gray', origin='lower')
                    plt.imshow(heatmap[:, coronal_slice, :], cmap='hot', alpha=0.6, origin='lower')
                    plt.title(f'Coronal Brain Heatmap - Slice {coronal_slice}', fontsize=18, fontweight='bold')
                    plt.colorbar(label='AI Attention Intensity')
                    plt.axis('off')
                    st.pyplot(fig_heat_full)
                    plt.close()
                    if st.button("❌ Close Coronal Heatmap", key="close_coronal_heatmap"):
                        st.session_state.show_coronal_heatmap = False
                        st.rerun()

                if st.session_state.show_sagittal_heatmap:
                    st.subheader(f"🔥 Sagittal Heatmap Fullscreen - Slice {sagittal_slice}")
                    fig_heat_full = plt.figure(figsize=(12, 10), dpi=200)
                    plt.imshow(data[sagittal_slice, :, :], cmap='gray', origin='lower')
                    plt.imshow(heatmap[sagittal_slice, :, :], cmap='hot', alpha=0.6, origin='lower')
                    plt.title(f'Sagittal Brain Heatmap - Slice {sagittal_slice}', fontsize=18, fontweight='bold')
                    plt.colorbar(label='AI Attention Intensity')
                    plt.axis('off')
                    st.pyplot(fig_heat_full)
                    plt.close()
                    if st.button("❌ Close Sagittal Heatmap", key="close_sagittal_heatmap"):
                        st.session_state.show_sagittal_heatmap = False
                        st.rerun()

else:
    st.warning("⚠️ Please load the real AI model first using the sidebar")

st.markdown("---")
st.markdown("**Demetify - Real Model Demo** | UIUC | Project Lead: S. Seshadri")
