"""
Demetify - Medical Imaging AI Frontend
Production-Ready Clinical Interface for Alzheimer's Disease Detection

Author: Demetify Team
Version: 1.0.0
License: MIT
"""

import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import torch
from pathlib import Path

# Import custom modules
from ncomms2022_model import NCOMMs2022Model
from ncomms2022_preprocessing import NCOMMs2022Preprocessor

# Page configuration
st.set_page_config(
    page_title="Demetify - AI-Powered Medical Imaging",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for professional medical interface
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        color: #1f4e79;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    .sub-header {
        font-size: 1.8rem;
        font-weight: 600;
        color: #2c5aa0;
        margin: 1.5rem 0 1rem 0;
        border-bottom: 2px solid #e1e5e9;
        padding-bottom: 0.5rem;
    }
    .metric-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }
    .clinical-info {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 8px 8px 0;
    }
    .stButton > button {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables for stable operation."""
    defaults = {
        'current_model': None,
        'preprocessed_data': None,
        'predictions': None,
        'heatmap_data': None,
        'uploaded_filename': None,
        'pdf_data': None,
        'pdf_filename': None,
        'overlay_opacity_stable': 0.5,
        'mpl_opacity_stable': 0.6,
        'main_opacity_value': 0.6,
        'heatmap_generated': False
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

def display_header():
    """Display the main application header."""
    st.markdown('<h1 class="main-header">🧠 Demetify</h1>', unsafe_allow_html=True)
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <p style="font-size: 1.2rem; color: #666;">
            AI-Powered Medical Imaging for Alzheimer's Disease Detection
        </p>
        <p style="color: #888;">
            University of Illinois at Urbana-Champaign | Project Lead: S. Seshadri
        </p>
    </div>
    """, unsafe_allow_html=True)

def load_model():
    """Load the NCOMMs2022 model with error handling."""
    try:
        with st.spinner("Loading AI model..."):
            model = NCOMMs2022Model()

            # Initialize model architecture
            if not model.initialize_model():
                st.error("❌ Failed to initialize model architecture")
                return False

            # Load pretrained weights
            if not model.load_pretrained_weights("CNN_baseline_new_cross0"):
                st.error("❌ Failed to load pretrained weights")
                return False

            st.session_state.current_model = model
            st.success("✅ AI model loaded successfully")
            return True
    except Exception as e:
        st.error(f"❌ Failed to load model: {str(e)}")
        import traceback
        st.error(f"Details: {traceback.format_exc()}")
        return False

def upload_and_preprocess():
    """Handle MRI file upload and preprocessing."""
    st.markdown('<h2 class="sub-header">📁 MRI Upload & Preprocessing</h2>', unsafe_allow_html=True)
    
    uploaded_file = st.file_uploader(
        "Upload MRI scan (.nii or .npy format)",
        type=['nii', 'npy'],
        help="Upload a preprocessed MRI scan for analysis"
    )
    
    if uploaded_file is not None:
        st.session_state.uploaded_filename = uploaded_file.name.split('.')[0]
        
        try:
            with st.spinner("Processing MRI data..."):
                # Preprocess the data using the class-based approach
                preprocessor = NCOMMs2022Preprocessor()
                preprocessed_data = preprocessor.preprocess_mri(uploaded_file,
                                                              file_type='npy' if uploaded_file.name.endswith('.npy') else 'nii')
                st.session_state.preprocessed_data = preprocessed_data

                st.success("✅ MRI data processed successfully")
                return preprocessed_data
                
        except Exception as e:
            st.error(f"❌ Error processing MRI data: {str(e)}")
            return None
    
    return None

def display_individual_fullscreen(image_data, title="Fullscreen Viewer", slice_idx=None, 
                                orientation="axial", colormap="gray", is_heatmap=False):
    """
    Display individual image in fullscreen with proper aspect ratio.
    
    Args:
        image_data: 2D or 3D image data array
        title: Title for the viewer
        slice_idx: Slice index if 3D data
        orientation: Orientation if 3D data
        colormap: Colormap to use
        is_heatmap: Whether this is a heatmap
    """
    st.markdown(f"## 🖥️ {title}")
    
    # Handle 3D data by extracting the specified slice
    if len(image_data.shape) == 3:
        if slice_idx is None:
            slice_idx = image_data.shape[2] // 2
        
        if orientation == "axial":
            display_data = image_data[:, :, slice_idx]
        elif orientation == "coronal":
            display_data = image_data[:, slice_idx, :]
        elif orientation == "sagittal":
            display_data = image_data[slice_idx, :, :]
        else:
            display_data = image_data[:, :, slice_idx]
    else:
        display_data = image_data
    
    # Calculate proper aspect ratio to prevent stretching
    img_height, img_width = display_data.shape
    aspect_ratio = img_width / img_height
    
    # Ultra high-resolution settings
    plt.rcParams['figure.dpi'] = 300
    plt.rcParams['savefig.dpi'] = 300
    
    # Calculate optimal figure size maintaining aspect ratio
    max_width = 30
    max_height = 20
    
    if aspect_ratio > max_width / max_height:
        fig_width = max_width
        fig_height = max_width / aspect_ratio
    else:
        fig_height = max_height
        fig_width = max_height * aspect_ratio
    
    # Create high-resolution figure
    fig, ax = plt.subplots(figsize=(fig_width, fig_height), dpi=300)
    
    # Display with maximum quality settings
    im = ax.imshow(
        display_data, 
        cmap=colormap, 
        interpolation='lanczos',
        aspect='equal',
        origin='lower'
    )
    
    # Add colorbar for heatmaps
    if is_heatmap or colormap != 'gray':
        cbar = plt.colorbar(im, ax=ax, orientation='horizontal', 
                           fraction=0.046, pad=0.08, shrink=0.8)
        cbar.set_label('Intensity', fontsize=18, fontweight='bold')
        cbar.ax.tick_params(labelsize=14)
    
    # Remove axes
    ax.axis('off')
    
    # Set title
    if len(image_data.shape) == 3:
        fig.suptitle(f'{title} - {orientation.title()} Slice {slice_idx}', 
                    fontsize=28, fontweight='bold', y=0.95)
    else:
        fig.suptitle(title, fontsize=28, fontweight='bold', y=0.95)
    
    plt.tight_layout()
    
    # Display
    st.pyplot(fig, dpi=300, use_container_width=True, clear_figure=True)
    
    # Statistics
    stat_col1, stat_col2, stat_col3, stat_col4 = st.columns(4)
    
    with stat_col1:
        st.metric("Resolution", f"{img_width}×{img_height}")
    with stat_col2:
        st.metric("Aspect Ratio", f"{aspect_ratio:.2f}:1")
    with stat_col3:
        st.metric("DPI", "300")
    with stat_col4:
        st.metric("Quality", "Ultra High")
    
    plt.close(fig)

def generate_heatmap(model, mri_data):
    """Generate gradient-based heatmap for model interpretability."""
    try:
        # Prepare input for gradient analysis
        input_tensor = torch.from_numpy(mri_data).float()
        input_tensor = input_tensor.unsqueeze(0).unsqueeze(0)
        input_tensor = input_tensor.to(model.device)
        input_tensor.requires_grad_(True)

        # Forward pass
        model.model.eval()
        predictions = model.model(input_tensor)

        # Use AD probability for gradient computation
        add_probs = torch.softmax(predictions['ADD'], dim=1)
        target_output = add_probs[:, 1]

        # Compute gradients
        target_output.backward()
        gradients = input_tensor.grad

        if gradients is None:
            return None

        # Use gradient magnitude as heatmap
        heatmap = torch.abs(gradients[0, 0]).cpu().numpy()

        # Apply smoothing
        from scipy.ndimage import gaussian_filter
        heatmap = gaussian_filter(heatmap, sigma=1.0)

        return heatmap

    except Exception as e:
        st.error(f"Heatmap generation failed: {str(e)}")
        return None

def display_heatmap_analysis(heatmap_data, original_data):
    """Display heatmap analysis with individual fullscreen options."""
    st.markdown("### 🔥 Brain Region Importance")

    # Normalize heatmap
    heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

    # Opacity control
    opacity = st.slider("Overlay Opacity", 0.0, 1.0, 0.5, 0.05, key="heatmap_opacity")

    # Slice selection
    col1, col2, col3 = st.columns(3)

    with col1:
        axial_slice = st.slider("Axial Slice", 0, heatmap_data.shape[2]-1,
                              heatmap_data.shape[2]//2, key="heatmap_axial")
        if st.button("🔥 Axial Heatmap Fullscreen", key="axial_heatmap_fullscreen"):
            display_individual_fullscreen(heatmap_norm[:, :, axial_slice],
                                        "Axial Brain Heatmap", colormap="hot", is_heatmap=True)
        if st.button("🖥️ Axial Overlay Fullscreen", key="axial_overlay_fullscreen"):
            # Create overlay
            mri_slice = original_data[:, :, axial_slice]
            heat_slice = heatmap_norm[:, :, axial_slice]
            overlay = mri_slice * (1-opacity) + heat_slice * opacity
            display_individual_fullscreen(overlay, "Axial MRI + Heatmap Overlay", colormap="hot")

    with col2:
        coronal_slice = st.slider("Coronal Slice", 0, heatmap_data.shape[1]-1,
                                heatmap_data.shape[1]//2, key="heatmap_coronal")
        if st.button("🔥 Coronal Heatmap Fullscreen", key="coronal_heatmap_fullscreen"):
            display_individual_fullscreen(heatmap_norm[:, coronal_slice, :],
                                        "Coronal Brain Heatmap", colormap="hot", is_heatmap=True)
        if st.button("🖥️ Coronal Overlay Fullscreen", key="coronal_overlay_fullscreen"):
            # Create overlay
            mri_slice = original_data[:, coronal_slice, :]
            heat_slice = heatmap_norm[:, coronal_slice, :]
            overlay = mri_slice * (1-opacity) + heat_slice * opacity
            display_individual_fullscreen(overlay, "Coronal MRI + Heatmap Overlay", colormap="hot")

    with col3:
        sagittal_slice = st.slider("Sagittal Slice", 0, heatmap_data.shape[0]-1,
                                 heatmap_data.shape[0]//2, key="heatmap_sagittal")
        if st.button("🔥 Sagittal Heatmap Fullscreen", key="sagittal_heatmap_fullscreen"):
            display_individual_fullscreen(heatmap_norm[sagittal_slice, :, :],
                                        "Sagittal Brain Heatmap", colormap="hot", is_heatmap=True)
        if st.button("🖥️ Sagittal Overlay Fullscreen", key="sagittal_overlay_fullscreen"):
            # Create overlay
            mri_slice = original_data[sagittal_slice, :, :]
            heat_slice = heatmap_norm[sagittal_slice, :, :]
            overlay = mri_slice * (1-opacity) + heat_slice * opacity
            display_individual_fullscreen(overlay, "Sagittal MRI + Heatmap Overlay", colormap="hot")

    # Statistics
    st.markdown("#### 📊 Heatmap Statistics")
    stat_col1, stat_col2, stat_col3, stat_col4 = st.columns(4)

    with stat_col1:
        st.metric("Max Importance", f"{heatmap_data.max():.4f}")
    with stat_col2:
        st.metric("Mean Importance", f"{heatmap_data.mean():.4f}")
    with stat_col3:
        st.metric("Active Regions", f"{np.sum(heatmap_norm > 0.1)}")
    with stat_col4:
        st.metric("Data Shape", f"{heatmap_data.shape}")

def main():
    """Main application function."""
    initialize_session_state()
    display_header()
    
    # Sidebar
    with st.sidebar:
        st.markdown("### 🔧 System Status")
        
        # Model loading
        if st.session_state.current_model is None:
            if st.button("🚀 Load AI Model", type="primary"):
                load_model()
        else:
            st.success("✅ AI Model Ready")
        
        # System info
        st.markdown("### 📊 System Information")
        st.info(f"""
        **Version**: 1.0.0  
        **Status**: Production Ready  
        **Updated**: {datetime.now().strftime('%Y-%m-%d')}
        """)
    
    # Main content
    if st.session_state.current_model is not None:
        # File upload and preprocessing
        preprocessed_data = upload_and_preprocess()
        
        if preprocessed_data is not None:
            # Display basic MRI information
            st.markdown('<h2 class="sub-header">🧠 MRI Analysis</h2>', unsafe_allow_html=True)
            
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Shape", f"{preprocessed_data.shape}")
            with col2:
                st.metric("Min Value", f"{preprocessed_data.min():.3f}")
            with col3:
                st.metric("Max Value", f"{preprocessed_data.max():.3f}")
            with col4:
                st.metric("Mean Value", f"{preprocessed_data.mean():.3f}")
            
            # AI Inference
            st.markdown('<h2 class="sub-header">🤖 AI Analysis</h2>', unsafe_allow_html=True)

            if st.button("🧠 Run AI Analysis", type="primary"):
                with st.spinner("Running AI inference..."):
                    try:
                        predictions = st.session_state.current_model.predict_single(preprocessed_data)
                        st.session_state.predictions = predictions

                        # Display results
                        col1, col2 = st.columns(2)

                        with col1:
                            st.markdown("#### 🎯 Alzheimer's Detection")
                            add_results = predictions['ADD']
                            if add_results['prediction'] == 1:  # AD detected
                                st.error(f"⚠️ **Alzheimer's Detected**: {add_results['probabilities']['AD']:.1%} confidence")
                            else:  # Normal cognition
                                st.success(f"✅ **Normal Cognition**: {add_results['probabilities']['Normal']:.1%} confidence")

                        with col2:
                            st.markdown("#### 🧮 Cognitive Score")
                            cog_score = predictions['COG']['score']
                            st.metric("COG Score", f"{cog_score:.3f}")

                    except Exception as e:
                        st.error(f"❌ Analysis failed: {str(e)}")

            # Brain Region Analysis
            if st.session_state.predictions is not None:
                st.markdown('<h2 class="sub-header">🔍 Brain Region Analysis</h2>', unsafe_allow_html=True)

                if st.button("🧠 Generate Brain Region Analysis"):
                    with st.spinner("Generating brain region analysis..."):
                        try:
                            heatmap_data = generate_heatmap(
                                st.session_state.current_model,
                                st.session_state.preprocessed_data
                            )
                            st.session_state.heatmap_data = heatmap_data
                            st.success("✅ Brain region analysis completed")

                        except Exception as e:
                            st.error(f"❌ Analysis failed: {str(e)}")

                # Display heatmap if available
                if st.session_state.heatmap_data is not None:
                    display_heatmap_analysis(st.session_state.heatmap_data, preprocessed_data)

            # Individual fullscreen options
            st.markdown("### 🖥️ Individual Fullscreen Viewing")

            col1, col2, col3 = st.columns(3)

            with col1:
                axial_slice = st.slider("Axial Slice", 0, preprocessed_data.shape[2]-1,
                                      preprocessed_data.shape[2]//2, key="axial_slice")
                if st.button("🖥️ Axial Fullscreen", key="axial_fullscreen"):
                    display_individual_fullscreen(preprocessed_data, "Axial MRI View",
                                                axial_slice, "axial")

            with col2:
                coronal_slice = st.slider("Coronal Slice", 0, preprocessed_data.shape[1]-1,
                                        preprocessed_data.shape[1]//2, key="coronal_slice")
                if st.button("🖥️ Coronal Fullscreen", key="coronal_fullscreen"):
                    display_individual_fullscreen(preprocessed_data, "Coronal MRI View",
                                                coronal_slice, "coronal")

            with col3:
                sagittal_slice = st.slider("Sagittal Slice", 0, preprocessed_data.shape[0]-1,
                                         preprocessed_data.shape[0]//2, key="sagittal_slice")
                if st.button("🖥️ Sagittal Fullscreen", key="sagittal_fullscreen"):
                    display_individual_fullscreen(preprocessed_data, "Sagittal MRI View",
                                                sagittal_slice, "sagittal")
    
    else:
        st.warning("⚠️ Please load the AI model to begin analysis")

if __name__ == "__main__":
    main()
